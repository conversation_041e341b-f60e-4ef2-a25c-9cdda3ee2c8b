import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(_request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Fetch campaigns
    const campaigns = await prisma.campaign.findMany({
      where: { userId: user.id },
      select: {
        id: true,
        name: true,
        subject: true,
        status: true,
        sentCount: true,
        totalEmails: true,
        createdAt: true,
        scheduledAt: true,
        updatedAt: true
      },
      orderBy: { createdAt: "desc" }
    })

    // Transform the data to match expected format
    const transformedCampaigns = campaigns.map((campaign: any) => ({
      id: campaign.id,
      name: campaign.name,
      subject: campaign.subject,
      status: campaign.status,
      sentCount: campaign.sentCount || 0,
      totalEmails: campaign.totalEmails || 0,
      createdAt: campaign.createdAt.toISOString(),
      scheduledFor: campaign.scheduledAt?.toISOString()
    }))

    return NextResponse.json(transformedCampaigns)
  } catch (error) {
    console.error("Error fetching campaigns:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const { name, subject, content, contactListIds, status, scheduledFor } = await request.json()

    // Validate required fields
    if (!name || !subject || !content) {
      return NextResponse.json({ error: "Name, subject, and content are required" }, { status: 400 })
    }

    if (!contactListIds || contactListIds.length === 0) {
      return NextResponse.json({ error: "At least one contact list must be selected" }, { status: 400 })
    }

    // Verify contact lists belong to user and get total contacts
    const contactLists = await prisma.contactList.findMany({
      where: {
        id: { in: contactListIds },
        userId: user.id
      },
      include: {
        _count: {
          select: { contacts: true }
        }
      }
    })

    if (contactLists.length !== contactListIds.length) {
      return NextResponse.json({ error: "One or more contact lists not found" }, { status: 404 })
    }

    // Calculate total emails
    const totalEmails = contactLists.reduce((total: number, list: any) => total + list._count.contacts, 0)

    if (totalEmails === 0) {
      return NextResponse.json({ error: "Selected contact lists have no contacts" }, { status: 400 })
    }

    // Create campaign
    const campaign = await prisma.campaign.create({
      data: {
        userId: user.id,
        name,
        subject,
        content,
        status: status || 'DRAFT',
        totalEmails,
        sentCount: 0,
        failedCount: 0,
        scheduledAt: scheduledFor ? new Date(scheduledFor) : null,
        contactLists: {
          create: contactListIds.map((listId: string) => ({
            contactListId: listId
          }))
        }
      }
    })

    // If sending immediately, you could trigger the sending process here
    // For now, we'll just create the campaign

    return NextResponse.json({
      id: campaign.id,
      name: campaign.name,
      subject: campaign.subject,
      status: campaign.status,
      totalEmails: campaign.totalEmails,
      sentCount: campaign.sentCount,
      createdAt: campaign.createdAt.toISOString()
    }, { status: 201 })
  } catch (error) {
    console.error("Error creating campaign:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 