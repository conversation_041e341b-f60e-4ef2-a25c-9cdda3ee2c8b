'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  Video, 
  Copy,
  ExternalLink,
  Users,
  Settings,
  Info,
  CheckCircle
} from 'lucide-react'

interface MeetingJoinInterfaceProps {
  meetingSpace: {
    name: string
    meetingUri: string
    meetingCode: string
    displayName?: string
    description?: string
    config?: {
      accessType: string
      entryPointAccess: string
    }
  }
  onClose: () => void
}

export default function MeetingJoinInterface({ meetingSpace, onClose }: MeetingJoinInterfaceProps) {
  const [isJoining, setIsJoining] = useState(false)
  const { toast } = useToast()

  const joinMeeting = async () => {
    setIsJoining(true)
    
    // Open Google Meet in a new tab
    window.open(meetingSpace.meetingUri, '_blank')
    
    toast({
      title: "Opening Google Meet",
      description: "Meeting opened in a new tab. The add-on will be available inside the meeting.",
    })
    
    // Reset after a short delay
    setTimeout(() => setIsJoining(false), 2000)
  }

  const copyMeetingCode = async () => {
    try {
      await navigator.clipboard.writeText(meetingSpace.meetingCode)
      toast({
        title: "Copied!",
        description: "Meeting code copied to clipboard",
      })
    } catch (error) {
      console.error('Failed to copy meeting code:', error)
    }
  }

  const copyMeetingLink = async () => {
    try {
      await navigator.clipboard.writeText(meetingSpace.meetingUri)
      toast({
        title: "Copied!",
        description: "Meeting link copied to clipboard",
      })
    } catch (error) {
      console.error('Failed to copy meeting link:', error)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-[600px] p-6">
      <div className="w-full max-w-2xl space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <Video className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold">Google Meet Session</h1>
          </div>
          <div className="text-3xl font-mono font-bold text-blue-600 bg-blue-50 py-3 px-6 rounded-lg inline-block">
            {meetingSpace.meetingCode}
          </div>
        </div>

        {/* Main Join Card */}
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Users className="h-5 w-5" />
              Ready to Join Meeting
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Join Button */}
            <Button 
              onClick={joinMeeting}
              className="w-full h-14 text-lg"
              size="lg"
              disabled={isJoining}
            >
              <Video className="h-6 w-6 mr-3" />
              {isJoining ? 'Opening Google Meet...' : 'Join Google Meet'}
            </Button>

            {/* Copy Actions */}
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" onClick={copyMeetingCode} className="h-12">
                <Copy className="h-4 w-4 mr-2" />
                Copy Code
              </Button>
              <Button variant="outline" onClick={copyMeetingLink} className="h-12">
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
            </div>

            {/* Meeting Info */}
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">Access Type:</span>
                <Badge variant="outline">{meetingSpace.config?.accessType || 'OPEN'}</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">Entry Access:</span>
                <Badge variant="outline">{meetingSpace.config?.entryPointAccess || 'ALL'}</Badge>
              </div>
              {meetingSpace.displayName && (
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Display Name:</span>
                  <span className="text-muted-foreground">{meetingSpace.displayName}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Add-on Information */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Settings className="h-5 w-5" />
              Enhanced Meeting Experience
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-blue-700">
              This meeting includes our collaborative add-on that provides enhanced features 
              directly within Google Meet.
            </p>
            
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-800">Collaborative Workspace</div>
                  <div className="text-sm text-blue-600">
                    Shared activities visible to all participants
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-800">Real-time Synchronization</div>
                  <div className="text-sm text-blue-600">
                    All interactions sync instantly across participants
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-800">Integrated Experience</div>
                  <div className="text-sm text-blue-600">
                    No need to leave Google Meet interface
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card className="bg-amber-50 border-amber-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-800">
              <Info className="h-5 w-5" />
              How to Access Add-on Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="text-sm text-amber-700 space-y-2">
              <li className="flex items-start gap-2">
                <span className="font-bold">1.</span>
                <span>Click "Join Google Meet" to open the meeting</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="font-bold">2.</span>
                <span>Once in the meeting, look for the meeting tools button (apps icon)</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="font-bold">3.</span>
                <span>Find our add-on in the "Your add-ons" section and click it</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="font-bold">4.</span>
                <span>Use the side panel to launch collaborative activities</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="font-bold">5.</span>
                <span>All participants will see shared activities in the main stage</span>
              </li>
            </ol>
          </CardContent>
        </Card>

        {/* Footer Actions */}
        <div className="flex gap-3 justify-center">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button onClick={joinMeeting} disabled={isJoining}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Join Meeting
          </Button>
        </div>
      </div>
    </div>
  )
}
