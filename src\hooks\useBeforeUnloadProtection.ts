import { useEffect, useRef } from 'react'

export interface BeforeUnloadProtectionOptions {
  enabled?: boolean
  message?: string
  onBeforeUnload?: () => void
}

/**
 * Hook to protect against accidental page navigation when there are unsaved changes
 * @param hasUnsavedChanges - Whether there are unsaved changes
 * @param options - Configuration options
 */
export function useBeforeUnloadProtection(
  hasUnsavedChanges: boolean,
  options: BeforeUnloadProtectionOptions = {}
) {
  const {
    enabled = true,
    message = 'You have unsaved changes. Are you sure you want to leave?',
    onBeforeUnload
  } = options

  const hasUnsavedChangesRef = useRef(hasUnsavedChanges)
  const enabledRef = useRef(enabled)

  // Update refs when values change
  useEffect(() => {
    hasUnsavedChangesRef.current = hasUnsavedChanges
  }, [hasUnsavedChanges])

  useEffect(() => {
    enabledRef.current = enabled
  }, [enabled])

  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Only show warning if protection is enabled and there are unsaved changes
      if (!enabledRef.current || !hasUnsavedChangesRef.current) {
        return
      }

      // Call the callback if provided
      onBeforeUnload?.()

      // Set the return value to show the browser's confirmation dialog
      event.preventDefault()
      event.returnValue = message
      return message
    }

    const handleVisibilityChange = () => {
      // Save draft when page becomes hidden (user switches tabs, minimizes browser, etc.)
      if (document.visibilityState === 'hidden' && hasUnsavedChangesRef.current) {
        onBeforeUnload?.()
      }
    }

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup event listeners
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [message, onBeforeUnload])

  // Return a function to manually trigger the protection check
  const checkUnsavedChanges = () => {
    if (enabled && hasUnsavedChanges) {
      return window.confirm(message)
    }
    return true
  }

  return {
    checkUnsavedChanges
  }
}
