import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentEmailService } from '@/lib/ai-agent-email'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')

    const insights = await aiAgentEmailService.getEmailInsights(session.user.id, days)

    return NextResponse.json({
      insights,
      success: true
    })

  } catch (error) {
    console.error('Email insights error:', error)
    return NextResponse.json(
      { error: 'Failed to get email insights' },
      { status: 500 }
    )
  }
}
