import { useState, useEffect, useCallback } from 'react'

export interface NetworkStatus {
  isOnline: boolean
  isSlowConnection: boolean
  connectionType?: string
  effectiveType?: string
  downlink?: number
  rtt?: number
}

export interface OfflineQueueItem {
  id: string
  type: 'draft_create' | 'draft_update' | 'draft_delete' | 'draft_send'
  data: any
  timestamp: Date
  retryCount: number
  maxRetries: number
}

export function useOfflineDetection() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    isSlowConnection: false
  })
  
  const [offlineQueue, setOfflineQueue] = useState<OfflineQueueItem[]>([])
  const [isProcessingQueue, setIsProcessingQueue] = useState(false)

  // Update network status
  const updateNetworkStatus = useCallback(() => {
    const isOnline = navigator.onLine
    let isSlowConnection = false
    let connectionInfo: any = {}

    // Check for Network Information API support
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connectionInfo = {
        connectionType: connection.type,
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      }

      // Consider connection slow if effective type is 2g or 3g, or if downlink is very low
      isSlowConnection = 
        connection.effectiveType === '2g' || 
        connection.effectiveType === 'slow-2g' ||
        (connection.downlink && connection.downlink < 0.5) ||
        (connection.rtt && connection.rtt > 2000)
    }

    setNetworkStatus({
      isOnline,
      isSlowConnection,
      ...connectionInfo
    })
  }, [])

  // Add item to offline queue
  const addToOfflineQueue = useCallback((item: Omit<OfflineQueueItem, 'id' | 'timestamp' | 'retryCount'>) => {
    const queueItem: OfflineQueueItem = {
      id: `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      retryCount: 0,
      ...item
    }

    setOfflineQueue(prev => [...prev, queueItem])
    return queueItem.id
  }, [])

  // Remove item from offline queue
  const removeFromOfflineQueue = useCallback((id: string) => {
    setOfflineQueue(prev => prev.filter(item => item.id !== id))
  }, [])

  // Process offline queue when coming back online
  const processOfflineQueue = useCallback(async () => {
    if (!networkStatus.isOnline || isProcessingQueue || offlineQueue.length === 0) {
      return
    }

    setIsProcessingQueue(true)

    try {
      const itemsToProcess = [...offlineQueue]
      
      for (const item of itemsToProcess) {
        try {
          let success = false

          switch (item.type) {
            case 'draft_create':
              success = await processDraftCreate(item.data)
              break
            case 'draft_update':
              success = await processDraftUpdate(item.data)
              break
            case 'draft_delete':
              success = await processDraftDelete(item.data)
              break
            case 'draft_send':
              success = await processDraftSend(item.data)
              break
          }

          if (success) {
            removeFromOfflineQueue(item.id)
          } else {
            // Increment retry count
            setOfflineQueue(prev => 
              prev.map(queueItem => 
                queueItem.id === item.id 
                  ? { ...queueItem, retryCount: queueItem.retryCount + 1 }
                  : queueItem
              )
            )

            // Remove if max retries reached
            if (item.retryCount >= item.maxRetries) {
              removeFromOfflineQueue(item.id)
              console.error(`Max retries reached for queue item ${item.id}`)
            }
          }
        } catch (error) {
          console.error(`Error processing queue item ${item.id}:`, error)
          
          // Increment retry count on error
          setOfflineQueue(prev => 
            prev.map(queueItem => 
              queueItem.id === item.id 
                ? { ...queueItem, retryCount: queueItem.retryCount + 1 }
                : queueItem
            )
          )

          // Remove if max retries reached
          if (item.retryCount >= item.maxRetries) {
            removeFromOfflineQueue(item.id)
          }
        }
      }
    } finally {
      setIsProcessingQueue(false)
    }
  }, [networkStatus.isOnline, isProcessingQueue, offlineQueue, removeFromOfflineQueue])

  // Process draft operations
  const processDraftCreate = async (data: any): Promise<boolean> => {
    try {
      const response = await fetch('/api/gmail/drafts/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      return response.ok
    } catch {
      return false
    }
  }

  const processDraftUpdate = async (data: any): Promise<boolean> => {
    try {
      const response = await fetch('/api/gmail/drafts/update', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      return response.ok
    } catch {
      return false
    }
  }

  const processDraftDelete = async (data: any): Promise<boolean> => {
    try {
      const response = await fetch('/api/gmail/drafts/delete', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      return response.ok
    } catch {
      return false
    }
  }

  const processDraftSend = async (data: any): Promise<boolean> => {
    try {
      const response = await fetch('/api/gmail/drafts/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      return response.ok
    } catch {
      return false
    }
  }

  // Clear offline queue
  const clearOfflineQueue = useCallback(() => {
    setOfflineQueue([])
  }, [])

  // Get queue statistics
  const getQueueStats = useCallback(() => {
    return {
      totalItems: offlineQueue.length,
      itemsByType: offlineQueue.reduce((acc, item) => {
        acc[item.type] = (acc[item.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      oldestItem: offlineQueue.length > 0 
        ? Math.min(...offlineQueue.map(item => item.timestamp.getTime()))
        : null
    }
  }, [offlineQueue])

  // Set up event listeners
  useEffect(() => {
    if (typeof window === 'undefined') return

    updateNetworkStatus()

    const handleOnline = () => {
      updateNetworkStatus()
      // Process queue when coming back online
      setTimeout(processOfflineQueue, 1000)
    }

    const handleOffline = () => {
      updateNetworkStatus()
    }

    const handleConnectionChange = () => {
      updateNetworkStatus()
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Listen for connection changes if supported
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connection.addEventListener('change', handleConnectionChange)
      
      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
        connection.removeEventListener('change', handleConnectionChange)
      }
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [updateNetworkStatus, processOfflineQueue])

  // Auto-process queue when network status changes to online
  useEffect(() => {
    if (networkStatus.isOnline && !isProcessingQueue && offlineQueue.length > 0) {
      const timer = setTimeout(processOfflineQueue, 2000)
      return () => clearTimeout(timer)
    }
  }, [networkStatus.isOnline, isProcessingQueue, offlineQueue.length, processOfflineQueue])

  return {
    networkStatus,
    offlineQueue,
    isProcessingQueue,
    addToOfflineQueue,
    removeFromOfflineQueue,
    processOfflineQueue,
    clearOfflineQueue,
    getQueueStats
  }
}
