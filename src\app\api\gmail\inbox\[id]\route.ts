import { NextRequest } from "next/server"
import { 
  getEmailDetails,
  markEmailAsRead,
  markEmailAsUnread,
  deleteEmail,
  archiveEmail,
  starEmail,
  addEmailLabel,
  removeEmailLabel,
  withGmailAuth,
  validateRequiredFields,
  formatEmailOperationResult
} from "@/lib/gmail"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withGmail<PERSON>uth(request, async ({ user }) => {
    const { id: messageId } = await params

    // Fetch email details from Gmail
    const email = await getEmailDetails(user.id, messageId)

    if (!email) {
      throw new Error("Email not found")
    }

    return { email }
  })
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withGmail<PERSON>uth(request, async ({ user }) => {
    const { id: messageId } = await params
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['action'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { action, value, labelId } = body

    let success = false
    let message = ""

    switch (action) {
      case 'markAsRead':
        success = await markEmailAsRead(user.id, messageId)
        message = "Email marked as read"
        break
      
      case 'markAsUnread':
        success = await markEmailAsUnread(user.id, messageId)
        message = "Email marked as unread"
        break
      
      case 'delete':
        success = await deleteEmail(user.id, messageId)
        message = "Email deleted"
        break
      
      case 'archive':
        success = await archiveEmail(user.id, messageId)
        message = "Email archived"
        break
      
      case 'star':
        success = await starEmail(user.id, messageId, value === true)
        message = value === true ? "Email starred" : "Email unstarred"
        break
      
      case 'addLabel':
        if (!labelId) {
          throw new Error("Label ID is required for addLabel action")
        }
        success = await addEmailLabel(user.id, messageId, labelId)
        message = "Label added to email"
        break
      
      case 'removeLabel':
        if (!labelId) {
          throw new Error("Label ID is required for removeLabel action")
        }
        success = await removeEmailLabel(user.id, messageId, labelId)
        message = "Label removed from email"
        break
      
      default:
        throw new Error(`Invalid action: ${action}`)
    }

    return formatEmailOperationResult(success, message)
  })
} 