import { 
  listRecordings as apiListRecordings,
  listTranscripts as apiListTranscripts,
  makeMeetApiRequest
} from './client'
import { Recording, Transcript, ListResponse } from './types'

/**
 * Get a specific recording
 */
export async function getRecording(
  userId: string,
  recordingName: string
): Promise<Recording> {
  const response = await makeMeetApiRequest(userId, `conferenceRecords/${recordingName}`)
  return await response.json()
}

/**
 * List recordings for a conference
 */
export async function listRecordings(
  userId: string,
  conferenceRecordName: string,
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<Recording>> {
  const response = await apiListRecordings(userId, conferenceRecordName, options)
  
  return {
    items: (response.recordings || []) as Recording[],
    nextPageToken: response.nextPageToken,
    totalSize: response.recordings?.length
  }
}

/**
 * Get recording download information
 */
export async function getRecordingDownloadInfo(
  userId: string,
  recordingName: string
): Promise<{
  downloadUrl: string
  expiresAt: string
  fileSize?: number
  format?: string
}> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning placeholder data
  return {
    downloadUrl: '',
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
    fileSize: 0,
    format: 'mp4'
  }
}

/**
 * Get a specific transcript
 */
export async function getTranscript(
  userId: string,
  transcriptName: string
): Promise<Transcript> {
  const response = await makeMeetApiRequest(userId, `conferenceRecords/${transcriptName}`)
  return await response.json()
}

/**
 * List transcripts for a conference
 */
export async function listTranscripts(
  userId: string,
  conferenceRecordName: string,
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<Transcript>> {
  const response = await apiListTranscripts(userId, conferenceRecordName, options)
  
  return {
    items: (response.transcripts || []) as Transcript[],
    nextPageToken: response.nextPageToken,
    totalSize: response.transcripts?.length
  }
}

/**
 * Get transcript entries (individual transcript segments)
 */
export async function getTranscriptEntries(
  userId: string,
  transcriptName: string,
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<any>> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning empty results
  return {
    items: [],
    nextPageToken: undefined,
    totalSize: 0
  }
}

/**
 * Search transcript content
 */
export async function searchTranscriptContent(
  userId: string,
  transcriptName: string,
  searchQuery: string,
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<{
  matches: Array<{
    text: string
    startTime: string
    endTime: string
    speaker?: string
    confidence?: number
  }>
  totalMatches: number
  nextPageToken?: string
}> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning empty results
  return {
    matches: [],
    totalMatches: 0,
    nextPageToken: undefined
  }
}

/**
 * Get meeting artifacts overview
 */
export async function getMeetingArtifacts(
  userId: string,
  conferenceRecordName: string
): Promise<{
  recordings: Recording[]
  transcripts: Transcript[]
  hasRecordings: boolean
  hasTranscripts: boolean
  totalRecordings: number
  totalTranscripts: number
}> {
  try {
    const [recordingsResponse, transcriptsResponse] = await Promise.all([
      listRecordings(userId, conferenceRecordName, { pageSize: 50 }),
      listTranscripts(userId, conferenceRecordName, { pageSize: 50 })
    ])

    return {
      recordings: recordingsResponse.items,
      transcripts: transcriptsResponse.items,
      hasRecordings: recordingsResponse.items.length > 0,
      hasTranscripts: transcriptsResponse.items.length > 0,
      totalRecordings: recordingsResponse.totalSize || 0,
      totalTranscripts: transcriptsResponse.totalSize || 0
    }
  } catch (error) {
    console.error('Error getting meeting artifacts:', error)
    return {
      recordings: [],
      transcripts: [],
      hasRecordings: false,
      hasTranscripts: false,
      totalRecordings: 0,
      totalTranscripts: 0
    }
  }
}

/**
 * Get artifact statistics
 */
export async function getArtifactStatistics(
  userId: string,
  conferenceRecordName: string
): Promise<{
  recordingStats: {
    totalRecordings: number
    totalDuration: number // in minutes
    totalFileSize: number // in bytes
    formats: { [format: string]: number }
  }
  transcriptStats: {
    totalTranscripts: number
    totalWords: number
    totalSpeakers: number
    languages: { [language: string]: number }
  }
}> {
  const artifacts = await getMeetingArtifacts(userId, conferenceRecordName)
  
  // Simplified statistics calculation
  return {
    recordingStats: {
      totalRecordings: artifacts.totalRecordings,
      totalDuration: 0, // Would calculate from actual data
      totalFileSize: 0, // Would calculate from actual data
      formats: { 'mp4': artifacts.totalRecordings }
    },
    transcriptStats: {
      totalTranscripts: artifacts.totalTranscripts,
      totalWords: 0, // Would calculate from actual data
      totalSpeakers: 0, // Would calculate from actual data
      languages: { 'en': artifacts.totalTranscripts }
    }
  }
}

/**
 * Get artifacts summary for a meeting
 */
export async function getArtifactsSummary(
  userId: string,
  conferenceRecordName: string
): Promise<{
  hasArtifacts: boolean
  recordingCount: number
  transcriptCount: number
  totalDuration: number
  lastUpdated: string
  summary: string
}> {
  const artifacts = await getMeetingArtifacts(userId, conferenceRecordName)
  
  return {
    hasArtifacts: artifacts.hasRecordings || artifacts.hasTranscripts,
    recordingCount: artifacts.totalRecordings,
    transcriptCount: artifacts.totalTranscripts,
    totalDuration: 0, // Would calculate from actual data
    lastUpdated: new Date().toISOString(),
    summary: `Meeting has ${artifacts.totalRecordings} recording(s) and ${artifacts.totalTranscripts} transcript(s)`
  }
}

/**
 * Get conference artifacts (alias for getMeetingArtifacts)
 */
export async function getConferenceArtifacts(
  userId: string,
  conferenceRecordName: string
): Promise<{
  recordings: Recording[]
  transcripts: Transcript[]
  hasRecordings: boolean
  hasTranscripts: boolean
  totalRecordings: number
  totalTranscripts: number
}> {
  return await getMeetingArtifacts(userId, conferenceRecordName)
}

/**
 * Get full transcript text as a single string
 */
export async function getFullTranscriptText(
  userId: string,
  transcriptName: string
): Promise<{
  fullText: string
  speakers: string[]
  duration: number
  wordCount: number
  language: string
}> {
  try {
    const transcript = await getTranscript(userId, transcriptName)
    const entries = await getTranscriptEntries(userId, transcriptName, { pageSize: 1000 })
    
    // Combine all transcript entries into full text
    const fullText = entries.items.map((entry: any) => entry.text || '').join(' ')
    
    return {
      fullText,
      speakers: [], // Would extract from entries
      duration: 0, // Would calculate from entries
      wordCount: fullText.split(' ').length,
      language: 'en' // Default language, would be extracted from entries
    }
  } catch (error) {
    console.error('Error getting full transcript text:', error)
    return {
      fullText: '',
      speakers: [],
      duration: 0,
      wordCount: 0,
      language: 'en'
    }
  }
}

/**
 * Get transcript download information
 */
export async function getTranscriptDownloadInfo(
  userId: string,
  transcriptName: string
): Promise<{
  downloadUrl: string
  expiresAt: string
  fileSize?: number
  format?: string
  language?: string
}> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning placeholder data
  return {
    downloadUrl: '',
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
    fileSize: 0,
    format: 'txt',
    language: 'en'
  }
}

/**
 * Get a specific transcript entry
 */
export async function getTranscriptEntry(
  userId: string,
  transcriptEntryName: string
): Promise<{
  name: string
  text: string
  startTime: string
  endTime: string
  speaker?: string
  confidence?: number
  language?: string
}> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning placeholder data
  return {
    name: transcriptEntryName,
    text: '',
    startTime: new Date().toISOString(),
    endTime: new Date().toISOString(),
    speaker: 'Unknown',
    confidence: 0.95,
    language: 'en'
  }
}

/**
 * List transcript entries with pagination
 */
export async function listTranscriptEntries(
  userId: string,
  transcriptName: string,
  options: {
    pageSize?: number
    pageToken?: string
    startTime?: string
    endTime?: string
  } = {}
): Promise<ListResponse<any>> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning empty results
  return {
    items: [],
    nextPageToken: undefined,
    totalSize: 0
  }
}

/**
 * Search transcript entries with specific criteria
 */
export async function searchTranscriptEntries(
  userId: string,
  transcriptName: string,
  searchQuery: string,
  options: {
    pageSize?: number
    pageToken?: string
    speaker?: string
    startTime?: string
    endTime?: string
  } = {}
): Promise<{
  matches: Array<{
    entryName: string
    text: string
    startTime: string
    endTime: string
    speaker?: string
    confidence?: number
    highlightedText?: string
  }>
  totalMatches: number
  nextPageToken?: string
  searchQuery: string
}> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning empty results
  return {
    matches: [],
    totalMatches: 0,
    nextPageToken: undefined,
    searchQuery
  }
}
