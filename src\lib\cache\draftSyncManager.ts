// Background sync manager for draft synchronization between cache and Gmail API

import { draftCache, CachedDraft } from './draftCache'
import { DraftData } from '../gmail/types'

export interface SyncResult {
  success: boolean
  draftId?: string
  gmailDraftId?: string
  error?: string
}

export interface SyncStats {
  totalProcessed: number
  successful: number
  failed: number
  errors: string[]
}

class DraftSyncManager {
  private syncInProgress = false
  private syncQueue: Set<string> = new Set()
  private retryAttempts = new Map<string, number>()
  private maxRetries = 3
  private syncInterval: NodeJS.Timeout | null = null
  private isOnline = true

  constructor() {
    // Monitor online/offline status
    if (typeof window !== 'undefined') {
      this.isOnline = navigator.onLine
      window.addEventListener('online', () => {
        this.isOnline = true
        // Disabled automatic sync startup
        // this.startPeriodicSync()
      })
      window.addEventListener('offline', () => {
        this.isOnline = false
        this.stopPeriodicSync()
      })
    }
  }

  startPeriodicSync(intervalMs = 30000): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
    }

    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.syncPendingDrafts()
      }
    }, intervalMs)
  }

  stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
    }
  }

  async queueDraftForSync(draftId: string): Promise<void> {
    // Disabled to avoid API calls
    console.log('queueDraftForSync called but disabled:', draftId)
    // this.syncQueue.add(draftId)

    // // Trigger immediate sync if online and not already syncing
    // if (this.isOnline && !this.syncInProgress) {
    //   setTimeout(() => this.syncPendingDrafts(), 100)
    // }
  }

  async syncPendingDrafts(userId?: string): Promise<SyncStats> {
    // Disabled to avoid API calls
    console.log('syncPendingDrafts called but disabled:', userId)
    return { totalProcessed: 0, successful: 0, failed: 0, errors: [] }
  }

  private async getAllPendingSyncDrafts(): Promise<CachedDraft[]> {
    // This would need to be implemented to get all users' pending drafts
    // For now, we'll assume we have the current user's ID
    const userId = await this.getCurrentUserId()
    return userId ? await draftCache.getPendingSyncDrafts(userId) : []
  }

  private async getCurrentUserId(): Promise<string | null> {
    // Disabled for now - using existing auth patterns instead
    console.log('getCurrentUserId called but disabled')
    return null
  }

  private async syncSingleDraft(draft: CachedDraft): Promise<SyncResult> {
    try {
      if (draft.isDeleted) {
        return await this.syncDeletedDraft(draft)
      } else if (draft.gmailDraftId) {
        return await this.syncUpdatedDraft(draft)
      } else {
        return await this.syncNewDraft(draft)
      }
    } catch (error) {
      return {
        success: false,
        draftId: draft.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  private async syncNewDraft(draft: CachedDraft): Promise<SyncResult> {
    const draftData: DraftData = {
      to: draft.to,
      cc: draft.cc,
      bcc: draft.bcc,
      subject: draft.subject,
      htmlBody: draft.htmlBody,
      textBody: draft.textBody,
      threadId: draft.threadId,
      replyToMessageId: draft.replyToMessageId,
      messageId: draft.messageId,
      references: draft.references,
      inReplyTo: draft.inReplyTo,
      attachments: draft.attachments
    }

    const response = await fetch('/api/gmail/drafts/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(draftData)
    })

    if (!response.ok) {
      throw new Error(`Failed to create draft: ${response.statusText}`)
    }

    const result = await response.json()
    if (result.success && result.draftId) {
      await draftCache.markDraftSynced(draft.id, result.draftId)
      return {
        success: true,
        draftId: draft.id,
        gmailDraftId: result.draftId
      }
    } else {
      throw new Error(result.error || 'Failed to create draft')
    }
  }

  private async syncUpdatedDraft(draft: CachedDraft): Promise<SyncResult> {
    const draftData: DraftData = {
      to: draft.to,
      cc: draft.cc,
      bcc: draft.bcc,
      subject: draft.subject,
      htmlBody: draft.htmlBody,
      textBody: draft.textBody,
      threadId: draft.threadId,
      replyToMessageId: draft.replyToMessageId,
      messageId: draft.messageId,
      references: draft.references,
      inReplyTo: draft.inReplyTo,
      attachments: draft.attachments
    }

    const response = await fetch('/api/gmail/drafts/update', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        draftId: draft.gmailDraftId,
        ...draftData
      })
    })

    if (!response.ok) {
      throw new Error(`Failed to update draft: ${response.statusText}`)
    }

    const result = await response.json()
    if (result.success) {
      await draftCache.markDraftSynced(draft.id, draft.gmailDraftId!)
      return {
        success: true,
        draftId: draft.id,
        gmailDraftId: draft.gmailDraftId
      }
    } else {
      throw new Error(result.error || 'Failed to update draft')
    }
  }

  private async syncDeletedDraft(draft: CachedDraft): Promise<SyncResult> {
    if (!draft.gmailDraftId) {
      // Draft was never synced to Gmail, just remove from cache
      await draftCache.purgeDraft(draft.id)
      return {
        success: true,
        draftId: draft.id
      }
    }

    const response = await fetch('/api/gmail/drafts/delete', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ draftId: draft.gmailDraftId })
    })

    if (!response.ok) {
      throw new Error(`Failed to delete draft: ${response.statusText}`)
    }

    const result = await response.json()
    if (result.success) {
      await draftCache.purgeDraft(draft.id)
      return {
        success: true,
        draftId: draft.id,
        gmailDraftId: draft.gmailDraftId
      }
    } else {
      throw new Error(result.error || 'Failed to delete draft')
    }
  }

  private async handleSyncError(draft: CachedDraft, error: string): Promise<void> {
    const attempts = this.retryAttempts.get(draft.id) || 0
    
    if (attempts < this.maxRetries) {
      this.retryAttempts.set(draft.id, attempts + 1)
      this.syncQueue.add(draft.id)
    } else {
      // Max retries reached, mark as error
      await draftCache.markDraftSyncError(draft.id, error)
      this.retryAttempts.delete(draft.id)
      this.syncQueue.delete(draft.id)
    }
  }

  async forceSyncDraft(draftId: string): Promise<SyncResult> {
    const draft = await draftCache.getDraft(draftId)
    if (!draft) {
      return {
        success: false,
        draftId,
        error: 'Draft not found in cache'
      }
    }

    return await this.syncSingleDraft(draft)
  }

  getSyncStatus(): {
    isOnline: boolean
    syncInProgress: boolean
    queueSize: number
  } {
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      queueSize: this.syncQueue.size
    }
  }

  async retrySyncErrors(userId: string): Promise<SyncStats> {
    // Disabled to avoid API calls
    console.log('retrySyncErrors called but disabled:', userId)
    return { totalProcessed: 0, successful: 0, failed: 0, errors: [] }
  }
}

// Singleton instance
export const draftSyncManager = new DraftSyncManager()

// Auto-start periodic sync - DISABLED to avoid API calls
// if (typeof window !== 'undefined') {
//   draftSyncManager.startPeriodicSync()
// }
