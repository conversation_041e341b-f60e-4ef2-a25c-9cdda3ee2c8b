// Google Meet API v2 Types

// Base types
export interface ListResponse<T> {
  items: T[]
  nextPageToken?: string
  totalSize?: number
}

// Meeting Space types
export interface Space {
  name: string
  meetingUri: string
  meetingCode: string
  config?: SpaceConfig
  activeConference?: {
    conferenceRecord: string
  }
  createTime?: string
  updateTime?: string
}

export interface SpaceConfig {
  accessType?: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
  entryPointAccess?: 'ALL' | 'CREATOR_APP_ONLY'
  displayName?: string
  description?: string
}

// Legacy compatibility
export interface MeetingSpace extends Space {}

export interface CreateSpaceRequest {
  config?: SpaceConfig
}

// Conference types
export interface ConferenceRecord {
  name: string
  startTime: string
  endTime?: string
  expireTime: string
  space: string
}

export interface ConferenceFilter {
  startTime?: string
  endTime?: string
  space?: string
}

// Participant types
export interface Participant {
  name: string
  earliestStartTime?: string
  latestEndTime?: string
  signalingId?: string
  phoneNumber?: string
  anonymousUser?: {
    displayName: string
  }
  signedinUser?: {
    user: string
    displayName: string
  }
}

export interface ParticipantSession {
  name: string
  startTime: string
  endTime?: string
  participant: string
}

export interface ParticipantFilter {
  conferenceRecord: string
}

// Recording types
export interface Recording {
  name: string
  driveDestination: {
    file: string
    exportUri: string
  }
  state: 'STATE_UNSPECIFIED' | 'STARTED' | 'ENDED' | 'FILE_GENERATED'
  startTime: string
  endTime?: string
}

export interface RecordingFilter {
  conferenceRecord: string
}

// Transcript types
export interface Transcript {
  name: string
  state: 'STATE_UNSPECIFIED' | 'STARTED' | 'ENDED' | 'FILE_GENERATED'
  startTime: string
  endTime?: string
  docsDestination: {
    document: string
    exportUri: string
  }
}

export interface TranscriptEntry {
  name: string
  participant: string
  text: string
  languageCode: string
  startTime: string
  endTime: string
}

export interface TranscriptFilter {
  conferenceRecord: string
}

// API Context types
export interface MeetApiContext {
  user: {
    id: string
    email: string
    meetRefreshToken: string | null
    meetConnected: boolean
  }
  url: URL
}

// Analytics types
export interface MeetingAnalytics {
  totalMeetings: number
  totalParticipants: number
  totalDuration: number // in minutes
  averageDuration: number
  recordingsCount: number
  transcriptsCount: number
  thisWeekMeetings: number
  thisMonthMeetings: number
}

export interface MeetingInsight {
  meetingId: string
  title: string
  duration: number
  participantCount: number
  hasRecording: boolean
  hasTranscript: boolean
  startTime: string
  endTime?: string
  keyTopics?: string[]
  sentiment?: 'positive' | 'neutral' | 'negative'
}

// Event subscription types
export interface MeetEvent {
  eventType: string
  eventTime: string
  meetingSpace?: string
  conferenceRecord?: string
  participant?: string
}

export interface EventSubscription {
  name: string
  targetResource: string
  eventTypes: string[]
  payloadOptions?: {
    includeResource?: boolean
    fieldMask?: string
  }
  notificationEndpoint: {
    pubsubTopic: string
  }
  state: 'STATE_UNSPECIFIED' | 'ACTIVE' | 'SUSPENDED' | 'DELETED'
  createTime: string
  updateTime: string
  expireTime: string
}

// Error types
export interface MeetApiError {
  code: number
  message: string
  details?: any[]
  status?: string
}
