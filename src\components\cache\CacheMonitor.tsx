"use client"

import React, { useState, useEffect } from 'react'
import { useUnifiedCache } from '@/contexts/cache'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Database, 
  Trash2, 
  RefreshCw, 
  BarChart3, 
  Clock, 
  HardDrive,
  Zap,
  Mail,
  Calendar,
  Video,
  Bot
} from 'lucide-react'

interface CacheMonitorProps {
  showControls?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
}

export function CacheMonitor({ 
  showControls = true, 
  autoRefresh = true, 
  refreshInterval = 5000 
}: CacheMonitorProps) {
  const cache = useUnifiedCache()
  const [stats, setStats] = useState(cache.getStats())
  const [namespaceSizes, setNamespaceSizes] = useState({
    email: 0,
    calendar: 0,
    meet: 0,
    ai: 0
  })

  // Auto-refresh stats
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      updateStats()
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval])

  const updateStats = () => {
    setStats(cache.getStats())
    setNamespaceSizes({
      email: cache.getNamespaceSize('email'),
      calendar: cache.getNamespaceSize('calendar'),
      meet: cache.getNamespaceSize('meet'),
      ai: cache.getNamespaceSize('ai')
    })
  }

  const handleCleanup = () => {
    const cleaned = cache.cleanup()
    updateStats()
    console.log(`🧹 Cleaned up ${cleaned} expired entries`)
  }

  const handleClearNamespace = (namespace: 'email' | 'calendar' | 'meet' | 'ai') => {
    cache.clear(namespace)
    updateStats()
    console.log(`🗑️ Cleared ${namespace} cache`)
  }

  const handleClearAll = () => {
    cache.clear()
    updateStats()
    console.log('🗑️ Cleared entire cache')
  }

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getHitRate = (): number => {
    const total = stats.hitRate + stats.missRate
    return total > 0 ? (stats.hitRate / total) * 100 : 0
  }

  const getNamespaceIcon = (namespace: string) => {
    switch (namespace) {
      case 'email': return <Mail className="h-4 w-4" />
      case 'calendar': return <Calendar className="h-4 w-4" />
      case 'meet': return <Video className="h-4 w-4" />
      case 'ai': return <Bot className="h-4 w-4" />
      default: return <Database className="h-4 w-4" />
    }
  }

  const getNamespaceColor = (namespace: string) => {
    switch (namespace) {
      case 'email': return 'bg-blue-500'
      case 'calendar': return 'bg-green-500'
      case 'meet': return 'bg-purple-500'
      case 'ai': return 'bg-orange-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className="space-y-6">
      {/* Overall Cache Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Cache Statistics
          </CardTitle>
          <CardDescription>
            Real-time cache performance and usage metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Total Entries</span>
              </div>
              <div className="text-2xl font-bold">{stats.totalEntries}</div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <HardDrive className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Memory Usage</span>
              </div>
              <div className="text-2xl font-bold">{formatBytes(stats.memoryUsage)}</div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">Hit Rate</span>
              </div>
              <div className="text-2xl font-bold">{getHitRate().toFixed(1)}%</div>
              <Progress value={getHitRate()} className="h-2" />
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">Last Cleanup</span>
              </div>
              <div className="text-sm text-gray-600">
                {new Date(stats.lastCleanup).toLocaleTimeString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Namespace Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Cache Namespaces</CardTitle>
          <CardDescription>
            Data distribution across different application domains
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(namespaceSizes).map(([namespace, size]) => (
              <div key={namespace} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getNamespaceIcon(namespace)}
                    <span className="text-sm font-medium capitalize">{namespace}</span>
                  </div>
                  <Badge variant="secondary">{size}</Badge>
                </div>
                
                <div className="space-y-2">
                  <div className={`h-2 rounded-full ${getNamespaceColor(namespace)} opacity-20`}>
                    <div 
                      className={`h-full rounded-full ${getNamespaceColor(namespace)}`}
                      style={{ 
                        width: `${stats.totalEntries > 0 ? (size / stats.totalEntries) * 100 : 0}%` 
                      }}
                    />
                  </div>
                  
                  {showControls && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleClearNamespace(namespace as any)}
                      className="w-full"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Clear
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Cache Controls */}
      {showControls && (
        <Card>
          <CardHeader>
            <CardTitle>Cache Management</CardTitle>
            <CardDescription>
              Tools for managing and optimizing cache performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button onClick={updateStats} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Stats
              </Button>
              
              <Button onClick={handleCleanup} variant="outline">
                <Clock className="h-4 w-4 mr-2" />
                Cleanup Expired
              </Button>
              
              <Button onClick={handleClearAll} variant="destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All Cache
              </Button>

              <Button 
                onClick={() => cache.preloadAllData()} 
                variant="outline"
                disabled={cache.isPreloading}
              >
                <Database className="h-4 w-4 mr-2" />
                {cache.isPreloading ? 'Preloading...' : 'Preload Data'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>
            Cache efficiency and performance indicators
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Cache Hits</span>
              <Badge variant="outline" className="text-green-600">
                {stats.hitRate}
              </Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Cache Misses</span>
              <Badge variant="outline" className="text-red-600">
                {stats.missRate}
              </Badge>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Hit Ratio</span>
              <Badge variant="outline" className="text-blue-600">
                {getHitRate().toFixed(2)}%
              </Badge>
            </div>
            
            <div className="pt-2">
              <div className="text-sm text-gray-600 mb-2">Cache Efficiency</div>
              <Progress value={getHitRate()} className="h-3" />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Poor</span>
                <span>Good</span>
                <span>Excellent</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Simplified version for dashboard widgets
export function CacheStatusWidget() {
  const cache = useUnifiedCache()
  const [stats, setStats] = useState(cache.getStats())

  useEffect(() => {
    const interval = setInterval(() => {
      setStats(cache.getStats())
    }, 10000) // Update every 10 seconds

    return () => clearInterval(interval)
  }, [])

  const hitRate = stats.hitRate + stats.missRate > 0 
    ? (stats.hitRate / (stats.hitRate + stats.missRate)) * 100 
    : 0

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Database className="h-4 w-4" />
          Cache Status
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="text-2xl font-bold">{stats.totalEntries}</div>
            <div className="text-xs text-gray-500">entries cached</div>
          </div>
          <div className="text-right space-y-1">
            <div className="text-lg font-semibold text-green-600">
              {hitRate.toFixed(0)}%
            </div>
            <div className="text-xs text-gray-500">hit rate</div>
          </div>
        </div>
        <Progress value={hitRate} className="h-1 mt-3" />
      </CardContent>
    </Card>
  )
}
