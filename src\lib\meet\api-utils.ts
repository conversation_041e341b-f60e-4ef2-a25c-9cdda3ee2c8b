import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { MeetApiContext } from './types'

export interface AuthenticatedMeetUser {
  id: string
  email: string
  meetRefreshToken: string | null
  meetConnected: boolean
}

/**
 * Standard authentication and Meet connection check for all routes
 */
export async function authenticate<PERSON><PERSON><PERSON><PERSON><PERSON>(request: NextRequest): Promise<{
  success: true
  data: MeetApiContext
} | {
  success: false
  response: NextResponse
}> {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return {
        success: false,
        response: NextResponse.json({ error: "Unauthorized" }, { status: 401 })
      }
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        meetRefreshToken: true,
        meetConnected: true
      }
    })

    if (!user) {
      return {
        success: false,
        response: NextResponse.json({ error: "User not found" }, { status: 404 })
      }
    }

    if (!user.meetConnected || !user.meetRefreshToken) {
      return {
        success: false,
        response: NextResponse.json({ 
          error: "Google Meet not connected. Please connect your Google Meet account first.",
          needsConnection: true
        }, { status: 403 })
      }
    }

    return {
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          meetRefreshToken: user.meetRefreshToken,
          meetConnected: user.meetConnected
        },
        url: new URL(request.url)
      }
    }
  } catch (error) {
    console.error('Meet authentication error:', error)
    return {
      success: false,
      response: NextResponse.json({ 
        error: "Authentication failed" 
      }, { status: 500 })
    }
  }
}

/**
 * Format error messages for consistent API responses
 */
export function formatMeetErrorMessage(error: any): string {
  if (error?.code === 403) {
    return "Insufficient permissions. Please reconnect your Google Meet account."
  }
  
  if (error?.code === 404) {
    return "Resource not found."
  }
  
  if (error?.code === 429) {
    return "Rate limit exceeded. Please try again later."
  }
  
  if (error?.message?.includes('insufficient authentication scopes')) {
    return "Meet permissions need to be updated. Please reconnect your Google account."
  }
  
  if (error?.message?.includes('invalid_grant')) {
    return "Authentication expired. Please reconnect your Google Meet account."
  }
  
  return error?.message || "An unexpected error occurred"
}

// Import consolidated error handling
import { createApiErrorResponse } from '@/lib/validation/utils'

/**
 * Handle Meet API errors consistently
 * @deprecated Use createApiErrorResponse from validation/utils instead
 */
export function handleMeetApiError(error: any): NextResponse {
  const { response } = createApiErrorResponse(error, {
    operation: 'Google Meet API',
    includeReconnectionFlag: true
  })

  return new NextResponse(response.body, {
    status: response.status,
    headers: response.headers
  })
}

/**
 * Validate pagination parameters
 */
export function validatePaginationParams(searchParams: URLSearchParams): {
  pageSize: number
  pageToken?: string
} {
  const pageSize = Math.min(
    Math.max(parseInt(searchParams.get('pageSize') || '10'), 1),
    100
  )
  
  const pageToken = searchParams.get('pageToken') || undefined
  
  return { pageSize, pageToken }
}

/**
 * Validate date range parameters
 */
export function validateDateRange(searchParams: URLSearchParams): {
  startTime?: string
  endTime?: string
} {
  const startTime = searchParams.get('startTime') || undefined
  const endTime = searchParams.get('endTime') || undefined
  
  // Validate date formats if provided
  if (startTime && isNaN(Date.parse(startTime))) {
    throw new Error('Invalid startTime format. Use ISO 8601 format.')
  }
  
  if (endTime && isNaN(Date.parse(endTime))) {
    throw new Error('Invalid endTime format. Use ISO 8601 format.')
  }
  
  // Ensure startTime is before endTime if both provided
  if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
    throw new Error('startTime must be before endTime')
  }
  
  return { startTime, endTime }
}

/**
 * Build filter object for API requests
 */
export function buildMeetFilter(searchParams: URLSearchParams): Record<string, any> {
  const filter: Record<string, any> = {}
  
  const { startTime, endTime } = validateDateRange(searchParams)
  if (startTime) filter.startTime = startTime
  if (endTime) filter.endTime = endTime
  
  const space = searchParams.get('space')
  if (space) filter.space = space
  
  return filter
}

/**
 * Format API response with consistent structure
 */
export function formatMeetApiResponse<T>(
  data: T,
  message?: string,
  metadata?: Record<string, any>
): NextResponse {
  return NextResponse.json({
    success: true,
    data,
    message,
    ...metadata
  })
}
