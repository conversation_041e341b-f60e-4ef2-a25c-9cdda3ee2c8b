import { NextRequest } from "next/server"
import { 
  restoreEmail, 
  with<PERSON><PERSON><PERSON><PERSON>, 
  validateRequiredFields,
  formatEmailOperationResult 
} from "@/lib/gmail"

export async function POST(request: NextRequest) {
  return with<PERSON><PERSON><PERSON><PERSON>(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['emailId'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { emailId } = body

    // Restore the email from trash
    const success = await restoreEmail(user.id, emailId)

    return formatEmailOperationResult(success, "Email restored successfully")
  })
}
