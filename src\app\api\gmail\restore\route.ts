import { NextRequest } from "next/server"
import {
  restoreEmail,
  with<PERSON><PERSON><PERSON><PERSON>,
  formatEmail<PERSON>perationR<PERSON>ult
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { emailRestoreSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function POST(request: NextRequest) {
  return withGmail<PERSON>uth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-email-restore', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, emailRestoreSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-email-restore')
      throw new Error(`Invalid email restore data: ${validation.error}`)
    }

    const { emailId } = validation.data

    // Restore the email from trash
    const success = await restoreEmail(user.id, emailId)

    // End performance monitoring
    endPerformanceMetric('gmail-email-restore')

    return formatEmailOperationResult(success, "Email restored successfully")
  })
}
