import { NextRequest } from "next/server"
import { 
  deleteDraft, 
  with<PERSON><PERSON><PERSON><PERSON>, 
  validateRequiredFields 
} from "@/lib/gmail"

export async function DELETE(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['draftId'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { draftId } = body

    // Delete draft
    const result = await deleteDraft(user.id, draftId)

    if (!result.success) {
      throw new Error(result.error || 'Failed to delete draft')
    }

    return {
      success: true,
      draftId: result.draftId,
      message: 'Draft deleted successfully'
    }
  })
}
