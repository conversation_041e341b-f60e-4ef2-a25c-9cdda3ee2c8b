import { NextRequest } from "next/server"
import {
  deleteDraft,
  withGmail<PERSON>uth
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { draftActionSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function DELETE(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-draft-delete', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, draftActionSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-draft-delete')
      throw new Error(`Invalid draft delete data: ${validation.error}`)
    }

    const { draftId } = validation.data

    // Delete draft
    const result = await deleteDraft(user.id, draftId)

    if (!result.success) {
      endPerformanceMetric('gmail-draft-delete')
      throw new Error(result.error || 'Failed to delete draft')
    }

    // End performance monitoring
    endPerformanceMetric('gmail-draft-delete')

    return {
      success: true,
      draftId: result.draftId,
      message: 'Draft deleted successfully'
    }
  })
}
