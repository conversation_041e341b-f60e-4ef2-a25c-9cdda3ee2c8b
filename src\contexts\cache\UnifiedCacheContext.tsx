"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react'
import { CacheService, cacheService } from './CacheService'
import {
  CacheNamespace,
  CacheKey,
  CacheQuery,
  CacheInvalidation,
  CacheStats,
  CachedEmailList,
  CachedEmail,
  CachedCalendarList,
  CachedCalendarEvent,
  CachedMeetingSpace,
  CachedConferenceRecord,
  CachedConversation,
  CachedMessage
} from './types'

// ============================================================================
// CONTEXT INTERFACE
// ============================================================================

interface UnifiedCacheContextType {
  // Core cache operations
  set: <T>(namespace: CacheNamespace, key: CacheKey, data: T, ttl?: number) => void
  get: <T>(namespace: CacheNamespace, key: CacheKey) => T | null
  has: (namespace: CacheNamespace, key: CacheKey) => boolean
  delete: (namespace: CacheNamespace, key: CacheKey) => boolean
  clear: (namespace?: CacheNamespace, keys?: CacheKey[]) => void
  
  // Advanced operations
  query: <T>(query: CacheQuery) => Array<{ key: CacheKey; data: T }>
  invalidate: (criteria: CacheInvalidation) => number
  
  // Email-specific helpers (maintaining compatibility with existing EmailCacheContext)
  getCachedEmails: (type: string, category?: string) => CachedEmailList | null
  setCachedEmails: (type: string, data: CachedEmailList, category?: string) => void
  getCachedEmailDetail: (emailId: string) => CachedEmail | null
  setCachedEmailDetail: (emailId: string, data: CachedEmail) => void
  updateCachedEmail: (emailId: string, updates: Partial<CachedEmail>) => void
  isCacheValid: (type: string, category?: string, maxAge?: number) => boolean
  
  // Calendar-specific helpers
  getCachedCalendarEvents: (key: string) => CachedCalendarList | null
  setCachedCalendarEvents: (key: string, data: CachedCalendarList) => void
  getCachedCalendarEvent: (eventId: string) => CachedCalendarEvent | null
  setCachedCalendarEvent: (eventId: string, data: CachedCalendarEvent) => void
  
  // Meet-specific helpers
  getCachedMeetingSpace: (spaceId: string) => CachedMeetingSpace | null
  setCachedMeetingSpace: (spaceId: string, data: CachedMeetingSpace) => void
  getCachedConference: (conferenceId: string) => CachedConferenceRecord | null
  setCachedConference: (conferenceId: string, data: CachedConferenceRecord) => void
  
  // AI-specific helpers
  getCachedConversation: (conversationId: string) => CachedConversation | null
  setCachedConversation: (conversationId: string, data: CachedConversation) => void
  getCachedMessages: (conversationId: string) => CachedMessage[] | null
  setCachedMessages: (conversationId: string, data: CachedMessage[]) => void
  
  // Preloading and performance
  preloadAllData: () => Promise<void>
  isPreloading: boolean
  
  // Stats and monitoring
  getStats: () => CacheStats
  getNamespaceSize: (namespace: CacheNamespace) => number
  cleanup: () => number
}

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const UnifiedCacheContext = createContext<UnifiedCacheContextType | undefined>(undefined)

// Cache expiration constants
const CACHE_EXPIRATION = 5 * 60 * 1000 // 5 minutes
const EMAIL_CACHE_EXPIRATION = 5 * 60 * 1000 // 5 minutes
const CALENDAR_CACHE_EXPIRATION = 10 * 60 * 1000 // 10 minutes
const MEET_CACHE_EXPIRATION = 15 * 60 * 1000 // 15 minutes
const AI_CACHE_EXPIRATION = 30 * 60 * 1000 // 30 minutes

// ============================================================================
// PROVIDER COMPONENT
// ============================================================================

export function UnifiedCacheProvider({ children }: { children: ReactNode }) {
  const [isPreloading, setIsPreloading] = useState(false)
  const [hasPreloaded, setHasPreloaded] = useState(false)

  // ============================================================================
  // CORE CACHE OPERATIONS
  // ============================================================================

  const set = useCallback(<T,>(namespace: CacheNamespace, key: CacheKey, data: T, ttl?: number) => {
    cacheService.set(namespace, key, data, ttl)
  }, [])

  const get = useCallback(<T,>(namespace: CacheNamespace, key: CacheKey): T | null => {
    return cacheService.get<T>(namespace, key)
  }, [])

  const has = useCallback((namespace: CacheNamespace, key: CacheKey): boolean => {
    return cacheService.has(namespace, key)
  }, [])

  const deleteEntry = useCallback((namespace: CacheNamespace, key: CacheKey): boolean => {
    return cacheService.delete(namespace, key)
  }, [])

  const clear = useCallback((namespace?: CacheNamespace, keys?: CacheKey[]) => {
    cacheService.clear(namespace, keys)
  }, [])

  const query = useCallback(<T,>(queryObj: CacheQuery) => {
    return cacheService.query<T>(queryObj)
  }, [])

  const invalidate = useCallback((criteria: CacheInvalidation): number => {
    return cacheService.invalidate(criteria)
  }, [])

  // ============================================================================
  // EMAIL-SPECIFIC HELPERS (Compatibility with existing EmailCacheContext)
  // ============================================================================

  const getCachedEmails = useCallback((type: string, category?: string): CachedEmailList | null => {
    const key = category ? `categories.${category}` : type
    return get<CachedEmailList>('email', key)
  }, [get])

  const setCachedEmails = useCallback((type: string, data: CachedEmailList, category?: string) => {
    const key = category ? `categories.${category}` : type
    set('email', key, data, EMAIL_CACHE_EXPIRATION)
  }, [set])

  const getCachedEmailDetail = useCallback((emailId: string): CachedEmail | null => {
    return get<CachedEmail>('email', `emailDetails.${emailId}`)
  }, [get])

  const setCachedEmailDetail = useCallback((emailId: string, data: CachedEmail) => {
    set('email', `emailDetails.${emailId}`, data, EMAIL_CACHE_EXPIRATION)
  }, [set])

  const updateCachedEmail = useCallback((emailId: string, updates: Partial<CachedEmail>) => {
    const existing = getCachedEmailDetail(emailId)
    if (existing) {
      const updated = { ...existing, ...updates }
      setCachedEmailDetail(emailId, updated)
    }
  }, [getCachedEmailDetail, setCachedEmailDetail])

  const isCacheValid = useCallback((type: string, category?: string, maxAge: number = CACHE_EXPIRATION): boolean => {
    const key = category ? `categories.${category}` : type
    return has('email', key)
  }, [has])

  // ============================================================================
  // CALENDAR-SPECIFIC HELPERS
  // ============================================================================

  const getCachedCalendarEvents = useCallback((key: string): CachedCalendarList | null => {
    return get<CachedCalendarList>('calendar', key)
  }, [get])

  const setCachedCalendarEvents = useCallback((key: string, data: CachedCalendarList) => {
    set('calendar', key, data, CALENDAR_CACHE_EXPIRATION)
  }, [set])

  const getCachedCalendarEvent = useCallback((eventId: string): CachedCalendarEvent | null => {
    return get<CachedCalendarEvent>('calendar', `eventDetails.${eventId}`)
  }, [get])

  const setCachedCalendarEvent = useCallback((eventId: string, data: CachedCalendarEvent) => {
    set('calendar', `eventDetails.${eventId}`, data, CALENDAR_CACHE_EXPIRATION)
  }, [set])

  // ============================================================================
  // MEET-SPECIFIC HELPERS
  // ============================================================================

  const getCachedMeetingSpace = useCallback((spaceId: string): CachedMeetingSpace | null => {
    return get<CachedMeetingSpace>('meet', `spaces.${spaceId}`)
  }, [get])

  const setCachedMeetingSpace = useCallback((spaceId: string, data: CachedMeetingSpace) => {
    set('meet', `spaces.${spaceId}`, data, MEET_CACHE_EXPIRATION)
  }, [set])

  const getCachedConference = useCallback((conferenceId: string): CachedConferenceRecord | null => {
    return get<CachedConferenceRecord>('meet', `conferences.${conferenceId}`)
  }, [get])

  const setCachedConference = useCallback((conferenceId: string, data: CachedConferenceRecord) => {
    set('meet', `conferences.${conferenceId}`, data, MEET_CACHE_EXPIRATION)
  }, [set])

  // ============================================================================
  // AI-SPECIFIC HELPERS
  // ============================================================================

  const getCachedConversation = useCallback((conversationId: string): CachedConversation | null => {
    return get<CachedConversation>('ai', `conversations.${conversationId}`)
  }, [get])

  const setCachedConversation = useCallback((conversationId: string, data: CachedConversation) => {
    set('ai', `conversations.${conversationId}`, data, AI_CACHE_EXPIRATION)
  }, [set])

  const getCachedMessages = useCallback((conversationId: string): CachedMessage[] | null => {
    return get<CachedMessage[]>('ai', `messages.${conversationId}`)
  }, [get])

  const setCachedMessages = useCallback((conversationId: string, data: CachedMessage[]) => {
    set('ai', `messages.${conversationId}`, data, AI_CACHE_EXPIRATION)
  }, [set])

  // ============================================================================
  // PRELOADING AND PERFORMANCE
  // ============================================================================

  const preloadAllData = useCallback(async () => {
    if (isPreloading || hasPreloaded) return

    setIsPreloading(true)
    try {
      console.log('🚀 [UNIFIED CACHE] Starting comprehensive data preloading...')

      // Get date ranges for preloading
      const today = new Date()
      today.setHours(23, 59, 59, 999)
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(today.getDate() - 7)
      sevenDaysAgo.setHours(0, 0, 0, 0)
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)

      const dateParams = `&dateFrom=${sevenDaysAgo.toISOString()}&dateTo=${today.toISOString()}`

      // Preload email data (maintaining existing patterns)
      const emailPreloadPromises = [
        // Inbox
        fetch(`/api/gmail/inbox?limit=20${dateParams}`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.emails) {
              setCachedEmails('inbox_7days', {
                emails: data.emails.map((email: any) => ({ ...email, date: new Date(email.date) })),
                totalCount: data.totalCount || 0,
                timestamp: Date.now(),
                currentPage: 1,
                totalPages: Math.ceil((data.totalCount || 0) / 20)
              })
            }
          }
        }).catch(err => console.error('Failed to preload inbox:', err)),

        // Sent emails
        fetch(`/api/gmail/sent?limit=15${dateParams}`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.emails) {
              setCachedEmails('sent_7days', {
                emails: data.emails.map((email: any) => ({ ...email, date: new Date(email.date) })),
                totalCount: data.totalCount || 0,
                timestamp: Date.now(),
                currentPage: 1,
                totalPages: Math.ceil((data.totalCount || 0) / 15)
              })
            }
          }
        }).catch(err => console.error('Failed to preload sent:', err))
      ]

      // Preload calendar data
      const calendarPreloadPromises = [
        // Upcoming events
        fetch(`/api/calendar/events?timeMin=${today.toISOString()}&timeMax=${nextWeek.toISOString()}&maxResults=50`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.events) {
              setCachedCalendarEvents('upcoming_week', {
                events: data.events,
                totalCount: data.events.length,
                timestamp: Date.now(),
                timeMin: today.toISOString(),
                timeMax: nextWeek.toISOString()
              })
            }
          }
        }).catch(err => console.error('Failed to preload calendar events:', err)),

        // Today's events
        fetch(`/api/calendar/events?timeMin=${today.toISOString()}&timeMax=${today.toISOString().split('T')[0]}T23:59:59.999Z&maxResults=20`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.events) {
              setCachedCalendarEvents('today', {
                events: data.events,
                totalCount: data.events.length,
                timestamp: Date.now(),
                timeMin: today.toISOString(),
                timeMax: today.toISOString().split('T')[0] + 'T23:59:59.999Z'
              })
            }
          }
        }).catch(err => console.error('Failed to preload today calendar events:', err))
      ]

      // Preload Meet data (if user has Meet connected)
      const meetPreloadPromises = [
        // Recent meeting spaces
        fetch('/api/meet/spaces?limit=10').then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.spaces) {
              set('meet', 'recent_spaces', {
                spaces: data.spaces,
                timestamp: Date.now(),
                totalCount: data.spaces.length
              }, MEET_CACHE_EXPIRATION)
            }
          }
        }).catch(err => console.error('Failed to preload Meet spaces:', err)),

        // Recent conferences
        fetch('/api/meet/conferences/recent?limit=10').then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.conferences) {
              set('meet', 'recent_conferences', {
                conferences: data.conferences,
                timestamp: Date.now(),
                totalCount: data.conferences.length
              }, MEET_CACHE_EXPIRATION)
            }
          }
        }).catch(err => console.error('Failed to preload Meet conferences:', err))
      ]

      // Wait for all preloading to complete
      await Promise.allSettled([...emailPreloadPromises, ...calendarPreloadPromises, ...meetPreloadPromises])
      setHasPreloaded(true)
      console.log('✅ [UNIFIED CACHE] Comprehensive data preloading completed')

    } catch (error) {
      console.error('❌ [UNIFIED CACHE] Error during data preloading:', error)
    } finally {
      setIsPreloading(false)
    }
  }, [isPreloading, hasPreloaded, setCachedEmails, setCachedCalendarEvents])

  // ============================================================================
  // STATS AND MONITORING
  // ============================================================================

  const getStats = useCallback((): CacheStats => {
    return cacheService.getStats()
  }, [])

  const getNamespaceSize = useCallback((namespace: CacheNamespace): number => {
    return cacheService.getNamespaceSize(namespace)
  }, [])

  const cleanup = useCallback((): number => {
    return cacheService.cleanup()
  }, [])

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue: UnifiedCacheContextType = {
    // Core operations
    set,
    get,
    has,
    delete: deleteEntry,
    clear,
    query,
    invalidate,

    // Email helpers
    getCachedEmails,
    setCachedEmails,
    getCachedEmailDetail,
    setCachedEmailDetail,
    updateCachedEmail,
    isCacheValid,

    // Calendar helpers
    getCachedCalendarEvents,
    setCachedCalendarEvents,
    getCachedCalendarEvent,
    setCachedCalendarEvent,

    // Meet helpers
    getCachedMeetingSpace,
    setCachedMeetingSpace,
    getCachedConference,
    setCachedConference,

    // AI helpers
    getCachedConversation,
    setCachedConversation,
    getCachedMessages,
    setCachedMessages,

    // Performance
    preloadAllData,
    isPreloading,

    // Monitoring
    getStats,
    getNamespaceSize,
    cleanup
  }

  return (
    <UnifiedCacheContext.Provider value={contextValue}>
      {children}
    </UnifiedCacheContext.Provider>
  )
}

// ============================================================================
// CUSTOM HOOK
// ============================================================================

export function useUnifiedCache(): UnifiedCacheContextType {
  const context = useContext(UnifiedCacheContext)
  if (context === undefined) {
    throw new Error('useUnifiedCache must be used within a UnifiedCacheProvider')
  }
  return context
}

// Backward compatibility hook for existing email cache usage
export function useEmailCache() {
  const cache = useUnifiedCache()
  return {
    getCachedEmails: cache.getCachedEmails,
    setCachedEmails: cache.setCachedEmails,
    getCachedEmailDetail: cache.getCachedEmailDetail,
    setCachedEmailDetail: cache.setCachedEmailDetail,
    updateCachedEmail: cache.updateCachedEmail,
    isCacheValid: cache.isCacheValid,
    preloadAllMailData: cache.preloadAllData,
    isPreloading: cache.isPreloading,
    invalidateCache: (type?: string, category?: string) => {
      if (type && category) {
        cache.delete('email', `categories.${category}`)
      } else if (type) {
        cache.delete('email', type)
      } else {
        cache.clear('email')
      }
    }
  }
}
