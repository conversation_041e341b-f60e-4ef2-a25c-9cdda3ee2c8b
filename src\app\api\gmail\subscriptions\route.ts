import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { getGmailClient } from "@/lib/gmail"
import { google } from 'googleapis'

interface SenderStats {
  senderEmail: string
  senderName: string
  totalEmails: number
  unreadEmails: number
  readEmails: number
  openRate: number
  readRate: number
  latestEmail: Date
  canUnsubscribe: boolean
}

export async function GET(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if user has Gmail connected
    if (!user.gmailRefreshToken || !user.gmailConnected) {
      return NextResponse.json({ 
        error: "Gmail account not connected",
        requiresReconnection: true 
      }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const getAllData = searchParams.get('getAllData') === 'true'

    try {
      // Set up OAuth2 client
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET
      )
      oauth2Client.setCredentials({
        refresh_token: user.gmailRefreshToken,
      })

      const gmail = google.gmail({ version: 'v1', auth: oauth2Client })

      // Build search query with date range
      let query = 'in:inbox'
      if (startDate && endDate) {
        query += ` after:${startDate} before:${endDate}`
      }

      // Get all emails in the date range (no limits if getAllData is true)
      const allEmails = []
      let nextPageToken: string | undefined

      do {
        const response = await gmail.users.messages.list({
          userId: 'me',
          q: query,
          maxResults: getAllData ? 500 : 100, // Fetch more per page if getting all data
          pageToken: nextPageToken
        })

        const messageIds = response.data.messages || []
        
        if (messageIds.length === 0) break

        // Fetch details for all messages in this batch
        const emailDetails = await Promise.all(
          messageIds.map(async (message) => {
            try {
              const email = await gmail.users.messages.get({
                userId: 'me',
                id: message.id!,
                format: 'metadata',
                metadataHeaders: ['Subject', 'Date', 'From']
              })

              const headers = email.data.payload?.headers || []
              const subject = headers.find(h => h.name === 'Subject')?.value || '(No Subject)'
              const dateHeader = headers.find(h => h.name === 'Date')?.value
              const from = headers.find(h => h.name === 'From')?.value || ''

              // Get snippet
              const snippetResponse = await gmail.users.messages.get({
                userId: 'me',
                id: message.id!,
                format: 'minimal'
              })

              const isRead = !email.data.labelIds?.includes('UNREAD')
              const isArchived = !email.data.labelIds?.includes('INBOX')

              return {
                id: message.id!,
                subject,
                snippet: snippetResponse.data.snippet || '',
                from,
                date: dateHeader ? new Date(dateHeader) : new Date(),
                isRead,
                isArchived
              }
            } catch (error) {
              console.error('Error fetching email details:', error)
              return null
            }
          })
        )

        const validEmails = emailDetails.filter(email => email !== null)
        allEmails.push(...validEmails)

        nextPageToken = response.data.nextPageToken || undefined
        
        // If not getting all data, limit to reasonable amount
        if (!getAllData && allEmails.length >= 1000) {
          break
        }
        
      } while (nextPageToken && getAllData) // Continue only if getAllData is true

      // Group emails by sender
      const senderMap = new Map<string, {
        emails: typeof allEmails
        senderName: string
        totalEmails: number
        unreadEmails: number
        readEmails: number
        latestEmail: Date
      }>()

      allEmails.forEach(email => {
        // Extract sender email and name from "from" field
        const fromMatch = email.from.match(/<(.+)>/) || email.from.match(/(.+)/)
        const senderEmail = fromMatch ? fromMatch[1].trim().toLowerCase() : email.from.toLowerCase()
        const senderName = email.from.replace(/<.+>/, '').replace(/[<>"]/g, '').trim() || senderEmail

        if (!senderMap.has(senderEmail)) {
          senderMap.set(senderEmail, {
            emails: [],
            senderName,
            totalEmails: 0,
            unreadEmails: 0,
            readEmails: 0,
            latestEmail: email.date
          })
        }

        const sender = senderMap.get(senderEmail)!
        sender.emails.push(email)
        sender.totalEmails++
        
        if (email.isRead) {
          sender.readEmails++
        } else {
          sender.unreadEmails++
        }

        if (email.date > sender.latestEmail) {
          sender.latestEmail = email.date
        }
      })

      // Convert to stats format and calculate rates
      const senderStats: SenderStats[] = Array.from(senderMap.entries()).map(([email, data]) => {
        const openRate = data.totalEmails > 0 ? (data.readEmails / data.totalEmails) * 100 : 0
        const readRate = openRate // For Gmail, open rate and read rate are the same
        
        // Check if sender likely has unsubscribe capability (newsletters, marketing emails)
        const canUnsubscribe = data.totalEmails >= 3 || 
          data.senderName.toLowerCase().includes('newsletter') ||
          data.senderName.toLowerCase().includes('marketing') ||
          data.senderName.toLowerCase().includes('no-reply') ||
          data.senderName.toLowerCase().includes('noreply')

        return {
          senderEmail: email,
          senderName: data.senderName,
          totalEmails: data.totalEmails,
          unreadEmails: data.unreadEmails,
          readEmails: data.readEmails,
          openRate: Math.round(openRate),
          readRate: Math.round(readRate),
          latestEmail: data.latestEmail,
          canUnsubscribe
        }
      })

      // Sort by total emails descending
      senderStats.sort((a, b) => b.totalEmails - a.totalEmails)

      return NextResponse.json({
        senderStats: getAllData ? senderStats : senderStats.slice(0, 50), // Return all if getAllData, otherwise limit to 50
        success: true
      })

    } catch (gmailError) {
      console.error("Gmail API error:", gmailError)
      
      if (gmailError instanceof Error && gmailError.message.includes('invalid_grant')) {
        return NextResponse.json({ 
          error: "Gmail authentication expired",
          requiresReconnection: true 
        }, { status: 401 })
      }
      
      return NextResponse.json({ 
        error: "Failed to fetch emails from Gmail",
        requiresReconnection: true 
      }, { status: 500 })
    }

  } catch (error) {
    console.error("Error analyzing subscriptions:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    if (!user.gmailRefreshToken || !user.gmailConnected) {
      return NextResponse.json({ 
        error: "Gmail account not connected" 
      }, { status: 400 })
    }

    const { action, senderEmails, operation } = await request.json()

    if (action === 'bulk_unsubscribe') {
      if (!senderEmails || !Array.isArray(senderEmails)) {
        return NextResponse.json({ error: "Sender emails are required" }, { status: 400 })
      }

      // Set up OAuth2 client
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET
      )
      oauth2Client.setCredentials({
        refresh_token: user.gmailRefreshToken,
      })

      const gmail = google.gmail({ version: 'v1', auth: oauth2Client })
      const results = []

      for (const senderEmail of senderEmails) {
        try {
          // Find all emails from this sender
          const query = `from:${senderEmail}`
          let allMessageIds: string[] = []
          let nextPageToken: string | undefined

          // Get all messages from this sender
          do {
            const messagesResponse = await gmail.users.messages.list({
              userId: 'me',
              q: query,
              maxResults: 500,
              pageToken: nextPageToken
            })

                         const messageIds = messagesResponse.data.messages?.map(msg => msg.id).filter(Boolean) || []
             allMessageIds.push(...messageIds as string[])
             nextPageToken = messagesResponse.data.nextPageToken || undefined
          } while (nextPageToken)

          if (allMessageIds.length > 0) {
            if (operation === 'delete') {
              // Delete all emails from this sender in batches
              const batchSize = 100
              for (let i = 0; i < allMessageIds.length; i += batchSize) {
                const batch = allMessageIds.slice(i, i + batchSize)
                await Promise.all(batch.map(messageId =>
                  gmail.users.messages.delete({
                    userId: 'me',
                    id: messageId
                  }).catch(err => console.error(`Failed to delete message ${messageId}:`, err))
                ))
              }
            } else if (operation === 'archive') {
              // Archive all emails from this sender in batches
              const batchSize = 1000
              for (let i = 0; i < allMessageIds.length; i += batchSize) {
                const batch = allMessageIds.slice(i, i + batchSize)
                await gmail.users.messages.batchModify({
                  userId: 'me',
                  requestBody: {
                    ids: batch,
                    removeLabelIds: ['INBOX']
                  }
                }).catch(err => console.error(`Failed to archive batch:`, err))
              }
            }
          }

          results.push({
            senderEmail,
            success: true,
            processed: allMessageIds.length
          })
        } catch (error) {
          console.error(`Error processing ${senderEmail}:`, error)
          results.push({
            senderEmail,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return NextResponse.json({
        success: true,
        results
      })
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 })

  } catch (error) {
    console.error("Error performing bulk operation:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 