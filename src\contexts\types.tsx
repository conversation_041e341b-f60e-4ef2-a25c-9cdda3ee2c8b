export interface CachedEmail {
  id: string
  threadId: string
  from: string
  subject: string
  date: Date
  snippet: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  labels?: Array<{
    id: string
    name: string
    type: string
  }>
  body?: string
  attachments?: Array<{
    filename: string
    mimeType: string
    size: number
    attachmentId: string
  }>
}

export interface CachedEmailList {
  emails: CachedEmail[]
  totalCount: number
  timestamp: number
  currentPage: number
  totalPages: number
}

export interface EmailCache {
  // Dynamic cache keys to support patterns like inbox_7days, sent_7days, etc.
  [key: string]: CachedEmailList | {
    [category: string]: CachedEmailList
  } | {
    [emailId: string]: {
      data: CachedEmail
      timestamp: number
    }
  } | undefined
  // Special named properties for known cache structure
  categories: {
    [category: string]: CachedEmailList
  }
  emailDetails: {
    [emailId: string]: {
      data: CachedEmail
      timestamp: number
    }
  }
}