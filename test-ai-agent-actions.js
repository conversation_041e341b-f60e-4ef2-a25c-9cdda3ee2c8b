// Test AI Agent Action Detection
const { aiAgentService } = require('./src/lib/ai-agent')

async function testActionDetection() {
  console.log('🧪 Testing AI Agent Action Detection...')
  
  const userId = 'test-user-123'
  
  try {
    // Test 1: Email action detection
    console.log('\n📧 Test 1: Email Action Detection')
    const emailMessage = "send <NAME_EMAIL> about project update"
    const conversationId = await aiAgentService.createConversation(userId, 'Email Test')
    const response1 = await aiAgentService.sendMessage(conversationId, userId, emailMessage)
    
    console.log('User Message:', emailMessage)
    console.log('AI Response:', response1.content.substring(0, 200) + '...')
    console.log('Actions Detected:', response1.metadata?.actions?.length || 0)
    if (response1.metadata?.actions) {
      response1.metadata.actions.forEach((action, i) => {
        console.log(`  Action ${i + 1}:`, action.type, action.status)
      })
    }
    
    // Test 2: Calendar action detection
    console.log('\n📅 Test 2: Calendar Action Detection')
    const calendarMessage = "schedule a meeting with team tomorrow at 2pm about quarterly review"
    const response2 = await aiAgentService.sendMessage(conversationId, userId, calendarMessage)
    
    console.log('User Message:', calendarMessage)
    console.log('AI Response:', response2.content.substring(0, 200) + '...')
    console.log('Actions Detected:', response2.metadata?.actions?.length || 0)
    if (response2.metadata?.actions) {
      response2.metadata.actions.forEach((action, i) => {
        console.log(`  Action ${i + 1}:`, action.type, action.status)
      })
    }
    
    // Test 3: Task action detection
    console.log('\n✅ Test 3: Task Action Detection')
    const taskMessage = "remind me to complete the project report by Friday"
    const response3 = await aiAgentService.sendMessage(conversationId, userId, taskMessage)
    
    console.log('User Message:', taskMessage)
    console.log('AI Response:', response3.content.substring(0, 200) + '...')
    console.log('Actions Detected:', response3.metadata?.actions?.length || 0)
    if (response3.metadata?.actions) {
      response3.metadata.actions.forEach((action, i) => {
        console.log(`  Action ${i + 1}:`, action.type, action.status)
      })
    }
    
    console.log('\n✅ AI Agent Action Detection Test Complete!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.error('Stack:', error.stack)
  }
}

// Run the test
testActionDetection()
