// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                       String  @id @default(cuid())
  userId                   String
  type                     String
  provider                 String
  providerAccountId        String
  refresh_token            String? @db.Text
  access_token             String? @db.Text
  expires_at               Int?
  token_type               String?
  scope                    String?
  id_token                 String? @db.Text
  session_state            String?
  refresh_token_expires_in Int?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]

  
  // Enhanced Gmail Integration
  gmailConnected    Boolean   @default(false)
  gmailRefreshToken String?   @db.Text  // Store encrypted
  dailySendLimit    Int       @default(250)
  dailySendCount    Int       @default(0)
  lastSendReset     DateTime? // Reset daily count
  
  // Google Calendar Integration
  calendarConnected    Boolean   @default(false)
  calendarRefreshToken String?   @db.Text  // Store encrypted

  // Google Meet Integration
  meetConnected        Boolean   @default(false)
  meetRefreshToken     String?   @db.Text  // Store encrypted
  
  // Mass mailer relationships
  campaigns     Campaign[]
  contacts      Contact[]
  templates     EmailTemplate[]
  contactLists  ContactList[]

  // AI Agent relationships
  conversations     Conversation[]
  userBehavior      UserBehavior?
  knowledgeEntries  KnowledgeEntry[]
  aiTasks          AITask[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Enhanced Campaign model with detailed status
model Campaign {
  id          String         @id @default(cuid())
  name        String
  subject     String
  content     String         @db.Text
  status      CampaignStatus @default(DRAFT)
  scheduledAt DateTime?
  sentAt      DateTime?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  userId      String
  
  // Enhanced tracking fields
  totalEmails Int @default(0)
  sentCount   Int @default(0)
  failedCount Int @default(0)
  
  // Relationships
  user        User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  sentEmails  SentEmail[]
  errors      CampaignError[]
  sendQueue   SendQueue[]
  contactLists CampaignContactList[] // Many campaigns can use same list
}

enum CampaignStatus {
  DRAFT
  SCHEDULED
  SENDING
  SENT
  PAUSED
  FAILED
}

model CampaignError {
  id         String   @id @default(cuid())
  campaignId String
  contactId  String
  error      String   @db.Text
  createdAt  DateTime @default(now())
  
  campaign   Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
}

// Enhanced Contact model
model Contact {
  id        String   @id @default(cuid())
  email     String
  firstName String?
  lastName  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  
  // Enhanced contact features
  customFields Json? // Store additional contact data
  
  // Relationships
  user         User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  sentEmails   SentEmail[]
  lists        ContactListMember[]
  sendQueue    SendQueue[]
  
  @@unique([email, userId])
}

// Contact Lists/Groups
model ContactList {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  
  // Relationships
  user        User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  contacts    ContactListMember[]
  campaigns   CampaignContactList[] // Many campaigns can use same list
}

model ContactListMember {
  id            String      @id @default(cuid())
  contactListId String
  contactId     String
  
  contactList   ContactList @relation(fields: [contactListId], references: [id], onDelete: Cascade)
  contact       Contact     @relation(fields: [contactId], references: [id], onDelete: Cascade)
  
  @@unique([contactListId, contactId])
}

// Junction table for Campaign <-> ContactList many-to-many relationship
model CampaignContactList {
  id            String      @id @default(cuid())
  campaignId    String
  contactListId String
  
  campaign      Campaign    @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  contactList   ContactList @relation(fields: [contactListId], references: [id], onDelete: Cascade)
  
  @@unique([campaignId, contactListId])
}

model EmailTemplate {
  id        String   @id @default(cuid())
  name      String
  subject   String
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Enhanced Email Tracking
model SentEmail {
  id             String      @id @default(cuid())
  campaignId     String?     // Make optional for standalone emails
  contactId      String
  sentAt         DateTime    @default(now())
  
  // Enhanced tracking fields
  status         EmailStatus @default(SENT)
  errorMessage   String?     @db.Text // Error messages can be long
  gmailMessageId String?     // Gmail's message ID for tracking
  
  // Tracking pixels/links
  trackingId     String?     @unique
  deliveredAt    DateTime?
  openedAt       DateTime?
  clickedAt      DateTime?
  openCount      Int         @default(0)
  clickCount     Int         @default(0)
  bounced        Boolean     @default(false)
  unsubscribed   Boolean     @default(false)
  
  campaign       Campaign?   @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  contact        Contact     @relation(fields: [contactId], references: [id], onDelete: Cascade)
}

enum EmailStatus {
  PENDING
  SENT
  DELIVERED
  BOUNCED
  FAILED
  UNSUBSCRIBED
}

// Rate Limiting & Queue
model SendQueue {
  id           String      @id @default(cuid())
  campaignId   String
  contactId    String
  scheduledFor DateTime
  status       QueueStatus @default(PENDING)
  attempts     Int         @default(0)
  lastError    String?     @db.Text // Error messages can be long
  createdAt    DateTime    @default(now())
  
  campaign     Campaign    @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  contact      Contact     @relation(fields: [contactId], references: [id], onDelete: Cascade)
}

enum QueueStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
  CANCELLED
}

// AI Agent Models

model Conversation {
  id              String   @id @default(cuid())
  userId          String
  title           String   @db.Text // Encrypted conversation title
  status          String   @default("active") // active, archived, deleted
  context         String?  @db.Text // Encrypted JSON context (email IDs, calendar events, etc.)
  summary         String?  @db.Text // Encrypted conversation summary
  encryptionSalt  String   // For field-level encryption

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages        Message[]

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model Message {
  id              String   @id @default(cuid())
  conversationId  String
  role            String   // user, assistant, system
  content         String   @db.Text // Encrypted message content
  metadata        String?  @db.Text // Encrypted JSON metadata (tokens, model, context)
  encryptionSalt  String   // For field-level encryption

  conversation    Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  createdAt       DateTime @default(now())

  @@index([conversationId])
  @@index([role])
  @@index([createdAt])
}

model UserBehavior {
  id                    String   @id @default(cuid())
  userId                String   @unique

  // Activity counters
  totalEmails           Int      @default(0)
  totalMeetings         Int      @default(0)
  totalTasks            Int      @default(0)
  totalChatMessages     Int      @default(0)

  // Email behavior patterns
  emailFrequency        String?  // daily, weekly, monthly
  commonEmailTimes      String?  @db.Text // Encrypted JSON array of preferred times
  frequentContacts      String?  @db.Text // Encrypted JSON array of frequent email contacts
  emailCategories       String?  @db.Text // Encrypted JSON of category preferences

  // Chat behavior patterns
  chatFrequency         String?  // high, medium, low
  commonChatTimes       String?  @db.Text // Encrypted JSON array of active chat times
  preferredResponseStyle String? // formal, casual, technical, brief
  commonQueries         String?  @db.Text // Encrypted JSON array of frequent query types

  // Task and calendar patterns
  taskCreationPattern   String?  @db.Text // Encrypted JSON of task creation habits
  meetingPreferences    String?  @db.Text // Encrypted JSON of meeting preferences
  calendarUsage         String?  @db.Text // Encrypted JSON of calendar usage patterns

  // AI interaction patterns
  aiInteractionStyle    String?  // collaborative, directive, exploratory
  contextPreferences    String?  @db.Text // Encrypted JSON of context preferences
  feedbackPatterns      String?  @db.Text // Encrypted JSON of feedback patterns

  encryptionSalt        String   // For field-level encryption

  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@index([userId])
}

model KnowledgeEntry {
  id              String   @id @default(cuid())
  userId          String
  type            String   // contact, preference, fact, pattern, context
  category        String   // email, calendar, task, personal, work
  key             String   // Encrypted key/identifier
  value           String   @db.Text // Encrypted value/data
  confidence      Float    @default(0.5) // 0.0 to 1.0 confidence score
  source          String   // conversation, email_analysis, user_input, inferred
  contextTags     String?  @db.Text // Encrypted JSON array of context tags
  encryptionSalt  String   // For field-level encryption

  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([userId])
  @@index([type])
  @@index([category])
  @@index([confidence])
  @@unique([userId, type, key])
}

model AITask {
  id                String   @id @default(cuid())
  userId            String
  conversationId    String?  // Optional link to conversation that created this task

  title             String   @db.Text // Encrypted task title
  description       String?  @db.Text // Encrypted task description
  type              String   // email, meeting, reminder, follow_up, bill_payment, deadline
  status            String   @default("pending") // pending, in_progress, completed, cancelled
  priority          String   @default("medium") // high, medium, low

  // Task details
  dueDate           DateTime?
  reminderDate      DateTime?
  completedAt       DateTime?

  // Related entities (encrypted JSON)
  relatedEmails     String?  @db.Text // Encrypted JSON array of email IDs
  relatedContacts   String?  @db.Text // Encrypted JSON array of contact info
  relatedMeetings   String?  @db.Text // Encrypted JSON array of meeting IDs

  // AI context
  aiContext         String?  @db.Text // Encrypted JSON of AI reasoning/context
  userFeedback      String?  @db.Text // Encrypted user feedback on task

  encryptionSalt    String   // For field-level encryption

  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([priority])
  @@index([dueDate])
  @@index([conversationId])
}
