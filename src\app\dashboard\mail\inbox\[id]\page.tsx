"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  ArrowLeft,
  Star,
  Archive,
  Trash2,
  Reply,
  Forward,
  MoreVertical,
  Paperclip,
  Send,
  RefreshCw,
  Tag,
  Download
} from "lucide-react"
import Em<PERSON><PERSON>ender<PERSON> from "@/components/email/EmailRenderer"

interface EmailDetail {
  id: string
  threadId: string
  from: string
  to: string
  subject: string
  date: Date
  snippet: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  body: string
  profilePhoto?: string
  labels?: Array<{
    id: string
    name: string
    type: string
  }>
  attachments?: Array<{
    filename: string
    mimeType: string
    size: number
    attachmentId: string
  }>
}

interface GmailLabel {
  id: string
  name: string
  type: string
}





export default function EmailDetailPage() {
  const router = useRouter()
  const params = useParams()
  const emailId = params.id as string

  const [email, setEmail] = useState<EmailDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [showReplyDialog, setShowReplyDialog] = useState(false)
  const [showLabelDialog, setShowLabelDialog] = useState(false)
  const [replyContent, setReplyContent] = useState("")
  const [newLabelName, setNewLabelName] = useState("")
  const [labels, setLabels] = useState<GmailLabel[]>([])
  const [actionLoading, setActionLoading] = useState(false)


  useEffect(() => {
    if (emailId) {
      fetchEmail()
      fetchLabels()
    }
  }, [emailId])

  const fetchEmail = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/gmail/inbox/${emailId}`)
      if (response.ok) {
        const data = await response.json()
        setEmail(data.email)
      } else {
        console.error('Failed to fetch email')
        router.push('/dashboard/mail/inbox')
      }
    } catch (error) {
      console.error('Error fetching email:', error)
      router.push('/dashboard/mail/inbox')
    } finally {
      setLoading(false)
    }
  }



  const fetchLabels = async () => {
    try {
      const response = await fetch('/api/gmail/labels')
      if (response.ok) {
        const data = await response.json()
        setLabels(data.labels)
      }
    } catch (error) {
      console.error('Error fetching labels:', error)
    }
  }

  const handleEmailAction = async (action: string, value?: any) => {
    if (!email) return

    try {
      setActionLoading(true)
      const body: any = { action }
      
      if (action === 'star') {
        body.value = value
      } else if (action === 'addLabel' || action === 'removeLabel') {
        body.labelId = value
      }

      const response = await fetch(`/api/gmail/inbox/${email.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      })

      if (response.ok) {
        // Update the email state
        switch (action) {
          case 'markAsRead':
            setEmail(prev => prev ? { ...prev, isRead: true } : null)
            break
          case 'markAsUnread':
            setEmail(prev => prev ? { ...prev, isRead: false } : null)
            break
          case 'star':
            setEmail(prev => prev ? { ...prev, isStarred: value } : null)
            break
          case 'delete':
          case 'archive':
            // Navigate back to inbox after delete/archive
            router.push('/dashboard/mail/inbox')
            break
        }
      }
    } catch (error) {
      console.error(`Error performing ${action}:`, error)
    } finally {
      setActionLoading(false)
    }
  }

  const handleReply = async () => {
    if (!email || !replyContent.trim()) return

    try {
      const response = await fetch('/api/gmail/reply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: extractSenderEmail(email.from),
          subject: `Re: ${email.subject}`,
          htmlBody: replyContent.replace(/\n/g, '<br>'),
          textBody: replyContent,
          threadId: email.threadId,
          replyToMessageId: email.id
        })
      })

      if (response.ok) {
        setReplyContent("")
        setShowReplyDialog(false)
        // Show success message
        console.log('Reply sent successfully')
      } else {
        console.error('Failed to send reply')
      }
    } catch (error) {
      console.error('Error sending reply:', error)
    }
  }

  const createLabel = async () => {
    if (!newLabelName.trim()) return

    try {
      const response = await fetch('/api/gmail/labels', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: newLabelName.trim()
        })
      })

      if (response.ok) {
        setNewLabelName("")
        setShowLabelDialog(false)
        await fetchLabels()
        console.log('Label created successfully')
      } else {
        console.error('Failed to create label')
      }
    } catch (error) {
      console.error('Error creating label:', error)
    }
  }

  const extractSenderName = (fromHeader: string) => {
    const match = fromHeader.match(/^(.*?)\s*<.*>$/)
    return match ? match[1].trim().replace(/"/g, '') : fromHeader.split('@')[0]
  }

  const extractSenderEmail = (fromHeader: string) => {
    const match = fromHeader.match(/<(.+?)>/)
    return match ? match[1] : fromHeader
  }

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getLabelColor = (labelName: string) => {
    const colors: Record<string, string> = {
      'IMPORTANT': 'bg-red-100 text-red-800 border-red-200',
      'CATEGORY_SOCIAL': 'bg-blue-100 text-blue-800 border-blue-200',
      'CATEGORY_PROMOTIONS': 'bg-green-100 text-green-800 border-green-200',
      'CATEGORY_UPDATES': 'bg-purple-100 text-purple-800 border-purple-200',
      'CATEGORY_FORUMS': 'bg-orange-100 text-orange-800 border-orange-200',
      'SPAM': 'bg-red-100 text-red-800 border-red-200',
    }
    return colors[labelName] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!email) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-muted-foreground">Email not found</p>
          <Button onClick={() => router.push('/dashboard/mail/inbox')} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Inbox
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push('/dashboard/mail/inbox')}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Inbox</span>
        </Button>

        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEmailAction('star', !email.isStarred)}
            disabled={actionLoading}
            className={email.isStarred ? 'text-yellow-500' : ''}
          >
            <Star className={`h-4 w-4 ${email.isStarred ? 'fill-current' : ''}`} />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEmailAction('archive')}
            disabled={actionLoading}
          >
            <Archive className="h-4 w-4" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEmailAction('delete')}
            disabled={actionLoading}
          >
            <Trash2 className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEmailAction(email.isRead ? 'markAsUnread' : 'markAsRead')}>
                {email.isRead ? 'Mark as Unread' : 'Mark as Read'}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setShowLabelDialog(true)}>
                <Tag className="h-4 w-4 mr-2" />
                Add Label
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>



      {/* Email Content - Gmail Style */}
      <Card className="bg-white border border-[#dadce0] shadow-sm">
        <CardHeader className="bg-white border-b border-[#e8eaed] pb-4">
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <CardTitle className="text-xl font-normal text-[#202124] flex-1 leading-tight">
                {email.subject || '(No Subject)'}
              </CardTitle>
            </div>
            
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                {email.profilePhoto && (
                  <AvatarImage 
                    src={email.profilePhoto} 
                    alt={extractSenderName(email.from)}
                  />
                )}
                <AvatarFallback className="bg-[#1a73e8] text-white text-sm font-medium">
                  {extractSenderName(email.from).charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="font-medium text-[#202124] text-base">{extractSenderName(email.from)}</p>
                <p className="text-sm text-[#5f6368]">
                  {extractSenderEmail(email.from)}
                </p>
                <p className="text-sm text-[#5f6368]">
                  to: {email.to}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-[#5f6368]">
                  {formatDate(email.date)}
                </p>
                {!email.isRead && (
                  <Badge variant="default" className="text-xs mt-1 bg-[#e8f0fe] text-[#1a73e8] border-[#dadce0]">
                    Unread
                  </Badge>
                )}
              </div>
            </div>

            {/* Email Labels */}
            {email.labels && email.labels.filter(label => 
              label.type === 'user' || 
              ['IMPORTANT', 'CATEGORY_SOCIAL', 'CATEGORY_PROMOTIONS', 'CATEGORY_UPDATES', 'CATEGORY_FORUMS', 'SPAM'].includes(label.name)
            ).length > 0 && (
              <div className="flex flex-wrap gap-1">
                {email.labels
                  .filter(label => 
                    label.type === 'user' || 
                    ['IMPORTANT', 'CATEGORY_SOCIAL', 'CATEGORY_PROMOTIONS', 'CATEGORY_UPDATES', 'CATEGORY_FORUMS', 'SPAM'].includes(label.name)
                  )
                  .map((label) => (
                    <Badge 
                      key={label.id} 
                      variant="outline" 
                      className={`text-xs ${getLabelColor(label.name)}`}
                    >
                      {label.name.replace('CATEGORY_', '').toLowerCase()}
                    </Badge>
                  ))
                }
              </div>
            )}
            
            {email.attachments && email.attachments.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium flex items-center">
                  <Paperclip className="h-4 w-4 mr-2" />
                  Attachments ({email.attachments.length})
                </p>
                <div className="flex flex-wrap gap-2">
                  {email.attachments.map((attachment, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex flex-col">
                        <span className="text-sm font-medium text-gray-700 truncate max-w-[200px]">
                          {attachment.filename}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatFileSize(attachment.size)}
                        </span>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                        title="Download attachment"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>

              </div>
            )}
          </div>
        </CardHeader>
        
        <Separator />
        
        <CardContent className="pt-6 bg-white p-0">
          <EmailRenderer body={email.body} enableQuoteToggle={true} />
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center space-x-4 mt-6">
        <Button 
          onClick={() => setShowReplyDialog(true)} 
          className="flex items-center space-x-2"
        >
          <Reply className="h-4 w-4" />
          <span>Reply</span>
        </Button>
        
        <Button 
          variant="outline" 
          className="flex items-center space-x-2"
        >
          <Forward className="h-4 w-4" />
          <span>Forward</span>
        </Button>
      </div>

      {/* Reply Dialog */}
      <Dialog open={showReplyDialog} onOpenChange={setShowReplyDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Reply to: {email.subject}
            </DialogTitle>
            <DialogDescription>
              Replying to {extractSenderEmail(email.from)}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <Textarea
              placeholder="Type your reply..."
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              className="min-h-[200px]"
            />
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowReplyDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReply}
              disabled={!replyContent.trim()}
            >
              <Send className="h-4 w-4 mr-2" />
              Send Reply
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Label Dialog */}
      <Dialog open={showLabelDialog} onOpenChange={setShowLabelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Label</DialogTitle>
            <DialogDescription>
              Create a new label to organize your emails
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <input
              type="text"
              placeholder="Label name"
              value={newLabelName}
              onChange={(e) => setNewLabelName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowLabelDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={createLabel}
              disabled={!newLabelName.trim()}
            >
              Create Label
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}