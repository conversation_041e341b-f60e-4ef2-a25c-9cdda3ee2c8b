import crypto from 'crypto'

export type EncryptionContext = 'chat' | 'calendar' | 'email-analysis'

// Production encryption configuration - AES-256-GCM with HKDF
const ENCRYPTION_CONFIG = {
  algorithm: 'aes-256-gcm' as const,
  keyDerivation: 'hkdf',
  hashAlgorithm: 'sha256',
  ivLength: 12,        // 96 bits for AES-GCM
  saltLength: 32,      // 256 bits
  keyLength: 32,       // 256 bits
  tagLength: 16,       // 128 bits authentication tag
  maxPlaintextSize: 1024 * 1024 * 10 // 10MB limit
}

export interface EncryptedDataFormat {
  algorithm: string
  salt: string
  iv: string
  authTag: string
  ciphertext: string
  version: string
}

/**
 * Production-ready modern encryption service using AES-256-GCM
 * No legacy support - clean, fast, and secure
 */
export class ModernEncryptionService {
  private readonly version = '2.0'
  
  /**
   * Generate cryptographically secure random bytes
   */
  generateSecureRandom(length: number): string {
    return crypto.randomBytes(length).toString('hex')
  }
  
  /**
   * Create cryptographic hash for salts and identifiers
   */
  hashData(data: string): string {
    return crypto.createHash('sha256').update(data, 'utf8').digest('hex')
  }
  
  /**
   * Generate master key for user and context
   */
  private async generateMasterKey(
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<Buffer> {
    const keyMaterial = [
      userId,
      'eagle-mass-mailer',
      context,
      'v2.0',
      additionalEntropy || ''
    ].join(':')
    
    return crypto.createHash('sha256').update(keyMaterial, 'utf8').digest()
  }
  
  /**
   * Derive encryption and authentication keys using HKDF
   */
  private deriveKeys(
    masterKey: Buffer,
    salt: Buffer,
    context: string,
    userId: string
  ): { encryptionKey: Buffer; authKey: Buffer } {
    const info = Buffer.from(`${context}:${userId}:keys`, 'utf8')
    
    // Derive 64 bytes (32 for encryption + 32 for auth)
    const derivedKeyArrayBuffer = crypto.hkdfSync(
      ENCRYPTION_CONFIG.hashAlgorithm,
      masterKey,
      salt,
      info,
      64
    )
    
    // Convert ArrayBuffer to Buffer
    const derivedKey = Buffer.from(derivedKeyArrayBuffer)
    
    return {
      encryptionKey: derivedKey.subarray(0, 32),
      authKey: derivedKey.subarray(32, 64)
    }
  }
  
  /**
   * Check if data is in modern encrypted format
   */
  isEncrypted(data: string): boolean {
    try {
      const parsed = JSON.parse(data)
      return parsed.algorithm === ENCRYPTION_CONFIG.algorithm && 
             parsed.version === this.version &&
             parsed.salt && parsed.iv && parsed.authTag && parsed.ciphertext
    } catch {
      return false
    }
  }
  
  /**
   * Encrypt data using AES-256-GCM
   */
  async encryptData(
    plaintext: string,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<string> {
    if (!plaintext || typeof plaintext !== 'string') {
      throw new Error('Invalid plaintext: must be a non-empty string')
    }
    
    if (Buffer.byteLength(plaintext, 'utf8') > ENCRYPTION_CONFIG.maxPlaintextSize) {
      throw new Error(`Plaintext too large: maximum ${ENCRYPTION_CONFIG.maxPlaintextSize} bytes`)
    }
    
    try {
      // Generate master key
      const masterKey = await this.generateMasterKey(userId, context, additionalEntropy)
      
      // Generate cryptographically secure random values
      const salt = crypto.randomBytes(ENCRYPTION_CONFIG.saltLength)
      const iv = crypto.randomBytes(ENCRYPTION_CONFIG.ivLength)
      
      // Derive keys using HKDF
      const derivedKeys = this.deriveKeys(masterKey, salt, context, userId)
      
      // Create cipher
      const cipher = crypto.createCipheriv(ENCRYPTION_CONFIG.algorithm, derivedKeys.encryptionKey, iv) as crypto.CipherGCM
      
      // Encrypt data
      let ciphertext = cipher.update(plaintext, 'utf8')
      ciphertext = Buffer.concat([ciphertext, cipher.final()])
      
      // Get authentication tag
      const authTag = cipher.getAuthTag()
      
      // Create encrypted data structure
      const encryptedData: EncryptedDataFormat = {
        algorithm: ENCRYPTION_CONFIG.algorithm,
        salt: salt.toString('hex'),
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        ciphertext: ciphertext.toString('hex'),
        version: this.version
      }
      
      return JSON.stringify(encryptedData)
    } catch (error) {
      throw new Error(`Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Decrypt data using AES-256-GCM
   */
  async decryptData(
    encryptedData: string,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<string> {
    if (!encryptedData || typeof encryptedData !== 'string') {
      throw new Error('Invalid encrypted data: must be a non-empty string')
    }
    
    try {
      // Parse encrypted data
      const parsed: EncryptedDataFormat = JSON.parse(encryptedData)
      
      // Validate format
      if (parsed.algorithm !== ENCRYPTION_CONFIG.algorithm) {
        throw new Error(`Unsupported algorithm: ${parsed.algorithm}`)
      }
      
      if (parsed.version !== this.version) {
        throw new Error(`Unsupported version: ${parsed.version}`)
      }
      
      // Convert hex strings back to buffers
      const salt = Buffer.from(parsed.salt, 'hex')
      const iv = Buffer.from(parsed.iv, 'hex')
      const authTag = Buffer.from(parsed.authTag, 'hex')
      const ciphertext = Buffer.from(parsed.ciphertext, 'hex')
      
      // Generate master key
      const masterKey = await this.generateMasterKey(userId, context, additionalEntropy)
      
      // Derive keys
      const derivedKeys = this.deriveKeys(masterKey, salt, context, userId)
      
      // Create decipher
      const decipher = crypto.createDecipheriv(parsed.algorithm, derivedKeys.encryptionKey, iv) as crypto.DecipherGCM
      decipher.setAuthTag(authTag)
      
      // Decrypt data
      let plaintext = decipher.update(ciphertext)
      plaintext = Buffer.concat([plaintext, decipher.final()])
      
      return plaintext.toString('utf8')
    } catch (error) {
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Generate a unique encryption salt for database storage
   */
  generateEncryptionSalt(userId: string, resourceId: string, context: EncryptionContext): string {
    return this.hashData(`${userId}:${resourceId}:${context}:${this.version}:salt`)
  }
  
  /**
   * Encrypt JSON data
   */
  async encryptJSON<T>(
    data: T,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<string> {
    const jsonString = JSON.stringify(data)
    return this.encryptData(jsonString, userId, context, additionalEntropy)
  }
  
  /**
   * Decrypt JSON data
   */
  async decryptJSON<T>(
    encryptedData: string,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<T> {
    const jsonString = await this.decryptData(encryptedData, userId, context, additionalEntropy)
    return JSON.parse(jsonString) as T
  }
  
  /**
   * Batch encrypt multiple strings
   */
  async encryptBatch(
    items: Array<{ data: string; entropy?: string }>,
    userId: string,
    context: EncryptionContext
  ): Promise<string[]> {
    return Promise.all(
      items.map(item => this.encryptData(item.data, userId, context, item.entropy))
    )
  }
  
  /**
   * Batch decrypt multiple strings
   */
  async decryptBatch(
    encryptedItems: Array<{ data: string; entropy?: string }>,
    userId: string,
    context: EncryptionContext
  ): Promise<string[]> {
    return Promise.all(
      encryptedItems.map(item => this.decryptData(item.data, userId, context, item.entropy))
    )
  }
}

// Export singleton instance
export const modernEncryption = new ModernEncryptionService()

export default modernEncryption 