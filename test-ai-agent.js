// Simple test script to verify AI agent comprehensive data fetching
const { aiAgentService } = require('./src/lib/ai-agent.ts')

async function testAIAgent() {
  try {
    console.log('Testing AI Agent comprehensive data fetching...')
    
    // Test with a sample user ID and message
    const testUserId = 'test-user-123'
    const testMessage = 'Show me my emails, calendar events, and tasks for today'
    
    console.log('Generating AI response with comprehensive data...')
    const response = await aiAgentService.generateResponse(testUserId, testMessage)
    
    console.log('AI Agent Response:')
    console.log('Response:', response.response)
    console.log('Actions:', response.actions?.length || 0)
    console.log('Data Context Available:', !!response.dataContext)
    
    if (response.actions && response.actions.length > 0) {
      console.log('Actions to execute:')
      response.actions.forEach((action, index) => {
        console.log(`  ${index + 1}. ${action.type}: ${action.description}`)
      })
    }
    
    console.log('✅ AI Agent test completed successfully!')
    
  } catch (error) {
    console.error('❌ AI Agent test failed:', error)
    console.error('Error details:', error.message)
  }
}

// Run the test
testAIAgent()
