import { z } from 'zod'
import { NextRequest } from 'next/server'

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validates request body against a Zod schema
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; error: string }> {
  try {
    const body = await request.json()
    const result = schema.safeParse(body)
    
    if (result.success) {
      return { success: true, data: result.data }
    } else {
      const errorMessage = result.error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join(', ')
      return { success: false, error: errorMessage }
    }
  } catch (error) {
    return { success: false, error: 'Invalid JSON in request body' }
  }
}

/**
 * Validates URL search parameters against a Zod schema
 */
export function validateSearchParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; error: string } {
  try {
    const params: Record<string, any> = {}
    
    // Convert URLSearchParams to object
    for (const [key, value] of searchParams.entries()) {
      // Handle numeric values
      if (/^\d+$/.test(value)) {
        params[key] = parseInt(value, 10)
      }
      // Handle boolean values
      else if (value === 'true' || value === 'false') {
        params[key] = value === 'true'
      }
      // Handle array values (comma-separated)
      else if (value.includes(',')) {
        params[key] = value.split(',').map(v => v.trim())
      }
      // Handle regular string values
      else {
        params[key] = value
      }
    }
    
    const result = schema.safeParse(params)
    
    if (result.success) {
      return { success: true, data: result.data }
    } else {
      const errorMessage = result.error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join(', ')
      return { success: false, error: errorMessage }
    }
  } catch (error) {
    return { success: false, error: 'Invalid search parameters' }
  }
}

/**
 * Validates form data against a Zod schema
 */
export function validateFormData<T>(
  formData: FormData,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; error: string } {
  try {
    const data: Record<string, any> = {}
    
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        data[key] = value
      } else {
        // Handle numeric values
        if (/^\d+$/.test(value)) {
          data[key] = parseInt(value, 10)
        }
        // Handle boolean values
        else if (value === 'true' || value === 'false') {
          data[key] = value === 'true'
        }
        // Handle array values (multiple entries with same key)
        else if (data[key]) {
          if (Array.isArray(data[key])) {
            data[key].push(value)
          } else {
            data[key] = [data[key], value]
          }
        }
        // Handle regular string values
        else {
          data[key] = value
        }
      }
    }
    
    const result = schema.safeParse(data)
    
    if (result.success) {
      return { success: true, data: result.data }
    } else {
      const errorMessage = result.error.errors
        .map(err => `${err.path.join('.')}: ${err.message}`)
        .join(', ')
      return { success: false, error: errorMessage }
    }
  } catch (error) {
    return { success: false, error: 'Invalid form data' }
  }
}

/**
 * Creates a validation middleware for API routes
 */
export function createValidationMiddleware<T>(schema: z.ZodSchema<T>) {
  return async (request: NextRequest) => {
    const validation = await validateRequestBody(request, schema)
    if (!validation.success) {
      return {
        isValid: false,
        error: validation.error,
        data: null
      }
    }
    return {
      isValid: true,
      error: null,
      data: validation.data
    }
  }
}

/**
 * Validates and transforms email addresses
 */
export function validateEmailAddresses(emails: string[]): {
  valid: string[]
  invalid: string[]
} {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  const valid: string[] = []
  const invalid: string[] = []
  
  emails.forEach(email => {
    const trimmedEmail = email.trim().toLowerCase()
    if (emailRegex.test(trimmedEmail)) {
      valid.push(trimmedEmail)
    } else {
      invalid.push(email)
    }
  })
  
  return { valid, invalid }
}

/**
 * Validates date strings and converts to Date objects
 */
export function validateAndParseDates(
  startDate?: string,
  endDate?: string
): {
  success: true
  startDate: Date
  endDate: Date
} | {
  success: false
  error: string
} {
  try {
    if (!startDate || !endDate) {
      return { success: false, error: 'Both start and end dates are required' }
    }
    
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return { success: false, error: 'Invalid date format' }
    }
    
    if (start > end) {
      return { success: false, error: 'Start date must be before end date' }
    }
    
    return { success: true, startDate: start, endDate: end }
  } catch (error) {
    return { success: false, error: 'Date validation failed' }
  }
}

/**
 * Sanitizes and validates HTML content
 */
export function sanitizeHtmlContent(html: string): string {
  // Remove script tags and event handlers
  let sanitized = html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/data:/gi, '')
  
  // Limit content size (1MB)
  if (sanitized.length > 1048576) {
    throw new Error('HTML content exceeds maximum size limit')
  }
  
  return sanitized.trim()
}

/**
 * Validates file uploads
 */
export function validateFileUpload(
  file: File,
  options: {
    maxSize?: number // in bytes
    allowedTypes?: string[]
    maxFiles?: number
  } = {}
): { success: true } | { success: false; error: string } {
  const {
    maxSize = 25 * 1024 * 1024, // 25MB default
    allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
  } = options
  
  if (file.size > maxSize) {
    return {
      success: false,
      error: `File size exceeds maximum limit of ${Math.round(maxSize / 1024 / 1024)}MB`
    }
  }
  
  if (!allowedTypes.includes(file.type)) {
    return {
      success: false,
      error: `File type ${file.type} is not allowed`
    }
  }
  
  return { success: true }
}

/**
 * Validates pagination parameters
 */
export function validatePaginationParams(searchParams: URLSearchParams): {
  page: number
  limit: number
  pageToken?: string
} {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10))
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '20', 10)))
  const pageToken = searchParams.get('pageToken') || undefined
  
  return { page, limit, pageToken }
}

/**
 * Creates a standardized error response
 */
export function createValidationErrorResponse(error: string, status = 400) {
  return {
    success: false,
    error,
    message: 'Validation failed',
    status
  }
}

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(data: T, message?: string) {
  return {
    success: true,
    data,
    message: message || 'Operation completed successfully'
  }
}
