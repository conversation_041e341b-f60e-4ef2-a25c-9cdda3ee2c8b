import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/gmail/client'
import { createMeetingSpace, getMeetingSpace } from '@/lib/meet'

/**
 * POST /api/calendar/meet-integration - Create calendar event with Meet space
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const eventData = await request.json()
    
    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Create Meet space first if requested
      let meetSpace = null
      if (eventData.createMeetSpace) {
        try {
          meetSpace = await createMeetingSpace(session.user.id, {
            accessType: eventData.meetAccessType || 'OPEN',
            entryPointAccess: eventData.meetEntryPointAccess || 'ALL'
          })
        } catch (meetError) {
          console.error('Failed to create Meet space:', meetError)
          // Continue with regular Meet link creation if Meet space creation fails
        }
      }

      // Prepare calendar event
      const googleEvent: any = {
        summary: eventData.summary || eventData.title,
        description: eventData.description,
        location: eventData.location,
        start: eventData.start,
        end: eventData.end,
        attendees: eventData.attendees,
        reminders: eventData.reminders
      }

      // Handle all-day events
      if (eventData.isAllDay) {
        googleEvent.start = {
          date: eventData.startDate ? new Date(eventData.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        }
        googleEvent.end = {
          date: eventData.endDate ? new Date(eventData.endDate).toISOString().split('T')[0] : 
                new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        }
      }

      // Add conference data
      if (eventData.addMeet || meetSpace) {
        if (meetSpace) {
          // Use the created Meet space
          googleEvent.conferenceData = {
            conferenceSolution: {
              key: { type: 'hangoutsMeet' },
              name: 'Google Meet',
              iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
            },
            conferenceId: meetSpace.name,
            entryPoints: [{
              entryPointType: 'video',
              uri: meetSpace.meetingUri,
              label: meetSpace.meetingCode
            }]
          }

          // Add Meet space info to description
          const meetInfo = `\n\n--- Google Meet Information ---\nMeeting Link: ${meetSpace.meetingUri}\nMeeting Code: ${meetSpace.meetingCode}\nAccess Type: ${eventData.meetAccessType || 'OPEN'}`
          googleEvent.description = (googleEvent.description || '') + meetInfo
        } else {
          // Fallback to regular Meet link creation
          googleEvent.conferenceData = {
            createRequest: {
              requestId: `meet-${Date.now()}`,
              conferenceSolutionKey: { type: 'hangoutsMeet' }
            }
          }
        }
      }

      const insertParams: any = {
        calendarId: 'primary',
        requestBody: googleEvent,
        sendUpdates: 'all',
        conferenceDataVersion: 1
      }

      const response = await calendar.events.insert(insertParams)

      // Return enhanced response with Meet space info
      const result = {
        event: response.data,
        meetSpace: meetSpace ? {
          name: meetSpace.name,
          meetingUri: meetSpace.meetingUri,
          meetingCode: meetSpace.meetingCode,
          accessType: eventData.meetAccessType || 'OPEN',
          entryPointAccess: eventData.meetEntryPointAccess || 'ALL'
        } : null,
        message: 'Event created successfully with Meet integration'
      }

      return NextResponse.json(result)

    } catch (error) {
      console.error('Google Calendar/Meet integration error:', error)
      return NextResponse.json(
        { error: 'Failed to create calendar event with Meet integration. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar/Meet integration error:', error)
    return NextResponse.json(
      { error: 'Failed to create calendar event with Meet integration' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/calendar/meet-integration - Get calendar events with Meet space details
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')
    
    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Get the calendar event
      const eventResponse = await calendar.events.get({
        calendarId: 'primary',
        eventId: eventId
      })

      const event = eventResponse.data
      let meetSpaceDetails = null

      // If the event has conference data, try to get Meet space details
      if (event.conferenceData?.conferenceId) {
        try {
          meetSpaceDetails = await getMeetingSpace(session.user.id, event.conferenceData.conferenceId)
        } catch (meetError) {
          console.error('Failed to get Meet space details:', meetError)
          // Continue without Meet space details
        }
      }

      return NextResponse.json({
        event,
        meetSpaceDetails,
        message: 'Event with Meet integration retrieved successfully'
      })

    } catch (error) {
      console.error('Google Calendar/Meet integration get error:', error)
      return NextResponse.json(
        { error: 'Failed to retrieve calendar event with Meet integration.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar/Meet integration get error:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve calendar event with Meet integration' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/calendar/meet-integration - Update calendar event with Meet space management
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')
    
    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    const eventData = await request.json()
    
    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Get existing event first
      const existingEvent = await calendar.events.get({
        calendarId: 'primary',
        eventId: eventId
      })

      // Update the event with new data
      const updatedEvent = {
        ...existingEvent.data,
        summary: eventData.summary || eventData.title || existingEvent.data.summary,
        description: eventData.description !== undefined ? eventData.description : existingEvent.data.description,
        location: eventData.location !== undefined ? eventData.location : existingEvent.data.location,
      }

      // Update time fields if provided
      if (eventData.startDate || eventData.start) {
        updatedEvent.start = {
          dateTime: eventData.startDate || eventData.start?.dateTime,
          timeZone: eventData.timeZone || 'UTC',
        }
      }

      if (eventData.endDate || eventData.end) {
        updatedEvent.end = {
          dateTime: eventData.endDate || eventData.end?.dateTime,
          timeZone: eventData.timeZone || 'UTC',
        }
      }

      // Update attendees if provided
      if (eventData.attendees) {
        updatedEvent.attendees = eventData.attendees.filter((attendee: any) => {
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return email && email.trim() && email.includes('@')
        }).map((attendee: any) => {
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return { email: email.trim() }
        })
      }

      // Handle Meet space updates
      let meetSpace = null
      if (eventData.updateMeetSpace && eventData.createMeetSpace) {
        try {
          meetSpace = await createMeetingSpace(session.user.id, {
            accessType: eventData.meetAccessType || 'OPEN',
            entryPointAccess: eventData.meetEntryPointAccess || 'ALL'
          })

          // Update conference data with new Meet space
          updatedEvent.conferenceData = {
            conferenceSolution: {
              key: { type: 'hangoutsMeet' },
              name: 'Google Meet',
              iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
            },
            conferenceId: meetSpace.name,
            entryPoints: [{
              entryPointType: 'video',
              uri: meetSpace.meetingUri,
              label: meetSpace.meetingCode
            }]
          }

          // Update description with new Meet info
          const meetInfo = `\n\n--- Google Meet Information ---\nMeeting Link: ${meetSpace.meetingUri}\nMeeting Code: ${meetSpace.meetingCode}\nAccess Type: ${eventData.meetAccessType || 'OPEN'}`
          updatedEvent.description = (updatedEvent.description || '').replace(/\n\n--- Google Meet Information ---[\s\S]*$/, '') + meetInfo
        } catch (meetError) {
          console.error('Failed to create new Meet space:', meetError)
        }
      }

      const response = await calendar.events.update({
        calendarId: 'primary',
        eventId: eventId,
        requestBody: updatedEvent,
        sendUpdates: 'all',
        conferenceDataVersion: 1
      })

      return NextResponse.json({
        event: response.data,
        meetSpace: meetSpace ? {
          name: meetSpace.name,
          meetingUri: meetSpace.meetingUri,
          meetingCode: meetSpace.meetingCode,
          accessType: eventData.meetAccessType || 'OPEN',
          entryPointAccess: eventData.meetEntryPointAccess || 'ALL'
        } : null,
        message: 'Event updated successfully with Meet integration'
      })

    } catch (error) {
      console.error('Google Calendar/Meet integration update error:', error)
      return NextResponse.json(
        { error: 'Failed to update calendar event with Meet integration.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar/Meet integration update error:', error)
    return NextResponse.json(
      { error: 'Failed to update calendar event with Meet integration' },
      { status: 500 }
    )
  }
}
