"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { 
  FileText, 
  Search, 
  Trash2, 
  Send, 
  Edit, 
  Clock,
  Mail,
  RefreshCw
} from "lucide-react"

interface Draft {
  id: string
  subject: string
  to: string
  cc?: string
  bcc?: string
  snippet: string
  lastModified: Date
  threadId?: string
}

interface DraftManagerProps {
  onDraftEdit?: (draft: Draft) => void
  onDraftSend?: (draftId: string) => void
  onDraftDelete?: (draftId: string) => void
}

export function DraftManager({ onDraftEdit, onDraftSend, onDraftDelete }: DraftManagerProps) {
  const [drafts, setDrafts] = useState<Draft[]>([])
  const [filteredDrafts, setFilteredDrafts] = useState<Draft[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedDraft, setSelectedDraft] = useState<Draft | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    fetchDrafts()
  }, [])

  useEffect(() => {
    // Filter drafts based on search query
    if (!searchQuery.trim()) {
      setFilteredDrafts(drafts)
    } else {
      const query = searchQuery.toLowerCase()
      setFilteredDrafts(
        drafts.filter(draft => 
          draft.subject.toLowerCase().includes(query) ||
          draft.to.toLowerCase().includes(query) ||
          draft.snippet.toLowerCase().includes(query)
        )
      )
    }
  }, [drafts, searchQuery])

  const fetchDrafts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/gmail/drafts')
      
      if (!response.ok) {
        throw new Error('Failed to fetch drafts')
      }

      const data = await response.json()
      
      // Transform Gmail drafts to our format
      const transformedDrafts: Draft[] = data.emails?.map((email: any) => ({
        id: email.id,
        subject: email.subject || 'No Subject',
        to: email.to || '',
        cc: email.cc || '',
        bcc: email.bcc || '',
        snippet: email.snippet || '',
        lastModified: new Date(email.date),
        threadId: email.threadId
      })) || []

      setDrafts(transformedDrafts)
    } catch (err) {
      console.error('Error fetching drafts:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSendDraft = async (draftId: string) => {
    try {
      setActionLoading(draftId)
      
      const response = await fetch('/api/gmail/drafts/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ draftId })
      })

      if (response.ok) {
        setDrafts(prev => prev.filter(draft => draft.id !== draftId))
        onDraftSend?.(draftId)
      } else {
        throw new Error('Failed to send draft')
      }
    } catch (err) {
      console.error('Error sending draft:', err)
    } finally {
      setActionLoading(null)
    }
  }

  const handleDeleteDraft = async (draftId: string) => {
    try {
      setActionLoading(draftId)
      
      const response = await fetch('/api/gmail/drafts/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ draftId })
      })

      if (response.ok) {
        setDrafts(prev => prev.filter(draft => draft.id !== draftId))
        onDraftDelete?.(draftId)
        setShowDeleteDialog(false)
        setSelectedDraft(null)
      } else {
        throw new Error('Failed to delete draft')
      }
    } catch (err) {
      console.error('Error deleting draft:', err)
    } finally {
      setActionLoading(null)
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  const confirmDelete = (draft: Draft) => {
    setSelectedDraft(draft)
    setShowDeleteDialog(true)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Draft Manager</span>
            </CardTitle>
            <CardDescription>
              Manage your unsent email drafts
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchDrafts}
            disabled={loading}
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search drafts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Drafts List */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading drafts...</span>
          </div>
        ) : filteredDrafts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {searchQuery ? 'No drafts match your search' : 'No drafts found'}
          </div>
        ) : (
          <div className="space-y-3">
            {filteredDrafts.map((draft) => (
              <div key={draft.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <Mail className="h-4 w-4 text-gray-500 flex-shrink-0" />
                      <span className="font-medium text-sm text-gray-900 truncate">
                        {draft.subject}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {formatTimeAgo(draft.lastModified)}
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>To: {draft.to || 'No recipients'}</div>
                      {draft.cc && <div>CC: {draft.cc}</div>}
                      {draft.bcc && <div>BCC: {draft.bcc}</div>}
                    </div>
                    {draft.snippet && (
                      <div className="text-xs text-gray-500 mt-2 line-clamp-2">
                        {draft.snippet}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onDraftEdit?.(draft)}
                      className="text-blue-600 border-blue-200 hover:bg-blue-50"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleSendDraft(draft.id)}
                      disabled={actionLoading === draft.id}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      {actionLoading === draft.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4 mr-1" />
                      )}
                      Send
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => confirmDelete(draft)}
                      disabled={actionLoading === draft.id}
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Draft</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this draft? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedDraft && (
            <div className="py-4">
              <div className="text-sm font-medium">{selectedDraft.subject}</div>
              <div className="text-sm text-gray-600">To: {selectedDraft.to}</div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => selectedDraft && handleDeleteDraft(selectedDraft.id)}
              disabled={actionLoading === selectedDraft?.id}
            >
              {actionLoading === selectedDraft?.id ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Delete Draft
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
