"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@heroui/date-picker"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, BarChart, Bar } from "recharts"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  Mail,
  RefreshCw,
  Trash2,
  Archive,
  UserX,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  TrendingUp,
  MoreHorizontal,
  Calendar,
  BarChart3,
  Eye,
  Tag,
  Ban,
  Inbox,
  Settings,
  X
} from "lucide-react"
import { format, subDays, isSameDay, isBefore, isAfter, addDays } from "date-fns"

interface SenderStats {
  senderEmail: string
  senderName: string
  totalEmails: number
  unreadEmails: number
  readEmails: number
  openRate: number
  readRate: number
  latestEmail: string
  canUnsubscribe: boolean
}

interface OverallStats {
  totalEmails: number
  totalUnread: number
  totalRead: number
  totalSenders: number
}

interface DetailedStats {
  senderEmail: string
  senderName: string
  dailyStats: Array<{
    date: string
    emailCount: number
  }>
  emails: Array<{
    id: string
    subject: string
    snippet: string
    date: string
    isRead: boolean
    isArchived: boolean
  }>
  totalEmails: number
  readRate: number
  archiveRate: number
}

interface DateRange {
  from: Date
  to: Date
}

export default function SubscriptionsPage() {
  const [senderStats, setSenderStats] = useState<SenderStats[]>([])
  const [selectedSenders, setSelectedSenders] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)
  const [bulkActionLoading, setBulkActionLoading] = useState(false)
  const [showBulkDialog, setShowBulkDialog] = useState(false)
  const [bulkAction, setBulkAction] = useState<'archive' | 'delete'>('archive')
  const [requiresReconnection, setRequiresReconnection] = useState(false)
  
  // Stats and date range state
  const [showStatsDialog, setShowStatsDialog] = useState(false)
  const [selectedSenderForStats, setSelectedSenderForStats] = useState<string | null>(null)
  const [detailedStats, setDetailedStats] = useState<DetailedStats | null>(null)
  const [statsLoading, setStatsLoading] = useState(false)
  const [dateRange, setDateRange] = useState<DateRange>({
    from: subDays(new Date(), 3),
    to: new Date()
  })

  useEffect(() => {
    fetchSenderStats()
  }, [dateRange])

  const fetchSenderStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/gmail/subscriptions?' + new URLSearchParams({
        startDate: format(dateRange.from, 'yyyy-MM-dd'),
        endDate: format(dateRange.to, 'yyyy-MM-dd'),
        getAllData: 'true' // No limits
      }))
      
      if (response.ok) {
        const data = await response.json()
        setSenderStats(data.senderStats)
        setRequiresReconnection(false)
      } else {
        const error = await response.json()
        if (error.requiresReconnection) {
          setRequiresReconnection(true)
        }
        console.error('Error fetching sender stats:', error)
      }
    } catch (error) {
      console.error('Error fetching sender stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchDetailedStats = async (senderEmail: string) => {
    try {
      setStatsLoading(true)
      const response = await fetch('/api/gmail/subscriptions/stats?' + new URLSearchParams({
        senderEmail,
        startDate: format(dateRange.from, 'yyyy-MM-dd'),
        endDate: format(dateRange.to, 'yyyy-MM-dd'),
        getAllData: 'true'
      }))
      
      if (response.ok) {
        const data = await response.json()
        setDetailedStats(data)
      } else {
        console.error('Error fetching detailed stats')
      }
    } catch (error) {
      console.error('Error fetching detailed stats:', error)
    } finally {
      setStatsLoading(false)
    }
  }

  const handleViewStats = (senderEmail: string) => {
    setSelectedSenderForStats(senderEmail)
    setShowStatsDialog(true)
    fetchDetailedStats(senderEmail)
  }

  const handleSenderAction = async (senderEmail: string, action: string) => {
    try {
      const response = await fetch('/api/gmail/subscriptions/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          senderEmail,
          action,
          dateRange: {
            startDate: format(dateRange.from, 'yyyy-MM-dd'),
            endDate: format(dateRange.to, 'yyyy-MM-dd')
          }
        })
      })

      if (response.ok) {
        fetchSenderStats()
        console.log(`Action ${action} completed for ${senderEmail}`)
      } else {
        console.error('Failed to perform action')
      }
    } catch (error) {
      console.error('Error performing action:', error)
    }
  }

  const toggleSenderSelection = (senderEmail: string) => {
    const newSelection = new Set(selectedSenders)
    if (newSelection.has(senderEmail)) {
      newSelection.delete(senderEmail)
    } else {
      newSelection.add(senderEmail)
    }
    setSelectedSenders(newSelection)
  }

  const selectAllSenders = () => {
    const allCanUnsubscribe = senderStats
      .filter(sender => sender.canUnsubscribe)
      .map(sender => sender.senderEmail)
    setSelectedSenders(new Set(allCanUnsubscribe))
  }

  const clearSelection = () => {
    setSelectedSenders(new Set())
  }

  const handleBulkAction = async () => {
    if (selectedSenders.size === 0) return

    setBulkActionLoading(true)
    try {
      const response = await fetch('/api/gmail/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'bulk_unsubscribe',
          senderEmails: Array.from(selectedSenders),
          operation: bulkAction
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Bulk action completed:', result)
        
        await fetchSenderStats()
        clearSelection()
        setShowBulkDialog(false)
        
        alert(`Successfully ${bulkAction === 'delete' ? 'deleted' : 'archived'} emails from ${selectedSenders.size} senders`)
      } else {
        console.error('Failed to perform bulk action')
        alert('Failed to perform bulk action')
      }
    } catch (error) {
      console.error('Error performing bulk action:', error)
      alert('Error performing bulk action')
    } finally {
      setBulkActionLoading(false)
    }
  }

  const getOpenRateBadgeColor = (rate: number) => {
    if (rate >= 70) return "bg-green-50 text-green-700 border-green-200"
    if (rate >= 40) return "bg-yellow-50 text-yellow-700 border-yellow-200"
    return "bg-red-50 text-red-700 border-red-200"
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  // Chart component for stats modal
  const StatsChart = ({ dailyStats }: { dailyStats: Array<{ date: string; emailCount: number }> }) => {
    if (!dailyStats || dailyStats.length === 0) {
      return (
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No data available for this period</p>
          </div>
        </div>
      )
    }

    const maxEmails = Math.max(...dailyStats.map(d => d.emailCount))
    const totalEmails = dailyStats.reduce((sum, d) => sum + d.emailCount, 0)
    
    return (
      <div className="space-y-4">
        {/* Chart */}
        <div className="bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="font-semibold text-gray-900">Email Frequency Over Time</h3>
            <div className="text-sm text-gray-500">
              Total: {totalEmails} emails
            </div>
          </div>
          
          <div className="h-64 flex items-end justify-between gap-2 px-2">
            {dailyStats.map((day, index) => {
              const height = maxEmails > 0 ? (day.emailCount / maxEmails) * 100 : 0
              const barColor = `hsl(${120 + (index % 3) * 30}, 70%, 50%)` // Dynamic colors
              
              return (
                <div key={day.date} className="flex-1 flex flex-col items-center">
                  <div className="relative group">
                    <div 
                      className="w-full max-w-12 rounded-t transition-all duration-300 hover:opacity-80"
                      style={{ 
                        height: `${Math.max(height, 2)}%`, 
                        backgroundColor: barColor,
                        minHeight: day.emailCount > 0 ? '4px' : '2px'
                      }}
                      title={`${day.emailCount} emails on ${format(new Date(day.date), 'MMM d')}`}
                    />
                    
                    {/* Tooltip on hover */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                      {day.emailCount} email{day.emailCount !== 1 ? 's' : ''}
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 mt-2 transform rotate-45 origin-bottom-left">
                    {format(new Date(day.date), 'M/d')}
                  </div>
                </div>
              )
            })}
          </div>
          
          {/* Y-axis labels */}
          <div className="flex justify-between text-xs text-gray-500 mt-4 px-2">
            <span>0</span>
            <span>{Math.round(maxEmails / 2)}</span>
            <span>{maxEmails}</span>
          </div>
        </div>
      </div>
    )
  }

  if (requiresReconnection) {
    return (
      <div className="min-h-screen bg-gray-50/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-2xl font-semibold text-gray-900">Subscription Management</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage your email subscriptions and bulk unsubscribe from unwanted senders
            </p>
          </div>

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <AlertCircle className="h-6 w-6 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-amber-900 mb-2">
                  Gmail Connection Required
                </h3>
                <p className="text-sm text-amber-800 mb-4">
                  Please connect your Gmail account to analyze your subscriptions and manage bulk unsubscribe.
                </p>
                <Button
                  onClick={() => window.location.href = '/api/gmail/connect'}
                  className="bg-amber-600 hover:bg-amber-700 text-white"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Connect Gmail Account
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50/30 flex flex-col">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 w-full">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900">Subscription Management</h1>
          <p className="mt-1 text-sm text-gray-600">
            Manage your email subscriptions and bulk unsubscribe from unwanted senders
          </p>
        </div>

        {/* Date Range Picker */}
        <div className="mb-6 flex gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
            <input
              type="date"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm px-3 py-2 border"
              value={format(dateRange.from, 'yyyy-MM-dd')}
              onChange={(e) => {
                const newDate = e.target.value ? new Date(e.target.value) : new Date();
                setDateRange(prev => ({
                  ...prev,
                  from: newDate
                }));
                setTimeout(() => fetchSenderStats(), 100);
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
            <input
              type="date"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm px-3 py-2 border"
              value={format(dateRange.to, 'yyyy-MM-dd')}
              onChange={(e) => {
                const newDate = e.target.value ? new Date(e.target.value) : new Date();
                setDateRange(prev => ({
                  ...prev,
                  to: newDate
                }));
                setTimeout(() => fetchSenderStats(), 100);
              }}
            />
          </div>
        </div>



        {/* Subscriptions List */}
        <Card>
          <CardHeader className="pb-4">
<div>
              <CardTitle>Subscriptions</CardTitle>
              <CardDescription>Manage your email subscriptions</CardDescription>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <RefreshCw className="h-8 w-8 text-gray-400 animate-spin" />
              </div>
            ) : senderStats.length === 0 ? (
              <div className="text-center py-12">
                <Mail className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No subscriptions found</h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  We couldn't find any email subscriptions in the selected date range. Try expanding your date range or check back later.
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-200">
                    <TableHead className="w-12 pl-6">
                      <Checkbox
                        checked={
                          senderStats.filter(s => s.canUnsubscribe).length > 0 &&
                          senderStats.filter(s => s.canUnsubscribe).every(s => selectedSenders.has(s.senderEmail))
                        }
                        onCheckedChange={(checked) => {
                          if (checked) {
                            selectAllSenders()
                          } else {
                            clearSelection()
                          }
                        }}
                      />
                    </TableHead>
                    <TableHead className="font-medium text-gray-900">From</TableHead>
                    <TableHead className="text-center font-medium text-gray-900">Emails</TableHead>
                    <TableHead className="text-center font-medium text-gray-900">Read</TableHead>
                    <TableHead className="text-center font-medium text-gray-900">Archived</TableHead>
                    <TableHead className="text-right font-medium text-gray-900 pr-6">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {senderStats.filter(sender => sender.canUnsubscribe).map((sender) => (
                    <TableRow key={sender.senderEmail} className="border-gray-100 hover:bg-gray-50/50">
                      <TableCell className="pl-6">
                        {sender.canUnsubscribe && (
                          <Checkbox
                            checked={selectedSenders.has(sender.senderEmail)}
                            onCheckedChange={() => toggleSenderSelection(sender.senderEmail)}
                          />
                        )}
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{sender.senderName}</div>
                          <div className="text-sm text-gray-600">{sender.senderEmail}</div>
                          <div className="text-xs text-gray-500">
                            Latest: {formatDate(sender.latestEmail)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex flex-col items-center gap-2">
                          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                            {sender.totalEmails}
                          </Badge>
                          <div className="w-16 bg-gray-200 rounded-full h-1">
                            <div 
                              className="bg-blue-500 h-1 rounded-full" 
                              style={{ width: `${Math.min((sender.totalEmails / Math.max(...senderStats.map(s => s.totalEmails))) * 100, 100)}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex flex-col items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className={getOpenRateBadgeColor(sender.readRate)}
                          >
                            {sender.readRate}%
                          </Badge>
                          <div className="w-16 bg-gray-200 rounded-full h-1">
                            <div 
                              className={`h-1 rounded-full ${
                                sender.readRate >= 70 ? 'bg-green-500' :
                                sender.readRate >= 40 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${sender.readRate}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex flex-col items-center gap-2">
                          <Badge variant="outline" className="bg-gray-50 text-gray-500 border-gray-200">
                            0%
                          </Badge>
                          <div className="w-16 bg-gray-200 rounded-full h-1">
                            <div className="bg-gray-400 h-1 rounded-full" style={{ width: '0%' }} />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right pr-6">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs bg-gray-100 hover:bg-gray-200"
                          >
                            Block
                          </Button>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="outline" size="sm" className="text-xs">
                                Skip Inbox
                                <MoreHorizontal className="h-3 w-3 ml-1" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem onClick={() => handleViewStats(sender.senderEmail)}>
                                <BarChart3 className="h-4 w-4 mr-2" />
                                View stats
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => window.open(`https://mail.google.com/mail/u/0/#search/from:${sender.senderEmail}`, '_blank')}>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View in Gmail
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleSenderAction(sender.senderEmail, 'label_future')}>
                                <Tag className="h-4 w-4 mr-2" />
                                Label future emails
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleSenderAction(sender.senderEmail, 'archive_all')}>
                                <Archive className="h-4 w-4 mr-2" />
                                Archive all
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleSenderAction(sender.senderEmail, 'delete_all')}
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete all
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleSenderAction(sender.senderEmail, 'block')}>
                                <Ban className="h-4 w-4 mr-2" />
                                Block sender
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleSenderAction(sender.senderEmail, 'unsubscribe')}>
                                <UserX className="h-4 w-4 mr-2" />
                                Unsubscribe
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Stats Dialog with Better Chart */}
      <Dialog open={showStatsDialog} onOpenChange={setShowStatsDialog}>
        <DialogContent className="max-w-[96vw] w-[96vw] max-h-[96vh] overflow-y-auto bg-white">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div>
                <DialogTitle className="text-xl">
                  Stats for {detailedStats?.senderName || selectedSenderForStats}
                </DialogTitle>
                <DialogDescription className="mt-1">
                  Email analytics for {format(dateRange.from, 'MMM d')} - {format(dateRange.to, 'MMM d, yyyy')}
                </DialogDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowStatsDialog(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>

          {statsLoading ? (
            <div className="flex items-center justify-center py-16">
              <div className="flex flex-col items-center space-y-3">
                <div className="w-8 h-8 border-3 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                <p className="text-sm text-gray-500">Loading statistics...</p>
              </div>
            </div>
          ) : detailedStats ? (
            <div className="space-y-8 p-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 text-center border border-blue-200">
                  <div className="text-3xl font-bold text-blue-700 mb-2">{detailedStats.totalEmails}</div>
                  <div className="text-sm font-medium text-blue-600">Total Emails</div>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 text-center border border-green-200">
                  <div className="text-3xl font-bold text-green-700 mb-2">{detailedStats.readRate}%</div>
                  <div className="text-sm font-medium text-green-600">Read Rate</div>
                </div>
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 text-center border border-purple-200">
                  <div className="text-3xl font-bold text-purple-700 mb-2">{detailedStats.archiveRate}%</div>
                  <div className="text-sm font-medium text-purple-600">Archive Rate</div>
                </div>
              </div>

              {/* Enhanced Chart */}
              <div className="bg-gray-50 rounded-xl border p-8">
                <h3 className="text-xl font-semibold mb-6 text-gray-800">Email Frequency Over Time</h3>
                {detailedStats.dailyStats && detailedStats.dailyStats.length > 0 ? (
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart 
                        data={detailedStats.dailyStats}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis 
                          dataKey="date" 
                          stroke="#666"
                          fontSize={12}
                          tickFormatter={(value) => {
                            try {
                              const date = new Date(value);
                              return format(date, 'MMM d');
                            } catch {
                              return value;
                            }
                          }}
                        />
                        <YAxis stroke="#666" fontSize={12} />
                        <ChartTooltip 
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="bg-white p-3 border rounded-lg shadow-lg">
                                  <p className="font-medium">
                                    {label ? format(new Date(label), 'MMM d, yyyy') : 'Date'}
                                  </p>
                                  <p className="text-blue-600">
                                    Emails: {payload[0]?.value || 0}
                                  </p>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Bar 
                          dataKey="emailCount" 
                          fill="#3b82f6" 
                          radius={[4, 4, 0, 0]}
                          name="Emails"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : detailedStats.totalEmails > 0 ? (
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart 
                        data={[{
                          date: format(new Date(), 'yyyy-MM-dd'),
                          emailCount: detailedStats.totalEmails,
                          label: 'Total Emails'
                        }]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis 
                          dataKey="label" 
                          stroke="#666"
                          fontSize={12}
                        />
                        <YAxis stroke="#666" fontSize={12} />
                        <ChartTooltip 
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="bg-white p-3 border rounded-lg shadow-lg">
                                  <p className="font-medium">{label}</p>
                                  <p className="text-blue-600">
                                    Count: {payload[0]?.value || 0}
                                  </p>
                                </div>
                              );
                            }
                            return null;
                          }}
                        />
                        <Bar 
                          dataKey="emailCount" 
                          fill="#3b82f6" 
                          radius={[4, 4, 0, 0]}
                          name="Emails"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="h-80 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <div className="text-4xl mb-2">📊</div>
                      <p>No data available for the selected date range</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Emails List */}
              <div className="bg-white rounded-xl border p-6">
                <h3 className="text-xl font-semibold mb-4 text-gray-800">Email Details</h3>
                <Tabs defaultValue="unarchived" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 mb-4">
                    <TabsTrigger value="unarchived">Unarchived ({detailedStats.emails?.filter(e => !e.isArchived).length || 0})</TabsTrigger>
                    <TabsTrigger value="all">All ({detailedStats.emails?.length || 0})</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="unarchived" className="mt-4">
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {detailedStats.emails
                        .filter(email => !email.isArchived)
                        .map((email) => (
                          <div key={email.id} className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <Checkbox />
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-gray-900 truncate">
                                {email.subject}
                              </div>
                              <div className="text-sm text-gray-600 truncate mt-1">
                                {email.snippet}
                              </div>
                              <div className="text-xs text-gray-500 mt-2">
                                {format(new Date(email.date), 'MMM d, yyyy h:mm a')}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {!email.isRead && (
                                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                  Unread
                                </Badge>
                              )}
                            </div>
                          </div>
                        ))}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="all" className="mt-4">
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {detailedStats.emails.map((email) => (
                        <div key={email.id} className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                          <Checkbox />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-gray-900 truncate">
                              {email.subject}
                            </div>
                            <div className="text-sm text-gray-600 truncate mt-1">
                              {email.snippet}
                            </div>
                            <div className="text-xs text-gray-500 mt-2">
                              {format(new Date(email.date), 'MMM d, yyyy h:mm a')}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {!email.isRead && (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                Unread
                              </Badge>
                            )}
                            {email.isArchived && (
                              <Badge variant="outline" className="bg-gray-50 text-gray-500 border-gray-200">
                                Archived
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          ) : (
            <div className="py-12 text-center">
              <AlertCircle className="h-8 w-8 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">No statistics available for this sender</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Bulk Action Confirmation Dialog */}
      <Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Bulk Action</DialogTitle>
            <DialogDescription>
              This will {bulkAction === 'delete' ? 'permanently delete' : 'archive'} all emails from {selectedSenders.size} selected sender{selectedSenders.size !== 1 ? 's' : ''}.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium text-amber-900 mb-1">Warning</p>
                  <p className="text-amber-800">
                    This action cannot be undone. {bulkAction === 'delete' 
                      ? 'Deleted emails will be permanently removed from your Gmail account.' 
                      : 'Archived emails will be moved to your All Mail folder.'}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="text-sm text-gray-600">
              <span className="font-medium">Selected senders:</span>
              <div className="mt-1 text-gray-500">
                {Array.from(selectedSenders).slice(0, 3).join(', ')}
                {selectedSenders.size > 3 && ` and ${selectedSenders.size - 3} more...`}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowBulkDialog(false)}
              disabled={bulkActionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleBulkAction}
              disabled={bulkActionLoading}
              className={bulkAction === 'delete' ? 'bg-red-600 hover:bg-red-700 text-white' : ''}
            >
              {bulkActionLoading && <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
              {bulkAction === 'delete' ? 'Delete' : 'Archive'} Emails
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 