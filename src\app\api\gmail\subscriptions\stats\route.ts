import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { gmail_v1, google } from 'googleapis'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return Response.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user has Gmail connected
    if (!user.gmailRefreshToken || !user.gmailConnected) {
      return Response.json({ 
        error: 'Gmail account not connected. Please connect your Gmail account first.',
        requiresReconnection: true 
      }, { status: 400 })
    }

    const searchParams = request.nextUrl.searchParams
    const senderEmail = searchParams.get('senderEmail')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const getAllData = searchParams.get('getAllData') === 'true'

    if (!senderEmail) {
      return Response.json({ error: 'Sender email is required' }, { status: 400 })
    }

    // Set up OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET
    )
    oauth2Client.setCredentials({
      refresh_token: user.gmailRefreshToken,
    })

    const gmail = google.gmail({ version: 'v1', auth: oauth2Client })

    // Build search query
    let query = `from:${senderEmail}`
    if (startDate && endDate) {
      query += ` after:${startDate} before:${endDate}`
    }

    // Get all emails from this sender in the date range
    const allMessageIds: string[] = []
    let nextPageToken: string | undefined

    // Fetch all message IDs (no limits if getAllData is true)
    do {
      const emailsResponse = await gmail.users.messages.list({
        userId: 'me',
        q: query,
        maxResults: getAllData ? 500 : 100, // Fetch more per page if getting all data
        pageToken: nextPageToken
      })

      const messageIds = emailsResponse.data.messages?.map(msg => msg.id).filter(Boolean) || []
      allMessageIds.push(...messageIds as string[])
      
      nextPageToken = emailsResponse.data.nextPageToken || undefined
      
      // If not getting all data, limit to reasonable amount
      if (!getAllData && allMessageIds.length >= 200) {
        break
      }
      
    } while (nextPageToken && getAllData) // Continue only if getAllData is true
    
    if (allMessageIds.length === 0) {
      return Response.json({
        senderEmail,
        senderName: senderEmail.split('@')[0],
        dailyStats: [],
        emails: [],
        totalEmails: 0,
        readRate: 0,
        archiveRate: 0
      })
    }

    // Get detailed information for each email
    const batchSize = 100
    const allEmailDetails = []
    
    for (let i = 0; i < allMessageIds.length; i += batchSize) {
      const batch = allMessageIds.slice(i, i + batchSize)
      
      const emailDetails = await Promise.all(
        batch.map(async (messageId) => {
          try {
            const email = await gmail.users.messages.get({
              userId: 'me',
              id: messageId,
              format: 'metadata',
              metadataHeaders: ['Subject', 'Date', 'From']
            })

            const headers = email.data.payload?.headers || []
            const subject = headers.find(h => h.name === 'Subject')?.value || '(No Subject)'
            const dateHeader = headers.find(h => h.name === 'Date')?.value
            const from = headers.find(h => h.name === 'From')?.value || senderEmail

            // Get snippet
            const snippetResponse = await gmail.users.messages.get({
              userId: 'me',
              id: messageId,
              format: 'minimal'
            })

            const isRead = !email.data.labelIds?.includes('UNREAD')
            const isArchived = !email.data.labelIds?.includes('INBOX')

            return {
              id: messageId,
              subject,
              snippet: snippetResponse.data.snippet || '',
              date: dateHeader ? new Date(dateHeader).toISOString() : new Date().toISOString(),
              isRead,
              isArchived
            }
          } catch (error) {
            console.error('Error fetching email details:', error)
            return null
          }
        })
      )
      
      const validBatchEmails = emailDetails.filter(email => email !== null)
      allEmailDetails.push(...validBatchEmails)
      
      // Add small delay between batches to avoid rate limiting
      if (i + batchSize < allMessageIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    // Calculate daily statistics
    const dailyStats = new Map<string, number>()
    
    allEmailDetails.forEach(email => {
      const date = new Date(email.date).toISOString().split('T')[0]
      dailyStats.set(date, (dailyStats.get(date) || 0) + 1)
    })

    // Convert to array and sort by date
    const dailyStatsArray = Array.from(dailyStats.entries())
      .map(([date, emailCount]) => ({ date, emailCount }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    // Calculate rates
    const totalEmails = allEmailDetails.length
    const readEmails = allEmailDetails.filter(email => email.isRead).length
    const archivedEmails = allEmailDetails.filter(email => email.isArchived).length

    const readRate = totalEmails > 0 ? Math.round((readEmails / totalEmails) * 100) : 0
    const archiveRate = totalEmails > 0 ? Math.round((archivedEmails / totalEmails) * 100) : 0

    // Extract sender name from sender email
    const senderName = senderEmail.split('@')[0]

    return Response.json({
      senderEmail,
      senderName,
      dailyStats: dailyStatsArray,
      emails: allEmailDetails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()),
      totalEmails,
      readRate,
      archiveRate
    })

  } catch (error) {
    console.error('Error fetching detailed stats:', error)
    
    if (error instanceof Error && error.message.includes('invalid_grant')) {
      return Response.json({ 
        error: 'Gmail authentication expired',
        requiresReconnection: true 
      }, { status: 401 })
    }
    
    return Response.json({ 
      error: 'Failed to fetch detailed statistics' 
    }, { status: 500 })
  }
} 