import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetUser, 
  handleMeetApiError, 
  formatMeetApiResponse,
  getConferenceRecord,
  getConferenceDetails,
  getMeetingOverview
} from "@/lib/meet"

interface RouteParams {
  params: {
    conferenceId: string
  }
}

/**
 * GET /api/meet/conferences/[conferenceId] - Get conference record details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { conferenceId } = params
    
    // Decode the conference ID in case it's URL encoded
    const decodedConferenceId = decodeURIComponent(conferenceId)
    
    // Check what type of data is requested
    const includeDetails = url.searchParams.get('includeDetails') === 'true'
    const includeOverview = url.searchParams.get('includeOverview') === 'true'

    if (includeOverview) {
      // Get comprehensive overview including participants and artifacts
      const overview = await getMeetingOverview(user.id, decodedConferenceId)
      
      return formatMeetApiResponse(
        overview,
        "Conference overview retrieved successfully"
      )
    } else if (includeDetails) {
      // Get detailed conference information
      const details = await getConferenceDetails(user.id, decodedConferenceId)
      
      return formatMeetApiResponse(
        details,
        "Conference details retrieved successfully"
      )
    } else {
      // Get basic conference record
      const conference = await getConferenceRecord(user.id, decodedConferenceId)
      
      return formatMeetApiResponse(
        conference,
        "Conference record retrieved successfully"
      )
    }
  } catch (error) {
    return handleMeetApiError(error)
  }
}
