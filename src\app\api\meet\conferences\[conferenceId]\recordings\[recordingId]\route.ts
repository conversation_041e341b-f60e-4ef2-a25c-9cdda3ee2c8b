import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetUser, 
  handleMeetApiError, 
  formatMeetApiResponse,
  getRecording,
  getRecordingDownloadInfo
} from "@/lib/meet"

interface RouteParams {
  params: {
    conferenceId: string
    recordingId: string
  }
}

/**
 * GET /api/meet/conferences/[conferenceId]/recordings/[recordingId] - Get recording details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { recordingId } = params
    
    // Decode the recording ID in case it's URL encoded
    const decodedRecordingId = decodeURIComponent(recordingId)
    const searchParams = url.searchParams

    const includeDownloadInfo = searchParams.get('includeDownloadInfo') === 'true'

    // Get recording details
    const recording = await getRecording(user.id, decodedRecordingId)

    let response: any = recording

    if (includeDownloadInfo) {
      try {
        const downloadInfo = await getRecordingDownloadInfo(user.id, decodedRecordingId)
        response = {
          ...recording,
          downloadInfo
        }
      } catch (error) {
        console.error(`Error getting download info for recording ${decodedRecordingId}:`, error)
        // Continue without download info if there's an error
      }
    }

    return formatMeetApiResponse(
      response,
      "Recording details retrieved successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
