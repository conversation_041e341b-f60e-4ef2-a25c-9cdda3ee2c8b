"use client"

import { useSearchParams } from "next/navigation"
import { EmailPageLayout } from "@/components/email/EmailPageLayout"
import { Tag } from "lucide-react"
import { useEffect, useState } from "react"

export default function CategoriesPage() {
  const searchParams = useSearchParams()
  const [currentCategory, setCurrentCategory] = useState<string>('primary')
  
  // Update category when URL params change
  useEffect(() => {
    const category = searchParams.get('category') || 'primary'
    setCurrentCategory(category)
  }, [searchParams])
  
  // Map category names to proper display names
  const categoryDisplayNames: { [key: string]: string } = {
    'primary': 'Primary',
    'social': 'Social',
    'promotions': 'Promotions',
    'updates': 'Updates',
    'forums': 'Forums'
  }

  const displayName = categoryDisplayNames[currentCategory] || 'All Categories'
  
  return (
    <EmailPageLayout
      key={currentCategory} // Force re-render when category changes
      title={`${displayName} Category`}
      icon={Tag}
      apiEndpoint={`/api/gmail/categories?category=${currentCategory}`}
      emptyMessage={`No emails in ${displayName} category`}
      showDateRange={true}
      pageContext="categories"
    />
  )
}