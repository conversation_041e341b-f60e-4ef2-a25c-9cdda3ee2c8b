"use client"

import { useSession } from "next-auth/react"
import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Mail, Shield, Zap } from "lucide-react"

export default function Home() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return // Still loading
    
    if (session) {
      router.push("/dashboard")
    } else {
      router.push("/auth/signin")
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Fallback UI while redirecting
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl mx-auto text-center space-y-6">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">
            Eagle Mass Mailer
          </h1>
          <p className="text-xl text-gray-600">
            Send personalized email campaigns and track their performance with powerful analytics.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
          <div className="flex flex-col items-center space-y-3 p-6">
            <Mail className="h-12 w-12 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Mass Email Campaigns</h3>
            <p className="text-gray-600 text-center">Send emails to thousands of recipients at once</p>
          </div>
          
          <div className="flex flex-col items-center space-y-3 p-6">
            <Shield className="h-12 w-12 text-green-600" />
            <h3 className="font-semibold text-gray-900">Secure & Reliable</h3>
            <p className="text-gray-600 text-center">Your data is protected with enterprise-grade security</p>
          </div>
          
          <div className="flex flex-col items-center space-y-3 p-6">
            <Zap className="h-12 w-12 text-yellow-600" />
            <h3 className="font-semibold text-gray-900">Real-time Analytics</h3>
            <p className="text-gray-600 text-center">Track opens, clicks, and engagement metrics</p>
          </div>
        </div>

        <div className="pt-8">
          <Button 
            onClick={() => router.push("/auth/signin")}
            size="lg"
            className="px-8 py-3 text-lg"
          >
            Get Started
          </Button>
        </div>
      </div>
    </div>
  )
}
