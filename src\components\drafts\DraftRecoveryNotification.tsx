"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ertCircle, FileText, Clock, X, Mail, Trash2 } from "lucide-react"

interface Draft {
  id: string
  subject: string
  to: string
  snippet: string
  lastModified: Date
  threadId?: string
}

interface DraftRecoveryNotificationProps {
  onDraftRecover?: (draft: Draft) => void
  onDraftDelete?: (draftId: string) => void
  onDismiss?: () => void
}

export function DraftRecoveryNotification({ 
  onDraftRecover, 
  onDraftDelete, 
  onDismiss 
}: DraftRecoveryNotificationProps) {
  const [drafts, setDrafts] = useState<Draft[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDrafts()
  }, [])

  const fetchDrafts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/gmail/drafts')
      
      if (!response.ok) {
        throw new Error('Failed to fetch drafts')
      }

      const data = await response.json()
      
      // Transform Gmail drafts to our format
      const transformedDrafts: Draft[] = data.emails?.map((email: any) => ({
        id: email.id,
        subject: email.subject || 'No Subject',
        to: email.to || '',
        snippet: email.snippet || '',
        lastModified: new Date(email.date),
        threadId: email.threadId
      })) || []

      setDrafts(transformedDrafts)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load drafts')
    } finally {
      setLoading(false)
    }
  }

  const handleRecoverDraft = (draft: Draft) => {
    onDraftRecover?.(draft)
  }

  const handleDeleteDraft = async (draftId: string) => {
    try {
      const response = await fetch('/api/gmail/drafts/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ draftId })
      })

      if (response.ok) {
        setDrafts(prev => prev.filter(draft => draft.id !== draftId))
        onDraftDelete?.(draftId)
      }
    } catch (err) {
      console.error('Error deleting draft:', err)
    }
  }

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  if (loading) {
    return (
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 animate-spin text-blue-600" />
            <span className="text-sm text-blue-800">Loading drafts...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-800">Error loading drafts: {error}</span>
            </div>
            {onDismiss && (
              <Button variant="ghost" size="sm" onClick={onDismiss}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (drafts.length === 0) {
    return null
  }

  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-orange-600" />
            <CardTitle className="text-lg text-orange-800">Unsent Drafts Found</CardTitle>
          </div>
          {onDismiss && (
            <Button variant="ghost" size="sm" onClick={onDismiss}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <CardDescription className="text-orange-700">
          You have {drafts.length} unsent draft{drafts.length > 1 ? 's' : ''}. Would you like to recover or delete them?
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {drafts.slice(0, 3).map((draft) => (
          <div key={draft.id} className="flex items-center justify-between p-3 bg-white rounded-lg border border-orange-200">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <Mail className="h-4 w-4 text-gray-500 flex-shrink-0" />
                <span className="font-medium text-sm text-gray-900 truncate">
                  {draft.subject}
                </span>
                <Badge variant="secondary" className="text-xs">
                  {formatTimeAgo(draft.lastModified)}
                </Badge>
              </div>
              <div className="text-xs text-gray-600 truncate">
                To: {draft.to || 'No recipients'}
              </div>
              {draft.snippet && (
                <div className="text-xs text-gray-500 truncate mt-1">
                  {draft.snippet}
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2 ml-3">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleRecoverDraft(draft)}
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                Recover
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleDeleteDraft(draft.id)}
                className="text-red-600 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
        
        {drafts.length > 3 && (
          <div className="text-center pt-2">
            <Button variant="ghost" size="sm" className="text-orange-700">
              View all {drafts.length} drafts
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
