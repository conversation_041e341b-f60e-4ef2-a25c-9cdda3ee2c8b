import { NextRequest } from "next/server"
import { withG<PERSON><PERSON><PERSON> } from "@/lib/gmail"
import { getGmailClient } from "@/lib/gmail/client"
import { extractEnhancedEmailBody, checkForAttachments, extractAttachments } from "@/lib/gmail/content-parser"
import { extractHeader } from "@/lib/gmail/utils"

interface ThreadMessage {
  id: string
  threadId: string
  from: string
  to?: string
  cc?: string
  bcc?: string
  replyTo?: string
  subject: string
  date: Date
  snippet: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  body: string
  labels?: Array<{
    id: string
    name: string
    type: string
  }>
  attachments?: Array<{
    filename: string
    mimeType: string
    size: number
    attachmentId: string
  }>
  messageId?: string
  references?: string
  inReplyTo?: string
}

async function processThreadMessage(gmail: any, message: any): Promise<ThreadMessage> {
  const payload = message.payload
  const headers = payload.headers || []
  
  // Extract headers
  const from = extractHeader(headers, 'From') || ''
  const to = extractHeader(headers, 'To') || ''
  const cc = extractHeader(headers, 'Cc') || ''
  const bcc = extractHeader(headers, 'Bcc') || ''
  const replyTo = extractHeader(headers, 'Reply-To') || ''
  const subject = extractHeader(headers, 'Subject') || ''
  const dateStr = extractHeader(headers, 'Date') || ''
  const messageId = extractHeader(headers, 'Message-ID') || ''
  const references = extractHeader(headers, 'References') || ''
  const inReplyTo = extractHeader(headers, 'In-Reply-To') || ''
  
  // Parse date
  const date = dateStr ? new Date(dateStr) : new Date()
  
  // Check if message is read and starred
  const labelIds = message.labelIds || []
  const isRead = !labelIds.includes('UNREAD')
  const isStarred = labelIds.includes('STARRED')
  
  // Extract body content
  const body = extractEnhancedEmailBody(payload)
  
  // Check for attachments
  const hasAttachments = checkForAttachments(payload)
  const attachments = hasAttachments ? extractAttachments(payload) : []
  
  // Process labels
  const labels = labelIds.map((labelId: string) => {
    // Map common system labels
    const systemLabels: { [key: string]: string } = {
      'INBOX': 'INBOX',
      'SENT': 'SENT',
      'DRAFT': 'DRAFT',
      'SPAM': 'SPAM',
      'TRASH': 'TRASH',
      'UNREAD': 'UNREAD',
      'STARRED': 'STARRED',
      'IMPORTANT': 'IMPORTANT'
    }
    
    return {
      id: labelId,
      name: systemLabels[labelId] || labelId,
      type: systemLabels[labelId] ? 'system' : 'user'
    }
  })
  
  return {
    id: message.id,
    threadId: message.threadId,
    from,
    to: to || undefined,
    cc: cc || undefined,
    bcc: bcc || undefined,
    replyTo: replyTo || undefined,
    subject,
    date,
    snippet: message.snippet || '',
    isRead,
    isStarred,
    hasAttachments,
    body,
    labels,
    attachments,
    messageId: messageId || undefined,
    references: references || undefined,
    inReplyTo: inReplyTo || undefined
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ threadId: string }> }
) {
  return withGmailAuth(request, async ({ user }) => {
    const { threadId } = await params
    
    try {
      const { gmail } = await getGmailClient(user.id)
      
      // Fetch the complete thread with all messages
      const threadResponse = await gmail.users.threads.get({
        userId: 'me',
        id: threadId,
        format: 'full'
      })
      
      const thread = threadResponse.data
      
      if (!thread || !thread.messages) {
        throw new Error("Thread not found or has no messages")
      }
      
      // Process all messages in the thread
      const messages: ThreadMessage[] = []
      
      for (const message of thread.messages) {
        try {
          const processedMessage = await processThreadMessage(gmail, message)
          messages.push(processedMessage)
        } catch (error) {
          console.error(`Error processing message ${message.id}:`, error)
          // Continue processing other messages even if one fails
        }
      }
      
      // Sort messages by date (oldest first for thread view)
      messages.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      
      return {
        threadId: thread.id,
        messageCount: messages.length,
        messages,
        historyId: thread.historyId
      }
    } catch (error) {
      console.error('Error fetching thread:', error)
      throw new Error(`Failed to fetch thread: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  })
}
