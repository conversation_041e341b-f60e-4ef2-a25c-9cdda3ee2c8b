import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetUser, 
  handleMeetApiError, 
  formatMeetApiResponse,
  getMeetingSpace,
  updateMeetingSpace,
  endActiveConference,
  getMeetingJoinInfo,
  hasActiveConference
} from "@/lib/meet"

interface RouteParams {
  params: {
    spaceName: string
  }
}

/**
 * GET /api/meet/spaces/[spaceName] - Get meeting space details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { spaceName } = params
    
    // Decode the space name in case it's URL encoded
    const decodedSpaceName = decodeURIComponent(spaceName)

    // Check if we want join info or full space details
    const includeJoinInfo = url.searchParams.get('includeJoinInfo') === 'true'
    const checkActiveConference = url.searchParams.get('checkActive') === 'true'

    const meetingSpace = await getMeetingSpace(user.id, decodedSpaceName)

    let response: any = meetingSpace

    if (includeJoinInfo) {
      const joinInfo = await getMeetingJoinInfo(user.id, decodedSpaceName)
      response = {
        ...meetingSpace,
        joinInfo
      }
    }

    if (checkActiveConference) {
      const isActive = await hasActiveConference(user.id, decodedSpaceName)
      response = {
        ...response,
        hasActiveConference: isActive
      }
    }

    return formatMeetApiResponse(
      response,
      "Meeting space retrieved successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}

/**
 * PATCH /api/meet/spaces/[spaceName] - Update meeting space configuration
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { spaceName } = params
    const body = await request.json()
    
    // Decode the space name in case it's URL encoded
    const decodedSpaceName = decodeURIComponent(spaceName)

    // Validate request body
    const config = body.config
    if (!config) {
      return NextResponse.json({
        error: "Missing config in request body"
      }, { status: 400 })
    }

    // Validate access type
    if (config.accessType && !['OPEN', 'TRUSTED', 'RESTRICTED'].includes(config.accessType)) {
      return NextResponse.json({
        error: "Invalid accessType. Must be one of: OPEN, TRUSTED, RESTRICTED"
      }, { status: 400 })
    }

    // Validate entry point access
    if (config.entryPointAccess && !['ALL', 'CREATOR_APP_ONLY'].includes(config.entryPointAccess)) {
      return NextResponse.json({
        error: "Invalid entryPointAccess. Must be one of: ALL, CREATOR_APP_ONLY"
      }, { status: 400 })
    }

    // Get update mask from query parameters
    const updateMask = url.searchParams.get('updateMask')?.split(',')

    // Update the meeting space
    const updatedSpace = await updateMeetingSpace(
      user.id, 
      decodedSpaceName, 
      config,
      updateMask
    )

    return formatMeetApiResponse(
      updatedSpace,
      "Meeting space updated successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}

/**
 * DELETE /api/meet/spaces/[spaceName] - End active conference in space
 * Note: Google Meet API doesn't support deleting spaces, only ending active conferences
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user } = authResult.data
    const { spaceName } = params
    
    // Decode the space name in case it's URL encoded
    const decodedSpaceName = decodeURIComponent(spaceName)

    // End the active conference
    await endActiveConference(user.id, decodedSpaceName)

    return formatMeetApiResponse(
      { spaceName: decodedSpaceName },
      "Active conference ended successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
