import { 
  listParticipants as apiListParticipants,
  getParticipant as apiGetParticipant
} from './client'
import { Participant, ParticipantSession, ListResponse } from './types'

/**
 * Get a specific participant
 */
export async function getParticipant(
  userId: string,
  participantName: string
): Promise<Participant> {
  return await apiGetParticipant(userId, participantName)
}

/**
 * List participants in a conference
 */
export async function listParticipants(
  userId: string,
  conferenceRecordName: string,
  options: {
    pageSize?: number
    pageToken?: string
    filter?: string
  } = {}
): Promise<ListResponse<Participant>> {
  const response = await apiListParticipants(userId, conferenceRecordName, options)
  
  return {
    items: (response.participants || []) as Participant[],
    nextPageToken: response.nextPageToken,
    totalSize: response.participants?.length
  }
}

/**
 * Get a specific participant session
 */
export async function getParticipantSession(
  userId: string,
  participantSessionName: string
): Promise<ParticipantSession> {
  const { meetClient } = await getMeetClient(userId)
  
  const [response] = await meetClient.getParticipantSession({
    name: participantSessionName
  })
  
  return response as ParticipantSession
}

/**
 * List participant sessions for a participant
 */
export async function listParticipantSessions(
  userId: string,
  participantName: string,
  options: {
    pageSize?: number
    pageToken?: string
    filter?: string
  } = {}
): Promise<ListResponse<ParticipantSession>> {
  // Note: This would need to be implemented with the correct API endpoint
  // For now, returning empty results
  return {
    items: [],
    nextPageToken: undefined,
    totalSize: 0
  }
}

/**
 * Get all participants with their sessions for a conference
 */
export async function getConferenceParticipants(
  userId: string,
  conferenceRecordName: string,
  includeSessions: boolean = false
): Promise<{
  participants: Participant[]
  totalParticipants: number
  participantSessions?: { [participantName: string]: ParticipantSession[] }
}> {
  const participantsResponse = await listParticipants(userId, conferenceRecordName, {
    pageSize: 100 // Get all participants
  })
  
  const result: any = {
    participants: participantsResponse.items,
    totalParticipants: participantsResponse.items.length
  }
  
  if (includeSessions) {
    const participantSessions: { [participantName: string]: ParticipantSession[] } = {}
    
    // Get sessions for each participant
    for (const participant of participantsResponse.items) {
      try {
        const sessionsResponse = await listParticipantSessions(userId, participant.name, {
          pageSize: 50
        })
        participantSessions[participant.name] = sessionsResponse.items
      } catch (error) {
        console.error(`Error getting sessions for participant ${participant.name}:`, error)
        participantSessions[participant.name] = []
      }
    }
    
    result.participantSessions = participantSessions
  }
  
  return result
}

/**
 * Get participant statistics for a conference
 */
export async function getParticipantStatistics(
  userId: string,
  conferenceRecordName: string
): Promise<{
  totalParticipants: number
  averageJoinTime: number // in minutes from start
  averageDuration: number // in minutes
  totalSessions: number
  uniqueParticipants: number
}> {
  const participantsResponse = await listParticipants(userId, conferenceRecordName, {
    pageSize: 100
  })
  
  // Simplified statistics calculation
  const totalParticipants = participantsResponse.items.length
  
  return {
    totalParticipants,
    averageJoinTime: 0, // Would calculate from actual data
    averageDuration: 0, // Would calculate from session data
    totalSessions: totalParticipants, // Simplified
    uniqueParticipants: totalParticipants
  }
}

/**
 * Get participant attendance summary
 */
export async function getParticipantAttendance(
  userId: string,
  conferenceRecordName: string
): Promise<{
  totalParticipants: number
  attendanceByTime: { [timeSlot: string]: number }
  topParticipants: { name: string; duration: number }[]
  averageAttendance: number
}> {
  const participants = await getConferenceParticipants(userId, conferenceRecordName, true)
  
  // Simplified attendance calculation
  return {
    totalParticipants: participants.totalParticipants,
    attendanceByTime: {}, // Would calculate from session data
    topParticipants: [], // Would sort by duration
    averageAttendance: 0 // Would calculate average
  }
}

/**
 * Search participants by criteria
 */
export async function searchParticipants(
  userId: string,
  conferenceRecordName: string,
  criteria: {
    name?: string
    email?: string
    joinedAfter?: string
    leftBefore?: string
    minDuration?: number
  },
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<Participant>> {
  // Build filter string
  const filters = []
  
  if (criteria.name) {
    filters.push(`name:"${criteria.name}"`)
  }
  
  if (criteria.email) {
    filters.push(`email:"${criteria.email}"`)
  }
  
  const filter = filters.length > 0 ? filters.join(' AND ') : undefined
  
  return await listParticipants(userId, conferenceRecordName, {
    ...options,
    filter
  })
}
