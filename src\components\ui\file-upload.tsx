"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { X, Upload, File, Image, FileText, Music, Video } from "lucide-react"

interface FileUpload {
  file: File
  id: string
  preview?: string
}

interface FileUploadProps {
  onFilesChange: (files: FileUpload[]) => void
  maxFiles?: number
  maxFileSize?: number // in MB
  acceptedFileTypes?: string[]
  className?: string
}

export function FileUploadComponent({
  onFilesChange,
  maxFiles = 5,
  maxFileSize = 25, // 25MB default
  acceptedFileTypes = [],
  className = ""
}: FileUploadProps) {
  const [files, setFiles] = useState<FileUpload[]>([])
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return Image
    if (mimeType.startsWith('video/')) return Video
    if (mimeType.startsWith('audio/')) return Music
    if (mimeType.includes('text') || mimeType.includes('document')) return FileText
    return File
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = (file: File): string | null => {
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`
    }
    if (acceptedFileTypes.length > 0 && !acceptedFileTypes.includes(file.type)) {
      return `File type ${file.type} is not accepted`
    }
    return null
  }

  const handleFiles = (newFiles: FileList) => {
    const fileArray = Array.from(newFiles)
    const validFiles: FileUpload[] = []

    fileArray.forEach(file => {
      const error = validateFile(file)
      if (!error && files.length + validFiles.length < maxFiles) {
        const fileUpload: FileUpload = {
          file,
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        }

        // Create preview for images
        if (file.type.startsWith('image/')) {
          const reader = new FileReader()
          reader.onload = (e) => {
            fileUpload.preview = e.target?.result as string
            setFiles(prev => 
              prev.map(f => f.id === fileUpload.id ? fileUpload : f)
            )
          }
          reader.readAsDataURL(file)
        }

        validFiles.push(fileUpload)
      }
    })

    const updatedFiles = [...files, ...validFiles]
    setFiles(updatedFiles)
    onFilesChange(updatedFiles)
  }

  const removeFile = (id: string) => {
    const updatedFiles = files.filter(f => f.id !== id)
    setFiles(updatedFiles)
    onFilesChange(updatedFiles)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files)
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* File Drop Zone */}
      <Card
        className={`border-2 border-dashed transition-colors cursor-pointer ${
          dragActive 
            ? 'border-primary bg-primary/5' 
            : 'border-muted-foreground/25 hover:border-primary/50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <CardContent className="p-6 text-center">
          <Upload className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
          <p className="text-sm font-medium mb-2">
            Drag and drop files here, or click to select
          </p>
          <p className="text-xs text-muted-foreground">
            Max {maxFiles} files, up to {maxFileSize}MB each
          </p>
          {acceptedFileTypes.length > 0 && (
            <p className="text-xs text-muted-foreground mt-1">
              Accepted: {acceptedFileTypes.join(', ')}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedFileTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Selected Files */}
      {files.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium">
            Selected Files ({files.length})
          </p>
          <div className="space-y-2">
            {files.map((fileUpload) => {
              const FileIcon = getFileIcon(fileUpload.file.type)
              return (
                <div
                  key={fileUpload.id}
                  className="flex items-center space-x-3 p-3 border rounded-lg bg-muted/30"
                >
                  {fileUpload.preview ? (
                    <div
                      style={{ backgroundImage: `url(${fileUpload.preview})` }}
                      className="w-10 h-10 bg-cover bg-center rounded"
                    />
                  ) : (
                    <FileIcon className="h-8 w-8 text-muted-foreground" />
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {fileUpload.file.name}
                    </p>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs">
                        {formatFileSize(fileUpload.file.size)}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {fileUpload.file.type}
                      </span>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      removeFile(fileUpload.id)
                    }}
                    className="text-muted-foreground hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
} 