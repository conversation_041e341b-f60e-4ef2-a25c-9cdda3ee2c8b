'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { 
  Send, 
  Bot, 
  User, 
  Loader2, 
  Sparkles,
  Mail,
  Calendar,
  CheckSquare,
  Clock,
  Zap,
  MessageSquare,
  Mic,
  Paperclip,
  MoreHorizontal,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  Smile,
  Plus,
  X,
  ChevronDown,
  Search,
  Filter,
  Archive,
  Star,
  Settings,
  HelpCircle,
  Minimize2,
  Maximize2
} from "lucide-react"
import { format, isToday, isYesterday } from "date-fns"
import { AIMessage, AIAction } from "@/lib/ai-agent"
import { cn } from "@/lib/utils"

interface ChatInterfaceProps {
  conversationId?: string
  onSendMessage?: (message: string, conversationId?: string) => Promise<AIMessage>
  messages?: AIMessage[]
  isLoading?: boolean
  className?: string
  conversations?: Array<{
    id: string
    title: string
    lastMessage?: {
      role: string
      content: string
      timestamp: Date
    }
    updatedAt: Date
    createdAt: Date
  }>
  onConversationSelect?: (conversationId: string) => void
  onNewConversation?: () => void
}

interface TypingIndicatorProps {
  isVisible: boolean
}

function TypingIndicator({ isVisible }: TypingIndicatorProps) {
  if (!isVisible) return null

  return (
    <div className="flex items-start gap-3 p-6 animate-fade-in">
      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
        <Bot className="h-4 w-4 text-white" />
      </div>
      <div className="flex-1 max-w-[70%]">
        <div className="bg-gray-50 rounded-2xl rounded-bl-md px-4 py-3 border border-gray-100 shadow-sm">
          <div className="flex items-center gap-2">
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]" />
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]" />
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
            </div>
            <span className="text-sm text-gray-500">AI is thinking...</span>
          </div>
        </div>
      </div>
    </div>
  )
}

interface MessageActionsProps {
  actions: AIAction[]
}

function MessageActions({ actions }: MessageActionsProps) {
  if (!actions || actions.length === 0) return null

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'send_email': return <Mail className="h-3 w-3" />
      case 'schedule_meeting': return <Calendar className="h-3 w-3" />
      case 'create_task': return <CheckSquare className="h-3 w-3" />
      case 'analyze_emails': return <Sparkles className="h-3 w-3" />
      default: return <Zap className="h-3 w-3" />
    }
  }

  const getActionColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100'
      case 'failed': return 'bg-red-50 text-red-700 border-red-200 hover:bg-red-100'
      case 'pending': return 'bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100'
      default: return 'bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100'
    }
  }

  return (
    <div className="mt-4 flex flex-wrap gap-2">
      {actions.map((action, index) => (
        <Badge 
          key={index} 
          variant="outline" 
          className={cn(
            "text-xs px-3 py-1 rounded-full transition-all duration-200 cursor-pointer",
            getActionColor(action.status)
          )}
        >
          {getActionIcon(action.type)}
          <span className="ml-2 font-medium">
            {action.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
          {action.status === 'completed' && (
            <span className="ml-2 text-xs">✓</span>
          )}
        </Badge>
      ))}
    </div>
  )
}

interface MessageBubbleProps {
  message: AIMessage
  isUser: boolean
  showAvatar?: boolean
  onCopy?: (content: string) => void
  onReact?: (messageId: string, reaction: string) => void
}

function MessageBubble({ message, isUser, showAvatar = true, onCopy, onReact }: MessageBubbleProps) {
  const [showActions, setShowActions] = useState(false)
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    if (onCopy) {
      onCopy(message.content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const formatTime = (timestamp: Date) => {
    if (isToday(timestamp)) {
      return format(timestamp, 'h:mm a')
    } else if (isYesterday(timestamp)) {
      return `Yesterday ${format(timestamp, 'h:mm a')}`
    } else {
      return format(timestamp, 'MMM d, h:mm a')
    }
  }

  return (
    <div 
      className={cn(
        "flex gap-3 p-6 group hover:bg-gray-50/50 transition-colors duration-200",
        isUser ? "flex-row-reverse" : "flex-row"
      )}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {showAvatar && (
        <div className={cn(
          "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-sm",
          isUser 
            ? "bg-blue-600 text-white" 
            : "bg-gradient-to-br from-blue-500 to-purple-600 text-white"
        )}>
          {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
        </div>
      )}
      
      <div className={cn(
        "flex-1 max-w-[70%] space-y-2",
        isUser ? "flex flex-col items-end" : "flex flex-col items-start"
      )}>
        <div className={cn(
          "rounded-2xl px-4 py-3 max-w-full relative group/message shadow-sm border",
          isUser 
            ? "bg-blue-600 text-white rounded-br-md border-blue-600" 
            : "bg-white text-gray-900 rounded-bl-md border-gray-200 hover:border-gray-300"
        )}>
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </div>
          
          {!isUser && message.metadata?.actions && (
            <MessageActions actions={message.metadata.actions} />
          )}

          {/* Message Actions */}
          {showActions && (
            <div className={cn(
              "absolute top-1 flex items-center gap-1 opacity-0 group-hover/message:opacity-100 transition-opacity duration-200",
              isUser ? "left-1" : "right-1"
            )}>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 bg-white/90 hover:bg-white shadow-sm border"
                onClick={handleCopy}
              >
                {copied ? (
                  <CheckSquare className="h-3 w-3 text-green-600" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
              {!isUser && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 bg-white/90 hover:bg-white shadow-sm border"
                    onClick={() => onReact?.(message.id, 'thumbs_up')}
                  >
                    <ThumbsUp className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 bg-white/90 hover:bg-white shadow-sm border"
                    onClick={() => onReact?.(message.id, 'thumbs_down')}
                  >
                    <ThumbsDown className="h-3 w-3" />
                  </Button>
                </>
              )}
            </div>
          )}
        </div>
        
        <div className={cn(
          "text-xs text-gray-500 px-2",
          isUser ? "text-right" : "text-left"
        )}>
          {formatTime(message.timestamp)}
        </div>
      </div>
    </div>
  )
}

const quickPrompts = [
  {
    text: "Summarize today's emails",
    icon: <Mail className="h-4 w-4" />,
    category: "Email",
    color: "bg-blue-50 text-blue-700 border-blue-200"
  },
  {
    text: "Schedule team meeting",
    icon: <Calendar className="h-4 w-4" />,
    category: "Calendar",
    color: "bg-green-50 text-green-700 border-green-200"
  },
  {
    text: "Create follow-up task",
    icon: <CheckSquare className="h-4 w-4" />,
    category: "Tasks",
    color: "bg-purple-50 text-purple-700 border-purple-200"
  },
  {
    text: "Analyze email trends",
    icon: <Sparkles className="h-4 w-4" />,
    category: "Analytics",
    color: "bg-amber-50 text-amber-700 border-amber-200"
  }
]

interface ConversationSidebarProps {
  conversations?: Array<{
    id: string
    title: string
    lastMessage?: {
      role: string
      content: string
      timestamp: Date
    }
    updatedAt: Date
    createdAt: Date
  }>
  currentConversationId?: string
  onConversationSelect?: (conversationId: string) => void
  onNewConversation?: () => void
  isCollapsed?: boolean
  onToggleCollapse?: () => void
}

function ConversationSidebar({ 
  conversations = [], 
  currentConversationId, 
  onConversationSelect, 
  onNewConversation,
  isCollapsed = false,
  onToggleCollapse
}: ConversationSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const filteredConversations = conversations.filter(conv => 
    conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.lastMessage?.content.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const groupedConversations = filteredConversations.reduce((acc, conv) => {
    const date = conv.updatedAt
    let key = 'Older'
    
    if (isToday(date)) {
      key = 'Today'
    } else if (isYesterday(date)) {
      key = 'Yesterday'
    } else if (date > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) {
      key = 'This Week'
    }
    
    if (!acc[key]) acc[key] = []
    acc[key].push(conv)
    return acc
  }, {} as Record<string, typeof conversations>)

  if (isCollapsed) {
    return (
      <div className="w-16 bg-gray-50 border-r border-gray-200 flex flex-col items-center py-4 gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className="w-10 h-10 p-0"
        >
          <MessageSquare className="h-5 w-5" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewConversation}
          className="w-10 h-10 p-0"
        >
          <Plus className="h-5 w-5" />
        </Button>
      </div>
    )
  }

  return (
    <div className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="w-8 h-8 p-0"
            >
              <Minimize2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onNewConversation}
              className="w-8 h-8 p-0"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-white border-gray-300"
          />
        </div>
      </div>

      {/* Conversations List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {Object.entries(groupedConversations).map(([group, convs]) => (
            <div key={group} className="mb-6">
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2 px-2">
                {group}
              </h3>
              <div className="space-y-1">
                {convs.map((conv) => (
                  <Card
                    key={conv.id}
                    className={cn(
                      "cursor-pointer transition-all duration-200 hover:shadow-sm border-0",
                      currentConversationId === conv.id 
                        ? "bg-blue-50 border-blue-200 shadow-sm" 
                        : "bg-white hover:bg-gray-50"
                    )}
                    onClick={() => onConversationSelect?.(conv.id)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {conv.title}
                          </h4>
                          {conv.lastMessage && (
                            <p className="text-xs text-gray-500 mt-1 truncate">
                              {conv.lastMessage.content}
                            </p>
                          )}
                        </div>
                        <div className="text-xs text-gray-400 ml-2">
                          {format(conv.updatedAt, 'MMM d')}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

export function ChatInterface({ 
  conversationId, 
  onSendMessage, 
  messages = [], 
  isLoading = false,
  className,
  conversations = [],
  onConversationSelect,
  onNewConversation
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [contextualSuggestions, setContextualSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showQuickPrompts, setShowQuickPrompts] = useState(messages.length === 0)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }, [messages, isTyping])

  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  // Hide quick prompts when messages exist
  useEffect(() => {
    setShowQuickPrompts(messages.length === 0)
  }, [messages.length])

  const handleSendMessage = async (messageText?: string) => {
    const message = messageText || inputValue.trim()
    if (!message || isLoading) return

    setInputValue('')
    setIsTyping(true)
    setShowQuickPrompts(false)

    try {
      if (onSendMessage) {
        await onSendMessage(message, conversationId)
      }
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsTyping(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const handleReact = (messageId: string, reaction: string) => {
    // Implement reaction handling
    console.log('React:', messageId, reaction)
  }

  const handleQuickPrompt = (prompt: string) => {
    setInputValue(prompt)
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  return (
    <div className={cn("h-full flex bg-white", className)}>
      {/* Sidebar */}
      <ConversationSidebar
        conversations={conversations}
        currentConversationId={conversationId}
        onConversationSelect={onConversationSelect}
        onNewConversation={onNewConversation}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-sm">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">AI Assistant</h1>
                <p className="text-sm text-gray-500">
                  {isTyping ? 'Typing...' : 'Ready to help with your tasks'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                Online
              </Badge>
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 flex flex-col">
          <ScrollArea ref={scrollAreaRef} className="flex-1">
            <div className="min-h-full flex flex-col">
              {/* Welcome Message */}
              {messages.length === 0 && !isTyping && (
                <div className="flex-1 flex items-center justify-center p-8">
                  <div className="text-center max-w-md">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4 shadow-lg">
                      <Bot className="h-8 w-8 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to AI Assistant</h2>
                    <p className="text-gray-600 mb-6">
                      I'm here to help you manage your emails, schedule meetings, create tasks, and more. 
                      How can I assist you today?
                    </p>
                  </div>
                </div>
              )}

              {/* Messages */}
              {messages.map((message, index) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  isUser={message.role === 'user'}
                  showAvatar={true}
                  onCopy={handleCopy}
                  onReact={handleReact}
                />
              ))}

              {/* Typing Indicator */}
              <TypingIndicator isVisible={isTyping} />

              {/* Quick Prompts */}
              {showQuickPrompts && (
                <div className="p-6 border-t border-gray-100">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {quickPrompts.map((prompt, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        className={cn(
                          "justify-start text-left h-auto p-3 transition-all duration-200",
                          prompt.color
                        )}
                        onClick={() => handleQuickPrompt(prompt.text)}
                      >
                        <div className="flex items-center gap-3">
                          {prompt.icon}
                          <div>
                            <div className="font-medium">{prompt.text}</div>
                            <div className="text-xs opacity-75">{prompt.category}</div>
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Input Area */}
          <div className="p-4 border-t border-gray-200 bg-white">
            <div className="flex items-end gap-3">
              <div className="flex-1">
                <div className="relative">
                  <Textarea
                    ref={inputRef}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type your message..."
                    className="min-h-[44px] max-h-32 resize-none pr-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <div className="absolute right-2 bottom-2 flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 p-0 hover:bg-gray-100"
                    >
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 p-0 hover:bg-gray-100"
                    >
                      <Smile className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
              
              <Button
                onClick={() => handleSendMessage()}
                disabled={!inputValue.trim() || isLoading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 h-11 transition-all duration-200 disabled:opacity-50"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
              <div className="flex items-center gap-4">
                <span>Press Enter to send, Shift+Enter for new line</span>
              </div>
              <div className="flex items-center gap-1">
                <span>Powered by AI</span>
                <Sparkles className="h-3 w-3" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
