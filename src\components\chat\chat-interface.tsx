'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Send, 
  Bot, 
  User, 
  Loader2, 
  Sparkles,
  Mail,
  Calendar,
  CheckSquare,
  Clock,
  Zap,
  MessageSquare
} from "lucide-react"
import { format } from "date-fns"
import { AIMessage, AIAction } from "@/lib/ai-agent"
import { cn } from "@/lib/utils"

interface ChatInterfaceProps {
  conversationId?: string
  onSendMessage?: (message: string, conversationId?: string) => Promise<AIMessage>
  messages?: AIMessage[]
  isLoading?: boolean
  className?: string
}

interface TypingIndicatorProps {
  isVisible: boolean
}

function TypingIndicator({ isVisible }: TypingIndicatorProps) {
  if (!isVisible) return null

  return (
    <div className="flex items-center gap-2 p-4 text-muted-foreground">
      <Bot className="h-4 w-4" />
      <div className="flex items-center gap-1">
        <span className="text-sm">AI Agent is thinking</span>
        <div className="flex gap-1">
          <div className="w-1 h-1 bg-current rounded-full animate-bounce [animation-delay:-0.3s]" />
          <div className="w-1 h-1 bg-current rounded-full animate-bounce [animation-delay:-0.15s]" />
          <div className="w-1 h-1 bg-current rounded-full animate-bounce" />
        </div>
      </div>
    </div>
  )
}

interface MessageActionsProps {
  actions: AIAction[]
}

function MessageActions({ actions }: MessageActionsProps) {
  if (!actions || actions.length === 0) return null

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'send_email': return <Mail className="h-3 w-3" />
      case 'schedule_meeting': return <Calendar className="h-3 w-3" />
      case 'create_task': return <CheckSquare className="h-3 w-3" />
      case 'analyze_emails': return <Sparkles className="h-3 w-3" />
      default: return <Zap className="h-3 w-3" />
    }
  }

  const getActionColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'failed': return 'bg-red-100 text-red-800 border-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="mt-3 flex flex-wrap gap-2">
      {actions.map((action, index) => (
        <Badge 
          key={index} 
          variant="outline" 
          className={cn("text-xs", getActionColor(action.status))}
        >
          {getActionIcon(action.type)}
          <span className="ml-1 capitalize">
            {action.type.replace('_', ' ')}
          </span>
          {action.status === 'completed' && action.result?.message && (
            <span className="ml-1 text-xs opacity-75">
              ✓
            </span>
          )}
        </Badge>
      ))}
    </div>
  )
}

interface MessageBubbleProps {
  message: AIMessage
  isUser: boolean
}

function MessageBubble({ message, isUser }: MessageBubbleProps) {
  return (
    <div className={cn(
      "flex gap-3 p-4",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        isUser 
          ? "bg-[#1a73e8] text-white" 
          : "bg-gradient-to-r from-[#4285f4] to-[#34a853] text-white"
      )}>
        {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
      </div>
      
      <div className={cn(
        "flex-1 max-w-[80%]",
        isUser ? "flex flex-col items-end" : "flex flex-col items-start"
      )}>
        <div className={cn(
          "rounded-2xl px-4 py-3 max-w-full",
          isUser 
            ? "bg-[#1a73e8] text-white rounded-br-md" 
            : "bg-[#f8f9fa] text-[#202124] border border-[#e8eaed] rounded-bl-md"
        )}>
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </div>
          
          {!isUser && message.metadata?.actions && (
            <MessageActions actions={message.metadata.actions} />
          )}
        </div>
        
        <div className="text-xs text-muted-foreground mt-1 px-2">
          {format(message.timestamp, 'h:mm a')}
        </div>
      </div>
    </div>
  )
}

const quickPrompts = [
  {
    text: "Get me all today's emails and give summary",
    icon: <Mail className="h-4 w-4" />,
    category: "Email"
  },
  {
    text: "Schedule a meeting with team for project review",
    icon: <Calendar className="h-4 w-4" />,
    category: "Meeting"
  },
  {
    text: "Create a task to follow up on client proposal",
    icon: <CheckSquare className="h-4 w-4" />,
    category: "Task"
  },
  {
    text: "Send email to John about the quarterly report",
    icon: <Mail className="h-4 w-4" />,
    category: "Email"
  }
]

export function ChatInterface({ 
  conversationId, 
  onSendMessage, 
  messages = [], 
  isLoading = false,
  className 
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [contextualSuggestions, setContextualSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight
      }
    }
  }, [messages, isTyping])

  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const handleSendMessage = async (messageText?: string) => {
    const message = messageText || inputValue.trim()
    if (!message || isLoading) return

    setInputValue('')
    setIsTyping(true)

    try {
      if (onSendMessage) {
        await onSendMessage(message, conversationId)
      }
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsTyping(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Fetch contextual suggestions when input changes
  const fetchContextualSuggestions = async (message: string) => {
    if (message.length < 10) {
      setContextualSuggestions([])
      setShowSuggestions(false)
      return
    }

    try {
      const response = await fetch('/api/ai-agent/contextual-suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message })
      })

      if (response.ok) {
        const data = await response.json()
        setContextualSuggestions(data.suggestions || [])
        setShowSuggestions(data.suggestions?.length > 0)
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error)
    }
  }

  // Debounced suggestion fetching
  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputValue.trim()) {
        fetchContextualSuggestions(inputValue.trim())
      } else {
        setShowSuggestions(false)
      }
    }, 1000)

    return () => clearTimeout(timer)
  }, [inputValue])

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion)
    setShowSuggestions(false)
    inputRef.current?.focus()
  }

  const handleQuickPrompt = (prompt: string) => {
    handleSendMessage(prompt)
  }

  return (
    <Card className={cn("flex flex-col h-full", className)}>
      <CardHeader className="border-b border-[#e8eaed] pb-4">
        <CardTitle className="flex items-center gap-2 text-[#202124]">
          <div className="p-2 bg-gradient-to-r from-[#4285f4] to-[#34a853] rounded-lg">
            <MessageSquare className="h-5 w-5 text-white" />
          </div>
          AI Agent Chat
          <Badge variant="outline" className="ml-auto text-xs">
            <Sparkles className="h-3 w-3 mr-1" />
            Powered by Gemini AI
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea ref={scrollAreaRef} className="flex-1">
          <div className="min-h-full">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                <div className="p-4 bg-gradient-to-r from-[#4285f4] to-[#34a853] rounded-full mb-4">
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-[#202124] mb-2">
                  Welcome to AI Agent
                </h3>
                <p className="text-[#5f6368] mb-6 max-w-md">
                  I can help you manage emails, schedule meetings, create tasks, and provide intelligent insights. 
                  Try one of the suggestions below or ask me anything!
                </p>
                
                {/* Quick Prompts */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 w-full max-w-2xl">
                  {quickPrompts.map((prompt, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="h-auto p-4 text-left justify-start hover:bg-[#f8f9fa] border-[#e8eaed]"
                      onClick={() => handleQuickPrompt(prompt.text)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-[#f8f9fa] rounded-lg">
                          {prompt.icon}
                        </div>
                        <div>
                          <div className="font-medium text-sm text-[#202124]">
                            {prompt.text}
                          </div>
                          <div className="text-xs text-[#5f6368] mt-1">
                            {prompt.category}
                          </div>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            ) : (
              <div className="pb-4">
                {messages.map((message) => (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    isUser={message.role === 'user'}
                  />
                ))}
                
                <TypingIndicator isVisible={isTyping || isLoading} />
              </div>
            )}
          </div>
        </ScrollArea>

        <Separator />

        {/* Contextual Suggestions */}
        {showSuggestions && contextualSuggestions.length > 0 && (
          <div className="px-4 py-2 border-t border-[#e8eaed] bg-[#f8f9fa]">
            <div className="text-xs font-medium text-[#5f6368] mb-2 flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              Smart Suggestions
            </div>
            <div className="flex flex-wrap gap-2">
              {contextualSuggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="text-xs h-7 border-[#dadce0] hover:border-[#1a73e8] hover:text-[#1a73e8]"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="p-4">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your emails, schedule, or tasks..."
              className="flex-1 border-[#e8eaed] focus:border-[#1a73e8] focus:ring-[#1a73e8]"
              disabled={isLoading}
            />
            <Button
              onClick={() => handleSendMessage()}
              disabled={!inputValue.trim() || isLoading}
              className="bg-[#1a73e8] hover:bg-[#1557b0] text-white"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          <div className="text-xs text-[#5f6368] mt-2 text-center">
            AI Agent can make mistakes. Verify important information.
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
