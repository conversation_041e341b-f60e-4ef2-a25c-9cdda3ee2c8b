import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { google } from 'googleapis'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return Response.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return Response.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if user has Gmail connected
    if (!user.gmailRefreshToken || !user.gmailConnected) {
      return Response.json({ 
        error: 'Gmail account not connected. Please connect your Gmail account first.',
        requiresReconnection: true 
      }, { status: 400 })
    }

    const body = await request.json()
    const { senderEmail, action, dateRange } = body

    if (!senderEmail || !action) {
      return Response.json({ error: 'Sender email and action are required' }, { status: 400 })
    }

    // Set up OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET
    )
    oauth2Client.setCredentials({
      refresh_token: user.gmailRefreshToken,
    })

    const gmail = google.gmail({ version: 'v1', auth: oauth2Client })

    // Build search query
    let query = `from:${senderEmail}`
    if (dateRange?.startDate && dateRange?.endDate) {
      query += ` after:${dateRange.startDate} before:${dateRange.endDate}`
    }

    // Get emails from this sender
    const emailsResponse = await gmail.users.messages.list({
      userId: 'me',
      q: query,
      maxResults: 1000, // Limit for safety
    })

    const messageIds = emailsResponse.data.messages || []

    if (messageIds.length === 0) {
      return Response.json({ 
        success: true, 
        message: 'No emails found for this sender',
        processedCount: 0 
      })
    }

    let processedCount = 0
    const emailIds = messageIds.map(msg => msg.id!).filter(Boolean)

    switch (action) {
      case 'block':
        // Create a filter to automatically delete future emails from this sender
        try {
          await gmail.users.settings.filters.create({
            userId: 'me',
            requestBody: {
              criteria: {
                from: senderEmail
              },
              action: {
                addLabelIds: ['TRASH']
              }
            }
          })
          processedCount = 1 // Filter created
        } catch (error) {
          console.error('Error creating filter:', error)
          throw new Error('Failed to create email filter')
        }
        break

      case 'archive_all':
        // Remove INBOX label and add archived label
        if (emailIds.length > 0) {
          // Process in batches to avoid API limits
          const batchSize = 1000
          for (let i = 0; i < emailIds.length; i += batchSize) {
            const batch = emailIds.slice(i, i + batchSize)
            try {
              await gmail.users.messages.batchModify({
                userId: 'me',
                requestBody: {
                  ids: batch,
                  removeLabelIds: ['INBOX']
                }
              })
              processedCount += batch.length
            } catch (error) {
              console.error('Error archiving emails:', error)
            }
          }
        }
        break

      case 'delete_all':
        // Move emails to trash
        if (emailIds.length > 0) {
          const batchSize = 1000
          for (let i = 0; i < emailIds.length; i += batchSize) {
            const batch = emailIds.slice(i, i + batchSize)
            try {
              await gmail.users.messages.batchModify({
                userId: 'me',
                requestBody: {
                  ids: batch,
                  addLabelIds: ['TRASH'],
                  removeLabelIds: ['INBOX']
                }
              })
              processedCount += batch.length
            } catch (error) {
              console.error('Error deleting emails:', error)
            }
          }
        }
        break

      case 'label_future':
        // Create a custom label for this sender if it doesn't exist
        const labelName = `Sender: ${senderEmail.split('@')[0]}`
        
        try {
          // Try to find existing label
          const labelsResponse = await gmail.users.labels.list({ userId: 'me' })
          const existingLabel = labelsResponse.data.labels?.find(
            label => label.name === labelName
          )
          
          let labelId: string
          
          if (existingLabel) {
            labelId = existingLabel.id!
          } else {
            // Create new label
            const newLabel = await gmail.users.labels.create({
              userId: 'me',
              requestBody: {
                name: labelName,
                messageListVisibility: 'show',
                labelListVisibility: 'labelShow'
              }
            })
            labelId = newLabel.data.id!
          }

          // Create filter to automatically apply this label to future emails
          await gmail.users.settings.filters.create({
            userId: 'me',
            requestBody: {
              criteria: {
                from: senderEmail
              },
              action: {
                addLabelIds: [labelId]
              }
            }
          })
          
          processedCount = 1 // Filter created
        } catch (error) {
          console.error('Error creating label filter:', error)
          throw new Error('Failed to create label filter')
        }
        break

      case 'unsubscribe':
        // This would typically involve parsing unsubscribe links
        // For now, we'll just archive the emails
        if (emailIds.length > 0) {
          const batchSize = 1000
          for (let i = 0; i < emailIds.length; i += batchSize) {
            const batch = emailIds.slice(i, i + batchSize)
            try {
              await gmail.users.messages.batchModify({
                userId: 'me',
                requestBody: {
                  ids: batch,
                  removeLabelIds: ['INBOX']
                }
              })
              processedCount += batch.length
            } catch (error) {
              console.error('Error processing unsubscribe:', error)
            }
          }
        }
        break

      default:
        return Response.json({ error: 'Invalid action' }, { status: 400 })
    }

    return Response.json({
      success: true,
      message: `Successfully processed ${action} for ${senderEmail}`,
      processedCount,
      totalFound: messageIds.length
    })

  } catch (error) {
    console.error('Error performing subscription action:', error)
    
    if (error instanceof Error && error.message.includes('invalid_grant')) {
      return Response.json({ 
        error: 'Gmail authentication expired',
        requiresReconnection: true 
      }, { status: 401 })
    }
    
    return Response.json({ 
      error: error instanceof Error ? error.message : 'Failed to perform action' 
    }, { status: 500 })
  }
} 