import { NextRequest } from "next/server"
import { 
  bulkEmailOperations, 
  withGmail<PERSON>uth, 
  validateRequiredFields,
  formatBulkOperationResult 
} from "@/lib/gmail"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['messageIds', 'operation'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { messageIds, operation, labelId } = body

    if (!Array.isArray(messageIds) || messageIds.length === 0) {
      throw new Error("messageIds must be a non-empty array")
    }

    // Perform bulk operation
    const result = await bulkEmailOperations(user.id, messageIds, operation, labelId)

    return formatBulkOperationResult(
      messageIds.length,
      result.success,
      result.failed,
      operation
    )
  })
} 