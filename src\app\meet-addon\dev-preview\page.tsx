'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Video, 
  ExternalLink,
  Code,
  Eye,
  Settings,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

export default function MeetAddonDevPreview() {
  const [activeTab, setActiveTab] = useState('overview')

  const openSidePanel = () => {
    window.open('/meet-addon/sidepanel', '_blank', 'width=400,height=600')
  }

  const openMainStage = () => {
    window.open('/meet-addon/mainstage', '_blank', 'width=1200,height=800')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <Video className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Google Meet Add-on</h1>
          </div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Development preview of the Google Meet Add-on components. 
            This page helps you preview the add-on interface before deployment.
          </p>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            Development Mode
          </Badge>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="deployment">Deployment</TabsTrigger>
            <TabsTrigger value="testing">Testing</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Add-on Architecture
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h3 className="font-medium text-blue-800 mb-2">Side Panel</h3>
                    <p className="text-sm text-blue-700 mb-3">
                      Private interface only the add-on user sees. Controls main stage activities.
                    </p>
                    <div className="text-xs text-blue-600">
                      Route: <code>/meet-addon/sidepanel</code>
                    </div>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h3 className="font-medium text-green-800 mb-2">Main Stage</h3>
                    <p className="text-sm text-green-700 mb-3">
                      Shared workspace all meeting participants can see and interact with.
                    </p>
                    <div className="text-xs text-green-600">
                      Route: <code>/meet-addon/mainstage</code>
                    </div>
                  </div>
                </div>

                <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-amber-800">Expected Development Behavior</h4>
                      <p className="text-sm text-amber-700 mt-1">
                        The "Missing required Meet SDK URL parameter: meet_sdk" error is expected when testing locally. 
                        This parameter is only provided when the add-on runs inside Google Meet.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Implementation Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="text-sm">Google Meet REST API integration (meeting creation)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="text-sm">Add-on side panel component</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="text-sm">Add-on main stage component</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="text-sm">Meeting join interface</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="text-sm">Development mode handling</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Component Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Click the buttons below to preview the add-on components in separate windows. 
                  This simulates how they would appear within Google Meet.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h3 className="font-medium">Side Panel Preview</h3>
                    <p className="text-sm text-muted-foreground">
                      Opens in a narrow window (400x600) to simulate the side panel experience.
                    </p>
                    <Button onClick={openSidePanel} className="w-full">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Preview Side Panel
                    </Button>
                  </div>
                  
                  <div className="space-y-3">
                    <h3 className="font-medium">Main Stage Preview</h3>
                    <p className="text-sm text-muted-foreground">
                      Opens in a large window (1200x800) to simulate the main stage experience.
                    </p>
                    <Button onClick={openMainStage} className="w-full">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Preview Main Stage
                    </Button>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-800 mb-2">What you'll see:</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Development mode interfaces with helpful instructions</li>
                    <li>• Demo functionality to preview user experience</li>
                    <li>• Clear indicators about deployment requirements</li>
                    <li>• Expected error messages with explanations</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="deployment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Deployment Checklist</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-3">
                    <h3 className="font-medium">1. Environment Variables</h3>
                    <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                      <div>NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_NUMBER=your_project_number</div>
                      <div>NEXT_PUBLIC_BASE_URL=https://yourapp.com</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h3 className="font-medium">2. Deploy to HTTPS Domain</h3>
                    <p className="text-sm text-muted-foreground">
                      Google Meet add-ons require HTTPS. Deploy your app to a secure domain.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <h3 className="font-medium">3. Google Cloud Console Configuration</h3>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Navigate to Google Workspace Add-ons</li>
                      <li>• Create a new Meet add-on deployment</li>
                      <li>• Set side panel URL: <code>https://yourapp.com/meet-addon/sidepanel</code></li>
                      <li>• Set main stage URL: <code>https://yourapp.com/meet-addon/mainstage</code></li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <h3 className="font-medium">4. Test in Google Meet</h3>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Create a Google Meet session</li>
                      <li>• Click meeting tools (apps icon)</li>
                      <li>• Find your add-on in "Your add-ons"</li>
                      <li>• Test side panel and main stage functionality</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="testing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Testing Guide</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <h3 className="font-medium">Local Development</h3>
                  <div className="bg-blue-50 p-3 rounded text-sm">
                    <p className="text-blue-800 mb-2">✅ Expected behavior:</p>
                    <ul className="text-blue-700 space-y-1">
                      <li>• "Missing required Meet SDK URL parameter" error</li>
                      <li>• Development mode interfaces</li>
                      <li>• Demo functionality preview</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-3">
                  <h3 className="font-medium">Production Testing</h3>
                  <div className="bg-green-50 p-3 rounded text-sm">
                    <p className="text-green-800 mb-2">✅ Full functionality:</p>
                    <ul className="text-green-700 space-y-1">
                      <li>• Google Meet SDK initialization</li>
                      <li>• Side panel controls</li>
                      <li>• Main stage activities</li>
                      <li>• Real-time synchronization</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                  <h4 className="font-medium text-amber-800 mb-2">Important Notes:</h4>
                  <ul className="text-sm text-amber-700 space-y-1">
                    <li>• The add-on only works when deployed and configured in Google Cloud</li>
                    <li>• Local testing shows development mode interfaces</li>
                    <li>• Full testing requires an actual Google Meet session</li>
                    <li>• HTTPS is required for production deployment</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              <Button onClick={openSidePanel} variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                Preview Side Panel
              </Button>
              <Button onClick={openMainStage} variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                Preview Main Stage
              </Button>
              <Button 
                onClick={() => window.open('/dashboard/meet', '_blank')} 
                variant="outline"
              >
                <Video className="h-4 w-4 mr-2" />
                Go to Meet Dashboard
              </Button>
              <Button 
                onClick={() => window.open('https://developers.google.com/workspace/meet/add-ons/guides/quickstart', '_blank')} 
                variant="outline"
              >
                <Code className="h-4 w-4 mr-2" />
                Google Docs
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
