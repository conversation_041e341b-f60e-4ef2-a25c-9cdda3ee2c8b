import { 
  makeMeetApiRequest,
  listConferenceRecords as apiListConferenceRecords,
  getConferenceRecord as apiGetConferenceRecord
} from './client'
import { ConferenceRecord, ListResponse, ConferenceFilter } from './types'

/**
 * Get a specific conference record
 */
export async function getConferenceRecord(
  userId: string,
  conferenceRecordName: string
): Promise<ConferenceRecord> {
  return await apiGetConferenceRecord(userId, conferenceRecordName)
}

/**
 * List conference records with optional filtering
 */
export async function listConferenceRecords(
  userId: string,
  options: {
    pageSize?: number
    pageToken?: string
    filter?: ConferenceFilter
  } = {}
): Promise<ListResponse<ConferenceRecord>> {
  
  let filterString = ''
  
  // Build filter string if provided
  if (options.filter) {
    const filters = []
    if (options.filter.startTime) {
      filters.push(`start_time >= "${options.filter.startTime}"`)
    }
    if (options.filter.endTime) {
      filters.push(`start_time <= "${options.filter.endTime}"`)
    }
    if (options.filter.space) {
      filters.push(`space = "${options.filter.space}"`)
    }
    
    if (filters.length > 0) {
      filterString = filters.join(' AND ')
    }
  }

  const response = await apiListConferenceRecords(userId, {
    pageSize: options.pageSize,
    pageToken: options.pageToken,
    filter: filterString || undefined
  })
  
  return {
    items: (response.conferenceRecords || []) as ConferenceRecord[],
    nextPageToken: response.nextPageToken,
    totalSize: response.conferenceRecords?.length
  }
}

/**
 * Get conference records for a specific meeting space
 */
export async function getSpaceConferenceRecords(
  userId: string,
  spaceName: string,
  options: {
    pageSize?: number
    pageToken?: string
    startTime?: string
    endTime?: string
  } = {}
): Promise<ListResponse<ConferenceRecord>> {
  return await listConferenceRecords(userId, {
    ...options,
    filter: {
      space: spaceName,
      startTime: options.startTime,
      endTime: options.endTime
    }
  })
}

/**
 * Get recent conference records
 */
export async function getRecentConferenceRecords(
  userId: string,
  days: number = 7,
  pageSize: number = 20
): Promise<ListResponse<ConferenceRecord>> {
  const endTime = new Date().toISOString()
  const startTime = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
  
  return await listConferenceRecords(userId, {
    pageSize,
    filter: {
      startTime,
      endTime
    }
  })
}

/**
 * Get conference statistics
 */
export async function getConferenceStatistics(
  userId: string,
  startTime?: string,
  endTime?: string
): Promise<{
  totalConferences: number
  totalDuration: number // in minutes
  averageDuration: number
  totalParticipants: number
  averageParticipants: number
  conferencesWithRecordings: number
  conferencesWithTranscripts: number
}> {
  const conferences = await listConferenceRecords(userId, {
    pageSize: 100, // Get more records for better statistics
    filter: {
      startTime,
      endTime
    }
  })
  
  let totalDuration = 0
  let totalParticipants = 0
  let conferencesWithRecordings = 0
  let conferencesWithTranscripts = 0
  
  // Note: This is a simplified calculation
  // In a real implementation, you would need to:
  // 1. Get participant counts for each conference
  // 2. Check for recordings and transcripts
  // 3. Calculate actual durations
  
  for (const conference of conferences.items) {
    if (conference.startTime && conference.endTime) {
      const duration = (new Date(conference.endTime).getTime() - new Date(conference.startTime).getTime()) / (1000 * 60)
      totalDuration += duration
    }
    
    // These would require additional API calls to get accurate counts
    totalParticipants += 1 // Placeholder
  }
  
  const totalConferences = conferences.items.length
  
  return {
    totalConferences,
    totalDuration,
    averageDuration: totalConferences > 0 ? totalDuration / totalConferences : 0,
    totalParticipants,
    averageParticipants: totalConferences > 0 ? totalParticipants / totalConferences : 0,
    conferencesWithRecordings,
    conferencesWithTranscripts
  }
}

/**
 * Get conference details with participants
 */
export async function getConferenceDetails(
  userId: string,
  conferenceRecordName: string
): Promise<{
  conference: ConferenceRecord
  participantCount: number
  duration?: number // in minutes
  hasRecording: boolean
  hasTranscript: boolean
}> {
  const conference = await getConferenceRecord(userId, conferenceRecordName)
  
  // Calculate duration if both start and end times are available
  let duration: number | undefined
  if (conference.startTime && conference.endTime) {
    duration = (new Date(conference.endTime).getTime() - new Date(conference.startTime).getTime()) / (1000 * 60)
  }
  
  // These would require additional API calls to check for recordings and transcripts
  // For now, we'll return placeholder values
  const hasRecording = false // Would check recordings API
  const hasTranscript = false // Would check transcripts API
  const participantCount = 0 // Would get from participants API
  
  return {
    conference,
    participantCount,
    duration,
    hasRecording,
    hasTranscript
  }
}

/**
 * Search conference records by criteria
 */
export async function searchConferenceRecords(
  userId: string,
  criteria: {
    spaceName?: string
    startDate?: string
    endDate?: string
    minDuration?: number // in minutes
    hasRecording?: boolean
    hasTranscript?: boolean
  },
  options: {
    pageSize?: number
    pageToken?: string
  } = {}
): Promise<ListResponse<ConferenceRecord>> {
  const filter: ConferenceFilter = {}
  
  if (criteria.spaceName) {
    filter.space = criteria.spaceName
  }
  
  if (criteria.startDate) {
    filter.startTime = criteria.startDate
  }
  
  if (criteria.endDate) {
    filter.endTime = criteria.endDate
  }
  
  // Get initial results
  const results = await listConferenceRecords(userId, {
    ...options,
    filter
  })
  
  // Additional filtering would be done client-side for criteria
  // not supported by the API filter (like minDuration, hasRecording, etc.)
  
  return results
}
