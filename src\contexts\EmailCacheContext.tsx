"use client"

import { createContext, useContext, useState, ReactNode } from 'react'
import { CachedEmail, CachedEmailList, EmailCache } from "./types"
// Define types for our cached data


interface EmailCacheContextType {
  cache: EmailCache
  getCachedEmails: (type: string, category?: string) => CachedEmailList | undefined
  setCachedEmails: (type: string, data: CachedEmailList, category?: string) => void
  getCachedEmailDetail: (emailId: string) => CachedEmail | undefined
  setCachedEmailDetail: (emailId: string, data: CachedEmail) => void
  updateCachedEmail: (emailId: string, updates: Partial<CachedEmail>) => void
  invalidateCache: (type?: string, category?: string) => void
  isCacheValid: (type: string, category?: string, maxAge?: number) => boolean
  preloadAllMailData: () => Promise<void>
  isPreloading: boolean
}

// Create the context
const EmailCacheContext = createContext<EmailCacheContextType | undefined>(undefined)

  // Cache expiration time in milliseconds (5 minutes)
  const CACHE_EXPIRATION = 5 * 60 * 1000

  interface DataPreloader {
    preloadAllMailData: () => Promise<void>
    isPreloading: boolean
  }

  export function EmailCacheProvider({ children }: { children: ReactNode }) {
    const [cache, setCache] = useState<EmailCache>({
      emailDetails: {},
      categories: {}
    })
    const [isPreloading, setIsPreloading] = useState(false)
    const [hasPreloaded, setHasPreloaded] = useState(false)

  // Get cached emails by type (inbox, spam, trash, category)
  const getCachedEmails = (type: string, category?: string): CachedEmailList | undefined => {
    console.log(`🔍 [CACHE DEBUG] getCachedEmails called with type: "${type}", category: "${category}"`)
    console.log(`🔍 [CACHE DEBUG] Current cache keys:`, Object.keys(cache))
    
    if (type === 'categories' && category) {
      const result = cache.categories[category]
      console.log(`🔍 [CACHE DEBUG] Category cache result for "${category}":`, !!result)
      return result
    }
    
    // Access cache using dynamic key, but check if it's a CachedEmailList
    const cacheEntry = cache[type]
    const result = cacheEntry &&
                   typeof cacheEntry === 'object' &&
                   'emails' in cacheEntry &&
                   'totalCount' in cacheEntry &&
                   'timestamp' in cacheEntry ? cacheEntry as CachedEmailList : undefined
    
    console.log(`🔍 [CACHE DEBUG] Direct cache result for "${type}":`, !!result)
    console.log(`🔍 [CACHE DEBUG] Available cache structure:`, {
      totalKeys: Object.keys(cache).length,
      categories: Object.keys(cache.categories),
      emailDetails: Object.keys(cache.emailDetails).length,
      dynamicKeys: Object.keys(cache).filter(k => k !== 'categories' && k !== 'emailDetails')
    })
    return result
  }

  // Set cached emails by type
  const setCachedEmails = (type: string, data: CachedEmailList, category?: string) => {
    console.log(`💾 [CACHE DEBUG] setCachedEmails called with type: "${type}", category: "${category}", emails: ${data.emails.length}`)
    
    setCache(prevCache => {
      if (type === 'categories' && category) {
        console.log(`💾 [CACHE DEBUG] Setting category cache for "${category}"`)
        return {
          ...prevCache,
          categories: {
            ...prevCache.categories,
            [category]: data
          }
        }
      }
      console.log(`💾 [CACHE DEBUG] Setting direct cache for "${type}"`)
      const newCache = {
        ...prevCache,
        [type]: data
      }
      console.log(`💾 [CACHE DEBUG] New cache structure after setting "${type}":`, Object.keys(newCache))
      return newCache
    })
  }

  // Get cached email detail
  const getCachedEmailDetail = (emailId: string): CachedEmail | undefined => {
    return cache.emailDetails[emailId]?.data
  }

  // Set cached email detail
  const setCachedEmailDetail = (emailId: string, data: CachedEmail) => {
    setCache(prevCache => ({
      ...prevCache,
      emailDetails: {
        ...prevCache.emailDetails,
        [emailId]: {
          data,
          timestamp: Date.now()
        }
      }
    }))
  }

  // Update a specific email in the cache (e.g., after starring)
  const updateCachedEmail = (emailId: string, updates: Partial<CachedEmail>) => {
    // Update in email details cache
    if (cache.emailDetails[emailId]) {
      setCache(prevCache => ({
        ...prevCache,
        emailDetails: {
          ...prevCache.emailDetails,
          [emailId]: {
            data: { ...prevCache.emailDetails[emailId].data, ...updates },
            timestamp: Date.now()
          }
        }
      }))
    }

    // Update in all list caches
    setCache(prevCache => {
      const newCache = { ...prevCache }
      
      // Update in all dynamic cache keys
      Object.keys(newCache).forEach(cacheKey => {
        if (cacheKey === 'categories' || cacheKey === 'emailDetails') return
        
        const cacheEntry = newCache[cacheKey]
        if (cacheEntry &&
            typeof cacheEntry === 'object' &&
            'emails' in cacheEntry &&
            Array.isArray(cacheEntry.emails)) {
          const emailIndex = cacheEntry.emails.findIndex((email: CachedEmail) => email.id === emailId)
          if (emailIndex >= 0) {
            cacheEntry.emails[emailIndex] = {
              ...cacheEntry.emails[emailIndex],
              ...updates
            }
          }
        }
      })
      
      // Update in categories if present
      Object.keys(newCache.categories).forEach(category => {
        const emailIndex = newCache.categories[category].emails.findIndex((email: CachedEmail) => email.id === emailId)
        if (emailIndex >= 0) {
          newCache.categories[category].emails[emailIndex] = {
            ...newCache.categories[category].emails[emailIndex],
            ...updates
          }
        }
      })
      
      return newCache
    })
  }

  // Invalidate cache for a specific type or all cache
  const invalidateCache = (type?: string, category?: string) => {
    if (!type) {
      // Invalidate all cache
      setCache({ emailDetails: {}, categories: {} })
      return
    }

    setCache(prevCache => {
      if (type === 'categories' && category) {
        const newCategories = { ...prevCache.categories }
        delete newCategories[category]
        return {
          ...prevCache,
          categories: newCategories
        }
      }
      
      const newCache = { ...prevCache }
      delete newCache[type as keyof typeof newCache]
      return newCache
    })
  }

  // Check if cache is still valid (not expired)
  const isCacheValid = (type: string, category?: string, maxAge: number = CACHE_EXPIRATION): boolean => {
    console.log(`⏰ [CACHE DEBUG] isCacheValid called for type: "${type}", category: "${category}", maxAge: ${maxAge}ms`)
    
    const cachedData = getCachedEmails(type, category)
    if (!cachedData) {
      console.log(`⏰ [CACHE DEBUG] No cached data found for "${type}"`)
      return false
    }
    
    const now = Date.now()
    const age = now - cachedData.timestamp
    const isValid = age < maxAge
    
    console.log(`⏰ [CACHE DEBUG] Cache age: ${age}ms, maxAge: ${maxAge}ms, isValid: ${isValid}`)
    return isValid
  }

  // Preload all mail data when user first loads the app
  const preloadAllMailData = async () => {
    if (isPreloading || hasPreloaded) return // Prevent concurrent preloading and re-running
    
    setIsPreloading(true)
    try {
      console.log('Starting mail data preloading...')
      
      // Get past 7 days date range for default preloading
      const today = new Date()
      today.setHours(23, 59, 59, 999)
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(today.getDate() - 7)
      sevenDaysAgo.setHours(0, 0, 0, 0)
      
      const dateParams = `&dateFrom=${sevenDaysAgo.toISOString()}&dateTo=${today.toISOString()}`
      
      // Preload essential mail data in parallel with small limits to avoid overwhelming
      const preloadPromises = [
        // Inbox (most important) - past 7 days
        fetch(`/api/gmail/inbox?limit=20${dateParams}`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.emails) {
              setCachedEmails('inbox_7days', {
                emails: data.emails.map((email: any) => ({
                  ...email,
                  date: new Date(email.date)
                })),
                totalCount: data.totalCount || 0,
                timestamp: Date.now(),
                currentPage: 1,
                totalPages: Math.ceil((data.totalCount || 0) / 20)
              })
              console.log('Preloaded inbox emails (7 days):', data.emails.length)
            }
          }
        }).catch(err => console.error('Failed to preload inbox:', err)),

        // Sent emails - past 7 days
        fetch(`/api/gmail/sent?limit=15${dateParams}`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.emails) {
              setCachedEmails('sent_7days', {
                emails: data.emails.map((email: any) => ({
                  ...email,
                  date: new Date(email.date)
                })),
                totalCount: data.totalCount || 0,
                timestamp: Date.now(),
                currentPage: 1,
                totalPages: Math.ceil((data.totalCount || 0) / 15)
              })
              console.log('Preloaded sent emails (7 days):', data.emails.length)
            }
          }
        }).catch(err => console.error('Failed to preload sent:', err)),

        // Drafts - past 7 days
        fetch(`/api/gmail/drafts?limit=10${dateParams}`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.emails) {
              setCachedEmails('drafts_7days', {
                emails: data.emails.map((email: any) => ({
                  ...email,
                  date: new Date(email.date)
                })),
                totalCount: data.totalCount || 0,
                timestamp: Date.now(),
                currentPage: 1,
                totalPages: Math.ceil((data.totalCount || 0) / 10)
              })
              console.log('Preloaded draft emails (7 days):', data.emails.length)
            }
          }
        }).catch(err => console.error('Failed to preload drafts:', err)),

        // Trash - past 7 days
        fetch(`/api/gmail/trash?limit=10${dateParams}`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.emails) {
              setCachedEmails('trash_7days', {
                emails: data.emails.map((email: any) => ({
                  ...email,
                  date: new Date(email.date)
                })),
                totalCount: data.totalCount || 0,
                timestamp: Date.now(),
                currentPage: 1,
                totalPages: Math.ceil((data.totalCount || 0) / 10)
              })
              console.log('Preloaded trash emails (7 days):', data.emails.length)
            }
          }
        }).catch(err => console.error('Failed to preload trash:', err)),

        // Spam - past 7 days
        fetch(`/api/gmail/spam?limit=10${dateParams}`).then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.emails) {
              setCachedEmails('spam_7days', {
                emails: data.emails.map((email: any) => ({
                  ...email,
                  date: new Date(email.date)
                })),
                totalCount: data.totalCount || 0,
                timestamp: Date.now(),
                currentPage: 1,
                totalPages: Math.ceil((data.totalCount || 0) / 10)
              })
              console.log('Preloaded spam emails (7 days):', data.emails.length)
            }
          }
        }).catch(err => console.error('Failed to preload spam:', err)),

        // Categories
        fetch('/api/gmail/categories?limit=10').then(async (response) => {
          if (response.ok) {
            const data = await response.json()
            if (data.categories) {
              Object.entries(data.categories).forEach(([category, emails]: [string, any]) => {
                if (Array.isArray(emails)) {
                  setCachedEmails('categories', {
                    emails: emails.map((email: any) => ({
                      ...email,
                      date: new Date(email.date)
                    })),
                    totalCount: emails.length,
                    timestamp: Date.now(),
                    currentPage: 1,
                    totalPages: 1
                  }, category)
                }
              })
              console.log('Preloaded category emails')
            }
          }
        }).catch(err => console.error('Failed to preload categories:', err))
      ]

      // Wait for all preloading to complete
      await Promise.allSettled(preloadPromises)
      setHasPreloaded(true) // Mark as preloaded to prevent re-running
      console.log('Mail data preloading completed')
      
    } catch (error) {
      console.error('Error during mail data preloading:', error)
    } finally {
      setIsPreloading(false)
    }
  }

  return (
    <EmailCacheContext.Provider value={{
      cache,
      getCachedEmails,
      setCachedEmails,
      getCachedEmailDetail,
      setCachedEmailDetail,
      updateCachedEmail,
      invalidateCache,
      isCacheValid,
      preloadAllMailData,
      isPreloading
    }}>
      {children}
    </EmailCacheContext.Provider>
  )
}

// Custom hook to use the email cache context
export function useEmailCache() {
  const context = useContext(EmailCacheContext)
  if (context === undefined) {
    throw new Error('useEmailCache must be used within an EmailCacheProvider')
  }
  return context
}
