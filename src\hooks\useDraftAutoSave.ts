import { useState, useEffect, useCallback, useRef } from 'react'

export interface DraftData {
  to: string
  cc?: string
  bcc?: string
  subject: string
  htmlBody: string
  textBody?: string
  threadId?: string
  replyToMessageId?: string
  messageId?: string
  references?: string
  inReplyTo?: string
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
}

export interface DraftAutoSaveOptions {
  debounceMs?: number
  enabled?: boolean
  onSaveSuccess?: (draftId: string) => void
  onSaveError?: (error: string) => void
  onDraftRecovered?: (draftId: string) => void
}

export interface DraftAutoSaveReturn {
  draftId: string | null
  isSaving: boolean
  lastSaved: Date | null
  hasUnsavedChanges: boolean
  saveDraft: () => Promise<void>
  deleteDraft: () => Promise<void>
  sendDraft: () => Promise<void>
  error: string | null
}

export function useDraftAutoSave(
  draftData: DraftData,
  options: DraftAutoSaveOptions = {}
): DraftAutoSaveReturn {
  const {
    debounceMs = 3000, // Auto-save every 3 seconds
    enabled = true,
    onSaveSuccess,
    onSaveError,
    onDraftRecovered
  } = options

  const [draftId, setDraftId] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastDraftDataRef = useRef<string>('')
  const isInitializedRef = useRef(false)

  // Check if draft data has meaningful content
  const hasContent = useCallback(() => {
    return draftData.to.trim() !== '' || 
           draftData.subject.trim() !== '' || 
           draftData.htmlBody.trim() !== ''
  }, [draftData])

  // Save draft function - Use existing API pattern
  const saveDraft = useCallback(async () => {
    if (!hasContent() || isSaving) return

    setIsSaving(true)
    setError(null)

    try {
      const endpoint = draftId ? '/api/gmail/drafts/update' : '/api/gmail/drafts/create'
      const method = draftId ? 'PUT' : 'POST'

      const payload = draftId ? { draftId, ...draftData } : draftData

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save draft')
      }

      const result = await response.json()

      if (result.success) {
        setDraftId(result.draftId)
        setLastSaved(new Date())
        setHasUnsavedChanges(false)
        onSaveSuccess?.(result.draftId)
      } else {
        throw new Error(result.error || 'Failed to save draft')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      onSaveError?.(errorMessage)
    } finally {
      setIsSaving(false)
    }
  }, [draftData, draftId, hasContent, isSaving, onSaveSuccess, onSaveError])

  // Delete draft function
  const deleteDraft = useCallback(async () => {
    if (!draftId) return

    try {
      const response = await fetch('/api/gmail/drafts/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ draftId })
      })

      if (response.ok) {
        setDraftId(null)
        setLastSaved(null)
        setHasUnsavedChanges(false)
        setError(null)
      }
    } catch (err) {
      console.error('Error deleting draft:', err)
    }
  }, [draftId])

  // Send draft function
  const sendDraft = useCallback(async () => {
    if (!draftId) return

    try {
      const response = await fetch('/api/gmail/drafts/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ draftId })
      })

      if (response.ok) {
        setDraftId(null)
        setLastSaved(null)
        setHasUnsavedChanges(false)
        setError(null)
      } else {
        throw new Error('Failed to send draft')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send draft'
      setError(errorMessage)
      throw err
    }
  }, [draftId])

  // Auto-save effect with debouncing
  useEffect(() => {
    if (!enabled || !isInitializedRef.current) {
      isInitializedRef.current = true
      return
    }

    const currentDraftData = JSON.stringify(draftData)
    
    // Check if data has changed
    if (currentDraftData !== lastDraftDataRef.current) {
      setHasUnsavedChanges(true)
      lastDraftDataRef.current = currentDraftData

      // Clear existing timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      // Set new timer for auto-save
      if (hasContent()) {
        debounceTimerRef.current = setTimeout(() => {
          saveDraft()
        }, debounceMs)
      }
    }

    // Cleanup timer on unmount
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [draftData, enabled, debounceMs, hasContent, saveDraft])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  return {
    draftId,
    isSaving,
    lastSaved,
    hasUnsavedChanges,
    saveDraft,
    deleteDraft,
    sendDraft,
    error
  }
}
