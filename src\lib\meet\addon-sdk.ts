/**
 * Google Meet Add-ons SDK Integration
 * This provides the proper way to embed Google Meet functionality
 * directly into our web application
 */

// Google Meet Add-ons SDK types
interface MeetAddonClient {
  createAddonSession(): Promise<AddonSession>
  getAddonSession(): AddonSession | null
}

interface AddonSession {
  getMeetingInfo(): Promise<MeetingInfo>
  getParticipants(): Promise<Participant[]>
  onParticipantJoined(callback: (participant: Participant) => void): void
  onParticipantLeft(callback: (participant: Participant) => void): void
  shareToMainStage(url: string): Promise<void>
  endMainStage(): Promise<void>
}

interface MeetingInfo {
  meetingId: string
  meetingCode: string
  organizer: Participant
}

interface Participant {
  id: string
  name: string
  email?: string
  isOrganizer: boolean
  isPresenting: boolean
}

// Global Meet SDK client
declare global {
  interface Window {
    meetAddonClient?: MeetAddonClient
    gapi?: any
  }
}

/**
 * Initialize Google Meet Add-ons SDK
 */
export async function initializeMeetAddonSDK(): Promise<MeetAddonClient> {
  return new Promise((resolve, reject) => {
    // Load Google Meet Add-ons SDK script
    if (!document.getElementById('meet-addon-sdk')) {
      const script = document.createElement('script')
      script.id = 'meet-addon-sdk'
      script.src = 'https://apis.google.com/js/api.js'
      script.onload = () => {
        // Initialize the Meet Add-ons SDK
        window.gapi.load('meet', {
          callback: () => {
            const client = window.gapi.meet.createAddonClient()
            window.meetAddonClient = client
            resolve(client)
          },
          onerror: (error: any) => {
            console.error('Failed to load Meet Add-ons SDK:', error)
            reject(error)
          }
        })
      }
      script.onerror = () => {
        reject(new Error('Failed to load Google APIs script'))
      }
      document.head.appendChild(script)
    } else if (window.meetAddonClient) {
      resolve(window.meetAddonClient)
    } else {
      reject(new Error('Meet Add-ons SDK not available'))
    }
  })
}

/**
 * Create an embedded meeting session
 */
export async function createEmbeddedMeetingSession(
  meetingSpaceUri: string
): Promise<AddonSession> {
  try {
    const client = await initializeMeetAddonSDK()
    const session = await client.createAddonSession()
    
    console.log('Created embedded meeting session:', session)
    return session
  } catch (error) {
    console.error('Failed to create embedded meeting session:', error)
    throw error
  }
}

/**
 * Get current meeting information
 */
export async function getCurrentMeetingInfo(): Promise<MeetingInfo | null> {
  try {
    const client = window.meetAddonClient
    if (!client) {
      throw new Error('Meet Add-ons SDK not initialized')
    }
    
    const session = client.getAddonSession()
    if (!session) {
      return null
    }
    
    return await session.getMeetingInfo()
  } catch (error) {
    console.error('Failed to get meeting info:', error)
    return null
  }
}

/**
 * Get current meeting participants
 */
export async function getCurrentParticipants(): Promise<Participant[]> {
  try {
    const client = window.meetAddonClient
    if (!client) {
      throw new Error('Meet Add-ons SDK not initialized')
    }
    
    const session = client.getAddonSession()
    if (!session) {
      return []
    }
    
    return await session.getParticipants()
  } catch (error) {
    console.error('Failed to get participants:', error)
    return []
  }
}

/**
 * Share content to main stage
 */
export async function shareToMainStage(contentUrl: string): Promise<void> {
  try {
    const client = window.meetAddonClient
    if (!client) {
      throw new Error('Meet Add-ons SDK not initialized')
    }
    
    const session = client.getAddonSession()
    if (!session) {
      throw new Error('No active meeting session')
    }
    
    await session.shareToMainStage(contentUrl)
  } catch (error) {
    console.error('Failed to share to main stage:', error)
    throw error
  }
}

/**
 * End main stage sharing
 */
export async function endMainStage(): Promise<void> {
  try {
    const client = window.meetAddonClient
    if (!client) {
      throw new Error('Meet Add-ons SDK not initialized')
    }
    
    const session = client.getAddonSession()
    if (!session) {
      throw new Error('No active meeting session')
    }
    
    await session.endMainStage()
  } catch (error) {
    console.error('Failed to end main stage:', error)
    throw error
  }
}

/**
 * Set up participant event listeners
 */
export function setupParticipantListeners(
  onJoined: (participant: Participant) => void,
  onLeft: (participant: Participant) => void
): void {
  const client = window.meetAddonClient
  if (!client) {
    console.error('Meet Add-ons SDK not initialized')
    return
  }
  
  const session = client.getAddonSession()
  if (!session) {
    console.error('No active meeting session')
    return
  }
  
  session.onParticipantJoined(onJoined)
  session.onParticipantLeft(onLeft)
}
