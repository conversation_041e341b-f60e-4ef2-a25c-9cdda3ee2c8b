'use client'

import { useEffect, useRef, useCallback, useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
    ArrowUpIcon,
    Paperclip,
    PlusIcon,
    User,
    Bot,
    Clock
} from "lucide-react";
import { ChatMessage } from "@/lib/gemini";
import { format } from "date-fns";

interface UseAutoResizeTextareaProps {
    minHeight: number;
    maxHeight?: number;
}

function useAutoResizeTextarea({
    minHeight,
    maxHeight,
}: UseAutoResizeTextareaProps) {
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const adjustHeight = useCallback(
        (reset?: boolean) => {
            const textarea = textareaRef.current;
            if (!textarea) return;

            if (reset) {
                textarea.style.height = `${minHeight}px`;
                return;
            }

            // Temporarily shrink to get the right scrollHeight
            textarea.style.height = `${minHeight}px`;

            // Calculate new height
            const newHeight = Math.max(
                minHeight,
                Math.min(
                    textarea.scrollHeight,
                    maxHeight ?? Number.POSITIVE_INFINITY
                )
            );

            textarea.style.height = `${newHeight}px`;
        },
        [minHeight, maxHeight]
    );

    useEffect(() => {
        // Set initial height
        const textarea = textareaRef.current;
        if (textarea) {
            textarea.style.height = `${minHeight}px`;
        }
    }, [minHeight]);

    // Adjust height on window resize
    useEffect(() => {
        const handleResize = () => adjustHeight();
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, [adjustHeight]);

    return { textareaRef, adjustHeight };
}

interface ChatInterfaceProps {
    onSendMessage: (message: string, context?: any) => Promise<string>;
    emailContext?: any[];
    messages: ChatMessage[];
    isLoading: boolean;
    className?: string;
}

export function ChatInterface({ 
    onSendMessage, 
    emailContext, 
    messages, 
    isLoading, 
    className = "" 
}: ChatInterfaceProps) {
    const [value, setValue] = useState("");
    const { textareaRef, adjustHeight } = useAutoResizeTextarea({
        minHeight: 60,
        maxHeight: 200,
    });
    const messagesEndRef = useRef<HTMLDivElement>(null);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            if (value.trim() && !isLoading) {
                handleSendMessage();
            }
        }
    };

    const handleSendMessage = async () => {
        if (!value.trim() || isLoading) return;

        const message = value;
        setValue("");
        adjustHeight(true);

        try {
            await onSendMessage(message, { emailContext });
        } catch (error) {
            console.error('Chat error:', error);
        }
    };

    const quickPrompts = [
        "Analyze my recent emails",
        "Show high priority emails",
        "Find meeting invitations",
        "Extract action items",
        "Summarize today's emails"
    ];

    return (
        <div className={`flex flex-col h-full bg-white ${className}`}>
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
                {messages.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-center">
                        <div className="max-w-2xl mx-auto space-y-6">
                            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
                                What can I help you with?
                            </h1>
                            <p className="text-lg text-gray-600 dark:text-gray-300">
                                I can analyze your emails, extract action items, schedule tasks, and help manage your inbox intelligently.
                            </p>
                            
                            {/* Quick Action Buttons */}
                            <div className="flex flex-wrap items-center justify-center gap-3 mt-8">
                                {quickPrompts.map((prompt) => (
                                    <button
                                        key={prompt}
                                        type="button"
                                        onClick={() => {
                                            setValue(prompt);
                                            setTimeout(() => handleSendMessage(), 100);
                                        }}
                                        className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded-full border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors text-sm"
                                    >
                                        <span>{prompt}</span>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                ) : (
                    <>
                        {messages.map((message) => (
                            <div key={message.id} className="flex gap-4">
                                <div className="flex-shrink-0">
                                    {message.role === 'user' ? (
                                        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                                            <User className="w-4 h-4 text-white" />
                                        </div>
                                    ) : (
                                        <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                                            <Bot className="w-4 h-4 text-white" />
                                        </div>
                                    )}
                                </div>
                                <div className="flex-1 space-y-2">
                                    <div className="flex items-center gap-2">
                                        <span className="font-medium text-gray-900 dark:text-white">
                                            {message.role === 'user' ? 'You' : 'AI Assistant'}
                                        </span>
                                        <span className="text-xs text-gray-500 flex items-center gap-1">
                                            <Clock className="w-3 h-3" />
                                            {format(message.timestamp, 'HH:mm')}
                                        </span>
                                    </div>
                                    <div className="prose prose-sm max-w-none text-gray-800 dark:text-gray-200">
                                        {message.role === 'assistant' ? (
                                            <div 
                                                className="ai-response"
                                                dangerouslySetInnerHTML={{ __html: message.content }}
                                            />
                                        ) : (
                                            message.content.split('\n').map((line, index) => (
                                                <p key={index} className="mb-2 last:mb-0">{line}</p>
                                            ))
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                        {isLoading && (
                            <div className="flex gap-4">
                                <div className="flex-shrink-0">
                                    <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                                        <Bot className="w-4 h-4 text-white" />
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <span className="font-medium text-gray-900 dark:text-white">AI Assistant</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-gray-500">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                                        <span className="text-sm">Thinking...</span>
                                    </div>
                                </div>
                            </div>
                        )}
                        <div ref={messagesEndRef} />
                    </>
                )}
            </div>

            {/* Input Area */}
            <div className="border-t border-gray-200 dark:border-gray-700 p-6">
                <div className="max-w-4xl mx-auto">
                    <div className="relative bg-gray-100 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
                        <div className="overflow-y-auto">
                            <Textarea
                                ref={textareaRef}
                                value={value}
                                onChange={(e) => {
                                    setValue(e.target.value);
                                    adjustHeight();
                                }}
                                onKeyDown={handleKeyDown}
                                placeholder="Ask me about your emails..."
                                className={cn(
                                    "w-full px-4 py-3",
                                    "resize-none",
                                    "bg-transparent",
                                    "border-none",
                                    "text-gray-900 dark:text-white text-sm",
                                    "focus:outline-none",
                                    "focus-visible:ring-0 focus-visible:ring-offset-0",
                                    "placeholder:text-gray-500 placeholder:text-sm",
                                    "min-h-[60px]"
                                )}
                                style={{
                                    overflow: "hidden",
                                }}
                                disabled={isLoading}
                            />
                        </div>

                        <div className="flex items-center justify-between p-3">
                            <div className="flex items-center gap-2">
                                <button
                                    type="button"
                                    className="group p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-1"
                                >
                                    <Paperclip className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                                    <span className="text-xs text-gray-500 dark:text-gray-400 hidden group-hover:inline transition-opacity">
                                        Attach
                                    </span>
                                </button>
                            </div>
                            <div className="flex items-center gap-2">
                                <button
                                    type="button"
                                    className="px-2 py-1 rounded-lg text-sm text-gray-500 dark:text-gray-400 transition-colors border border-dashed border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-between gap-1"
                                >
                                    <PlusIcon className="w-4 h-4" />
                                    Context
                                </button>
                                <button
                                    type="button"
                                    onClick={handleSendMessage}
                                    disabled={!value.trim() || isLoading}
                                    className={cn(
                                        "px-1.5 py-1.5 rounded-lg text-sm transition-colors border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-between gap-1",
                                        value.trim() && !isLoading
                                            ? "bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
                                            : "text-gray-400 dark:text-gray-500"
                                    )}
                                >
                                    <ArrowUpIcon
                                        className={cn(
                                            "w-4 h-4",
                                            value.trim() && !isLoading
                                                ? "text-white"
                                                : "text-gray-400 dark:text-gray-500"
                                        )}
                                    />
                                    <span className="sr-only">Send</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
} 