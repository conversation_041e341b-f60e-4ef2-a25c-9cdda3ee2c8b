import { NextRequest, NextResponse } from "next/server"
import { google } from 'googleapis'
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state') // This is the user ID
    
    if (!code || !state) {
      return NextResponse.redirect(new URL('/dashboard/meet?error=missing_params', request.url))
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXTAUTH_URL}/api/meet/callback`
    )

    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code)
    
    if (!tokens.refresh_token) {
      return NextResponse.redirect(new URL('/dashboard/meet?error=no_refresh_token', request.url))
    }

    // Update user with Meet tokens
    await prisma.user.update({
      where: { id: state },
      data: {
        meetConnected: true,
        meetRefreshToken: tokens.refresh_token
      }
    })

    console.log('Meet connected successfully for user:', state)
    return NextResponse.redirect(new URL('/dashboard/meet?connected=true', request.url))

  } catch (error) {
    console.error('Meet callback error:', error)
    return NextResponse.redirect(new URL('/dashboard/meet?error=callback_failed', request.url))
  }
}
