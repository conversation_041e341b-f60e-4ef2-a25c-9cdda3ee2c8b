'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  X, 
  MapPin, 
  Users, 
  Bell, 
  Calendar as CalendarIcon,
  Clock,
  Video,
  ChevronDown,
  Plus,
  Trash2
} from 'lucide-react'
import { GoogleCalendarEvent } from '@/lib/calendar-types'

interface EventCreationModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (eventData: Partial<GoogleCalendarEvent>) => Promise<void>
  initialData?: Partial<GoogleCalendarEvent>
  userTimezone?: string
}

interface NotificationSetting {
  method: 'email' | 'popup'
  minutes: number
}

export function EventCreationModal({ 
  isOpen, 
  onClose, 
  onSave, 
  initialData,
  userTimezone 
}: EventCreationModalProps) {
  const [loading, setLoading] = useState(false)
  const [title, setTitle] = useState('')
  const [startDate, setStartDate] = useState('')
  const [startTime, setStartTime] = useState('')
  const [endDate, setEndDate] = useState('')
  const [endTime, setEndTime] = useState('')
  const [isAllDay, setIsAllDay] = useState(false)
  const [location, setLocation] = useState('')
  const [description, setDescription] = useState('')
  const [attendees, setAttendees] = useState<string[]>([])
  const [newAttendee, setNewAttendee] = useState('')
  const [addGoogleMeet, setAddGoogleMeet] = useState(false)
  const [notifications, setNotifications] = useState<NotificationSetting[]>([
    { method: 'popup', minutes: 10 }
  ])
  const [repeatOption, setRepeatOption] = useState('none')
  const [visibility, setVisibility] = useState('default')
  const [showFreeBusy, setShowFreeBusy] = useState(true)
  const [guestCanModify, setGuestCanModify] = useState(false)
  const [guestCanInvite, setGuestCanInvite] = useState(true)
  const [guestCanSeeList, setGuestCanSeeList] = useState(true)

  // Get user's timezone
  const timezone = userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone

  useEffect(() => {
    if (initialData) {
      setTitle(initialData.summary || '')
      setDescription(initialData.description || '')
      setLocation(initialData.location || '')
      
      if (initialData.start?.dateTime) {
        const startDateTime = new Date(initialData.start.dateTime)
        setStartDate(startDateTime.toISOString().split('T')[0])
        setStartTime(startDateTime.toTimeString().slice(0, 5))
      }
      
      if (initialData.end?.dateTime) {
        const endDateTime = new Date(initialData.end.dateTime)
        setEndDate(endDateTime.toISOString().split('T')[0])
        setEndTime(endDateTime.toTimeString().slice(0, 5))
      }
      
      if (initialData.attendees) {
        setAttendees(initialData.attendees.map(a => a.email))
      }
      
      if (initialData.conferenceData?.entryPoints?.length) {
        setAddGoogleMeet(true)
      }
    } else {
      // Set default values for new event
      const now = new Date()
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000)
      
      setStartDate(now.toISOString().split('T')[0])
      setStartTime(now.toTimeString().slice(0, 5))
      setEndDate(oneHourLater.toISOString().split('T')[0])
      setEndTime(oneHourLater.toTimeString().slice(0, 5))
    }
  }, [initialData])

  const handleAddAttendee = () => {
    if (newAttendee && !attendees.includes(newAttendee)) {
      setAttendees([...attendees, newAttendee])
      setNewAttendee('')
    }
  }

  const handleRemoveAttendee = (email: string) => {
    setAttendees(attendees.filter(a => a !== email))
  }

  const handleAddNotification = () => {
    setNotifications([...notifications, { method: 'popup', minutes: 10 }])
  }

  const handleRemoveNotification = (index: number) => {
    setNotifications(notifications.filter((_, i) => i !== index))
  }

  const handleNotificationChange = (index: number, field: keyof NotificationSetting, value: any) => {
    const updated = [...notifications]
    updated[index] = { ...updated[index], [field]: value }
    setNotifications(updated)
  }

  const handleSave = async () => {
    if (!title.trim()) return

    setLoading(true)
    try {
      const eventData: Partial<GoogleCalendarEvent> = {
        summary: title,
        description,
        location,
        start: isAllDay ? {
          date: startDate,
          timeZone: timezone
        } : {
          dateTime: new Date(`${startDate}T${startTime}`).toISOString(),
          timeZone: timezone
        },
        end: isAllDay ? {
          date: endDate || startDate,
          timeZone: timezone
        } : {
          dateTime: new Date(`${endDate}T${endTime}`).toISOString(),
          timeZone: timezone
        },
        attendees: attendees.filter(email => email && email.trim() && email.includes('@')).map(email => ({ email: email.trim() })),
        reminders: {
          useDefault: false,
          overrides: notifications.map(n => ({
            method: n.method,
            minutes: n.minutes
          }))
        }
      }

      // Add conference data if Google Meet is requested
      if (addGoogleMeet) {
        (eventData as any).conferenceData = {
          createRequest: {
            requestId: `meet-${Date.now()}`,
            conferenceSolutionKey: { type: 'hangoutsMeet' }
          }
        }
      }

      await onSave(eventData)
      onClose()
    } catch (error) {
      console.error('Error saving event:', error)
    } finally {
      setLoading(false)
    }
  }

  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2)
    const minute = i % 2 === 0 ? '00' : '30'
    const time = `${hour.toString().padStart(2, '0')}:${minute}`
    const display = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
    return { value: time, label: display }
  })

  const notificationOptions = [
    { value: 0, label: 'At time of event' },
    { value: 5, label: '5 minutes before' },
    { value: 10, label: '10 minutes before' },
    { value: 15, label: '15 minutes before' },
    { value: 30, label: '30 minutes before' },
    { value: 60, label: '1 hour before' },
    { value: 120, label: '2 hours before' },
    { value: 1440, label: '1 day before' },
    { value: 2880, label: '2 days before' },
    { value: 10080, label: '1 week before' }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-xl font-semibold">
            {initialData ? 'Edit Event' : 'New Event'}
          </DialogTitle>
          <div className="flex items-center gap-2">
            <Button onClick={handleSave} disabled={loading || !title.trim()}>
              {loading ? 'Saving...' : 'Save'}
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Title */}
          <div>
            <Input
              placeholder="Add title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="text-lg font-medium border-0 px-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* Date and Time */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-auto"
                />
                {!isAllDay && (
                  <Select value={startTime} onValueChange={setStartTime}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {timeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
              
              <span className="text-muted-foreground">to</span>
              
              <div className="flex items-center gap-2">
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-auto"
                />
                {!isAllDay && (
                  <Select value={endTime} onValueChange={setEndTime}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {timeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="all-day"
                  checked={isAllDay}
                  onCheckedChange={(checked) => setIsAllDay(checked === true)}
                />
                <Label htmlFor="all-day">All day</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Select value={repeatOption} onValueChange={setRepeatOption}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Does not repeat" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Does not repeat</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="text-sm text-muted-foreground">
                Time zone: {timezone}
              </div>
            </div>
          </div>

          {/* Google Meet */}
          <div className="flex items-center space-x-2">
            <Video className="h-4 w-4 text-blue-600" />
            <Checkbox
              id="google-meet"
              checked={addGoogleMeet}
              onCheckedChange={(checked) => setAddGoogleMeet(checked === true)}
            />
            <Label htmlFor="google-meet">Add Google Meet video conferencing</Label>
          </div>

          {/* Location */}
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Add location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              className="flex-1 border-0 px-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Textarea
              placeholder="Add description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="min-h-24 border-0 px-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* Attendees */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1">
                <Input
                  placeholder="Add guests"
                  value={newAttendee}
                  onChange={(e) => setNewAttendee(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddAttendee()}
                  className="border-0 px-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
              <Button size="sm" onClick={handleAddAttendee} disabled={!newAttendee}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            {attendees.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {attendees.map((email) => (
                  <Badge key={email} variant="secondary" className="flex items-center gap-1">
                    {email}
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleRemoveAttendee(email)}
                      className="h-auto p-0 hover:bg-transparent"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Guest Permissions */}
            {attendees.length > 0 && (
              <div className="space-y-2 pl-6">
                <h4 className="text-sm font-medium">Guest permissions</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="guest-modify"
                      checked={guestCanModify}
                      onCheckedChange={(checked) => setGuestCanModify(checked === true)}
                    />
                    <Label htmlFor="guest-modify" className="text-sm">Modify event</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="guest-invite"
                      checked={guestCanInvite}
                      onCheckedChange={(checked) => setGuestCanInvite(checked === true)}
                    />
                    <Label htmlFor="guest-invite" className="text-sm">Invite others</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="guest-see-list"
                      checked={guestCanSeeList}
                      onCheckedChange={(checked) => setGuestCanSeeList(checked === true)}
                    />
                    <Label htmlFor="guest-see-list" className="text-sm">See guest list</Label>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Notifications */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Bell className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Notifications</span>
            </div>
            
            {notifications.map((notification, index) => (
              <div key={index} className="flex items-center space-x-2 pl-6">
                <Select 
                  value={notification.method} 
                  onValueChange={(value) => handleNotificationChange(index, 'method', value)}
                >
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="popup">Notification</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select 
                  value={notification.minutes.toString()} 
                  onValueChange={(value) => handleNotificationChange(index, 'minutes', parseInt(value))}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationOptions.map(option => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleRemoveNotification(index)}
                  disabled={notifications.length === 1}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <Button
              size="sm"
              variant="outline"
              onClick={handleAddNotification}
              className="ml-6"
            >
              Add notification
            </Button>
          </div>

          {/* Calendar and Visibility */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Calendar</span>
              <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
              <span className="text-sm font-medium">Primary</span>
            </div>
            
            <Select value={visibility} onValueChange={setVisibility}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default visibility</SelectItem>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="private">Private</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 