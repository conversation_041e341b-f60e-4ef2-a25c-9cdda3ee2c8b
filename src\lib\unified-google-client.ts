import { google } from 'googleapis'
import { prisma } from './prisma'

export type GoogleService = 'gmail'

export interface GoogleClient {
  gmail?: any
  userEmail: string
  auth: any
}

export interface ConnectionTestResult {
  connected: boolean
  error?: string
}

/**
 * Unified Google API client for Gmail service
 * Simplified to focus on Gmail API only - can be extended later
 */
class UnifiedGoogleClientService {
  
  /**
   * Create OAuth2 client with user's credentials
   */
  private async createAuthClient(userId: string, service: GoogleService) {
    // Get user's OAuth tokens from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        gmailRefreshToken: true,
        gmailConnected: true,
        email: true
      }
    })

    if (!user) {
      throw new Error('User not found')
    }

    // Check Gmail connection
    if (!user.gmailConnected || !user.gmailRefreshToken) {
      throw new Error('Gmail not connected for this user')
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    // Set the refresh token
    oauth2Client.setCredentials({
      refresh_token: user.gmailRefreshToken
    })

    return { auth: oauth2Client, userEmail: user.email }
  }

  /**
   * Handle authentication errors and update user connection status
   */
  private async handleAuthError(error: Error, userId: string, service: GoogleService) {
    console.error(`Error creating ${service} client:`, error)
    
    // If the error is about insufficient scopes, mark user as needing reconnection
    if (error.message.includes('insufficient authentication scopes')) {
      await prisma.user.update({
        where: { id: userId },
        data: { 
          gmailConnected: false, 
          gmailRefreshToken: null 
        }
      })
      
      throw new Error('Gmail permissions need to be updated. Please reconnect your Google account.')
    }
    
    throw error
  }

  /**
   * Get Gmail client for a user
   */
  async getGmailClient(userId: string): Promise<{ gmail: any; userEmail: string; auth: any }> {
    try {
      const { auth, userEmail } = await this.createAuthClient(userId, 'gmail')
      
      // Create Gmail client
      const gmail = google.gmail({ version: 'v1', auth })

      return { gmail, userEmail, auth }
    } catch (error) {
      await this.handleAuthError(error as Error, userId, 'gmail')
      throw error // Ensure we always throw after handling error
    }
  }

  /**
   * Get unified client with Gmail service
   */
  async getUnifiedClient(userId: string): Promise<GoogleClient> {
    try {
      // Get user data to check Gmail connection
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          gmailConnected: true,
          gmailRefreshToken: true,
          email: true
        }
      })

      if (!user) {
        throw new Error('User not found')
      }

      if (!user.gmailRefreshToken) {
        throw new Error('No Google services connected for this user')
      }

      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.NEXTAUTH_URL + '/api/auth/callback/google'
      )

      oauth2Client.setCredentials({
        refresh_token: user.gmailRefreshToken
      })

      const client: GoogleClient = {
        userEmail: user.email || '',
        auth: oauth2Client
      }

      // Add Gmail client if connected
      if (user.gmailConnected && user.gmailRefreshToken) {
        client.gmail = google.gmail({ version: 'v1', auth: oauth2Client })
      }

      return client
    } catch (error) {
      console.error('Error creating unified Google client:', error)
      throw error
    }
  }

  /**
   * Test Gmail connection
   */
  async testGmailConnection(userId: string): Promise<ConnectionTestResult> {
    try {
      const client = await this.getGmailClient(userId)
      if (!client || !client.gmail) {
        return { connected: false, error: 'Gmail client not available' }
      }
      
      // Try to get user profile to test connection
      await client.gmail.users.getProfile({ userId: 'me' })
      
      return { connected: true }
    } catch (error) {
      console.error('Gmail connection test failed:', error)
      return {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get user's Gmail connection status
   */
  async getConnectionStatus(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        gmailConnected: true,
        gmailRefreshToken: true,
        email: true
      }
    })

    if (!user) {
      throw new Error('User not found')
    }

    return {
      gmail: {
        connected: user.gmailConnected && !!user.gmailRefreshToken,
        hasToken: !!user.gmailRefreshToken
      },
      userEmail: user.email
    }
  }

  /**
   * Disconnect Gmail service
   */
  async disconnectService(userId: string, service: GoogleService) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        gmailConnected: false,
        gmailRefreshToken: null
      }
    })

    return { success: true, message: 'Gmail disconnected successfully' }
  }

  /**
   * Disconnect all services (currently just Gmail)
   */
  async disconnectAllServices(userId: string) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        gmailConnected: false,
        gmailRefreshToken: null
      }
    })

    return { success: true, message: 'Gmail disconnected. Please re-authenticate.' }
  }
}

// Export singleton instance
export const unifiedGoogleClient = new UnifiedGoogleClientService()

// Export backward compatibility functions
export const getGmailClient = (userId: string) => unifiedGoogleClient.getGmailClient(userId)
export const testGmailConnection = (userId: string) => unifiedGoogleClient.testGmailConnection(userId)

// Placeholder functions for calendar (to be implemented when Prisma is regenerated)
export const getCalendarClient = async (userId: string) => {
  throw new Error('Calendar client not available yet - Prisma client needs regeneration')
}

export const testCalendarConnection = async (userId: string) => {
  return { connected: false, error: 'Calendar client not available yet' }
} 