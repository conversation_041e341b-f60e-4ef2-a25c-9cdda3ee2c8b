"use client"

import { useSession, signOut } from "next-auth/react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { AppSidebar } from "@/components/dashboard/sidebar"
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { UnifiedCacheProvider, useUnifiedCache } from "@/contexts/cache"

// Component to handle comprehensive data preloading
function UnifiedDataPreloader({ children }: { children: React.ReactNode }) {
  const { preloadAllData, isPreloading } = useUnifiedCache()

  useEffect(() => {
    console.log('🚀 [UNIFIED PRELOADER] Starting comprehensive data preloading...')
    preloadAllData()
  }, [preloadAllData])

  console.log('🔄 [UNIFIED PRELOADER] isPreloading:', isPreloading)
  return <>{children}</>
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return
    if (!session) router.push("/auth/signin")
  }, [session, status, router])

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" })
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <UnifiedCacheProvider>
      <UnifiedDataPreloader>
        <SidebarProvider>
          <AppSidebar
            gmailConnected={session?.user?.email ? true : false}
            meetConnected={session?.user?.meetConnected || false}
            user={session?.user?.email ? { ...session.user, id: session.user.email, email: session.user.email } : undefined}
            onSignOut={handleSignOut}
          />
          <SidebarInset>
            <div className="flex flex-1 flex-col p-0">
              {children}
            </div>
          </SidebarInset>
        </SidebarProvider>
      </UnifiedDataPreloader>
    </UnifiedCacheProvider>
  )
}
