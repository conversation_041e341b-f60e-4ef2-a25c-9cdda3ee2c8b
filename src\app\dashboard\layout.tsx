"use client"

import { useSession, signOut } from "next-auth/react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { AppSidebar } from "@/components/dashboard/sidebar"
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { EmailCacheProvider, useEmailCache } from "@/contexts/EmailCacheContext"

// Component to handle data preloading (temporarily disabled for debugging)
function DataPreloader({ children }: { children: React.ReactNode }) {
  console.log('🚫 [PRELOADER DEBUG] DataPreloader is DISABLED - preloading will not occur')
  console.log('🚫 [PRELOADER DEBUG] This means cache will not be populated on app load')
  
  // Temporarily disabled to debug pagination issues
  return <>{children}</>
}

// Component that would normally handle preloading (for comparison)
function ActiveDataPreloader({ children }: { children: React.ReactNode }) {
  const { preloadAllMailData, isPreloading } = useEmailCache()
  
  useEffect(() => {
    console.log('🔄 [PRELOADER DEBUG] ActiveDataPreloader starting preload...')
    preloadAllMailData()
  }, [preloadAllMailData])
  
  console.log('🔄 [PRELOADER DEBUG] ActiveDataPreloader isPreloading:', isPreloading)
  return <>{children}</>
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return
    if (!session) router.push("/auth/signin")
  }, [session, status, router])

  const handleSignOut = () => {
    signOut({ callbackUrl: "/" })
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <EmailCacheProvider>
      <ActiveDataPreloader>
        <SidebarProvider>
          <AppSidebar
            gmailConnected={session?.user?.email ? true : false}
            meetConnected={session?.user?.meetConnected || false}
            user={session?.user?.email ? { ...session.user, id: session.user.email, email: session.user.email } : undefined}
            onSignOut={handleSignOut}
          />
          <SidebarInset>
            <div className="flex flex-1 flex-col p-0">
              {children}
            </div>
          </SidebarInset>
        </SidebarProvider>
      </ActiveDataPreloader>
    </EmailCacheProvider>
  )
}
