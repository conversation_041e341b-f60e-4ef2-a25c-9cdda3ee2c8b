import { google } from 'googleapis'
import { prisma } from '../prisma'

export async function getGmailClient(userId: string) {
  try {
    // Get user's OAuth tokens from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        gmailRefreshToken: true,
        gmailConnected: true,
        email: true
      }
    })

    if (!user || !user.gmailRefreshToken || !user.gmailConnected) {
      throw new Error('Gmail not connected for this user')
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    // Set the refresh token
    oauth2Client.setCredentials({
      refresh_token: user.gmailRefreshToken
    })

    // Create Gmail client
    const gmail = google.gmail({ version: 'v1', auth: oauth2Client })

    return { gmail, userEmail: user.email }
  } catch (error) {
    console.error('Error creating Gmail client:', error)
    
    // If the error is about insufficient scopes, mark user as needing reconnection
    if (error instanceof Error && error.message.includes('insufficient authentication scopes')) {
      await prisma.user.update({
        where: { id: userId },
        data: {
          gmailConnected: false,
          gmailRefreshToken: null
        }
      })
      throw new Error('Gmail permissions need to be updated. Please reconnect your Gmail account.')
    }
    
    throw error
  }
}

export async function getCalendarClient(userId: string) {
  try {
    // Get user's OAuth tokens from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        calendarRefreshToken: true,
        calendarConnected: true,
        email: true
      }
    })

    if (!user || !user.calendarRefreshToken || !user.calendarConnected) {
      throw new Error('Google Calendar not connected for this user')
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    // Set the refresh token
    oauth2Client.setCredentials({
      refresh_token: user.calendarRefreshToken
    })

    // Create Calendar client
    const calendar = google.calendar({ version: 'v3', auth: oauth2Client })

    return { calendar, userEmail: user.email }
  } catch (error) {
    console.error('Error creating Calendar client:', error)
    
    // If the error is about insufficient scopes, mark user as needing reconnection
    if (error instanceof Error && error.message.includes('insufficient authentication scopes')) {
      await prisma.user.update({
        where: { id: userId },
        data: {
          calendarConnected: false,
          calendarRefreshToken: null
        }
      })
      throw new Error('Calendar permissions need to be updated. Please reconnect your Google account.')
    }
    
    throw error
  }
}

export async function testGmailConnection(userId: string): Promise<{connected: boolean, error?: string}> {
  try {
    const { gmail } = await getGmailClient(userId)
    
    // Try to get user profile to test connection
    await gmail.users.getProfile({ userId: 'me' })
    
    return { connected: true }
  } catch (error) {
    console.error('Gmail connection test failed:', error)
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function testCalendarConnection(userId: string): Promise<{connected: boolean, error?: string}> {
  try {
    const { calendar } = await getCalendarClient(userId)

    // Try to get user's primary calendar to test connection
    await calendar.calendars.get({ calendarId: 'primary' })

    return { connected: true }
  } catch (error) {
    console.error('Calendar connection test failed:', error)
    return {
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

