"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  BarChart3, 
  Mail, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Clock,
  RefreshCw
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"

interface EmailStats {
  overall: Record<string, number>
  today: Record<string, number>
}

interface SentEmail {
  id: string
  status: string
  sentAt: string
  contact: {
    email: string
    firstName?: string
    lastName?: string
  }
  campaign?: {
    name: string
    subject: string
  } | null
}

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState("30d")
  const [stats, setStats] = useState<EmailStats>({ overall: {}, today: {} })
  const [recentEmails, setRecentEmails] = useState<SentEmail[]>([])
  const [loading, setLoading] = useState(true)

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/emails/history?page=1&limit=10')
      if (response.ok) {
        const data = await response.json()
        setStats(data.stats || { overall: {}, today: {} })
        setRecentEmails(data.emails || [])
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange])

  const getTotalEmails = () => {
    return Object.values(stats.overall).reduce((sum, count) => sum + count, 0)
  }

  const getDeliveryRate = () => {
    const total = getTotalEmails()
    const sent = stats.overall.SENT || 0
    return total > 0 ? ((sent / total) * 100).toFixed(1) : "0"
  }

  const getFailureRate = () => {
    const total = getTotalEmails()
    const failed = (stats.overall.FAILED || 0) + (stats.overall.BOUNCED || 0)
    return total > 0 ? ((failed / total) * 100).toFixed(1) : "0"
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
            <p className="text-muted-foreground">Loading analytics data...</p>
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 animate-pulse mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const totalEmails = getTotalEmails()

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
          <p className="text-muted-foreground">
            Track your email delivery performance and history. Real-time data from your email sending activity.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={fetchAnalytics} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Emails Sent</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEmails.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {Object.values(stats.today).reduce((sum, count) => sum + count, 0)} today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successfully Sent</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.overall.SENT || 0}</div>
            <p className="text-xs text-muted-foreground">
              {getDeliveryRate()}% delivery rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed/Bounced</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {(stats.overall.FAILED || 0) + (stats.overall.BOUNCED || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {getFailureRate()}% failure rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.overall.PENDING || 0}</div>
            <p className="text-xs text-muted-foreground">
              In queue
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Email Activity */}
      {recentEmails.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Email Activity</CardTitle>
                <CardDescription>
                  Latest emails sent from your account
                </CardDescription>
              </div>
              <Link href="/dashboard/emails">
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentEmails.slice(0, 5).map((email) => (
                <div key={email.id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {email.status === 'SENT' && <CheckCircle className="h-4 w-4 text-green-600" />}
                    {email.status === 'FAILED' && <XCircle className="h-4 w-4 text-red-600" />}
                    {email.status === 'PENDING' && <Clock className="h-4 w-4 text-yellow-600" />}
                    {email.status === 'BOUNCED' && <AlertTriangle className="h-4 w-4 text-orange-600" />}
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {email.contact.firstName} {email.contact.lastName} ({email.contact.email})
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {email.campaign ? email.campaign.subject : 'Direct email'}
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(email.sentAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {totalEmails === 0 && (
        <div className="flex flex-col items-center justify-center space-y-6 py-16">
          <BarChart3 className="h-12 w-12 text-muted-foreground" />
          <div className="text-center space-y-2">
            <h3 className="text-lg font-medium">No Email Data Yet</h3>
            <p className="text-muted-foreground max-w-md">
              Start sending emails to see analytics and performance metrics here. 
              Your delivery rates, success rates, and email activity will be tracked automatically.
            </p>
          </div>
          <div className="flex space-x-3">
            <Link href="/dashboard/compose">
              <Button>
                <Mail className="mr-2 h-4 w-4" />
                Send Your First Email
              </Button>
            </Link>
            <Link href="/dashboard/campaigns/new">
              <Button variant="outline">
                <BarChart3 className="mr-2 h-4 w-4" />
                Create Campaign
              </Button>
            </Link>
          </div>
        </div>
      )}

      {/* Advanced Analytics Coming Soon */}
      <Card className="opacity-60">
        <CardHeader>
          <CardTitle>Advanced Analytics</CardTitle>
          <CardDescription>
            Coming soon: Open rates, click tracking, geographic data, and more detailed insights.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Detailed charts and insights coming soon</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 