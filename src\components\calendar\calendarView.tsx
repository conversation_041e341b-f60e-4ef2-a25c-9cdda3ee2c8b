"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Plus,
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  Users,
  Video,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight,
  ExternalLink,
  Settings,
  Search,
  TrendingUp,
  Sparkles,
  Bell,
  Filter,
  ChevronDown,
  Zap,
  BarChart3,
  Target,
  MoreHorizontal,
  Edit,
  Trash2,
  Mail,
  Phone,
  User,
  Copy
} from "lucide-react"
import { format, isSameDay, startOf<PERSON>onth, endOfMonth, isToday, addMonths, subMonths, parseISO, startOfWeek, endOfWeek, eachDayOfInterval, isSameMonth, addDays, startOfDay } from "date-fns"
import { useSession } from "next-auth/react"
import { GoogleCalendarEvent, CalendarStats } from "@/lib/calendar-types"

interface CalendarData {
  id: string
  summary: string
  description?: string
  primary?: boolean
  accessRole: string
  selected?: boolean
  backgroundColor?: string
  foregroundColor?: string
}

interface CalendarColors {
  calendar: Record<string, { background: string; foreground: string }>
  event: Record<string, { background: string; foreground: string }>
}

interface CalendarSettings {
  weekStart: number
  showWeekNumbers: boolean
  defaultEventLength: number
  workingHours: {
    start: string
    end: string
  }
  timeZone: string
}

interface CalendarViewProps {
  events: GoogleCalendarEvent[]
  calendars: CalendarData[]
  colors: CalendarColors
  settings: CalendarSettings
  stats: CalendarStats
  onEventCreate: (eventData: Partial<GoogleCalendarEvent>) => Promise<void>
  onEventUpdate: (eventId: string, eventData: Partial<GoogleCalendarEvent>) => Promise<void>
  onEventDelete: (eventId: string) => Promise<void>
  onCalendarToggle: (calendarId: string) => void
  currentDate: Date
  view: 'Month' | 'Week' | 'Day'
  userTimezone?: string
  loading?: boolean
  error?: string | null
  needsConnection?: boolean
  onRetry?: () => void
}

export function CalendarView({ 
  events: passedEvents = [],
  calendars = [],
  colors,
  settings,
  stats,
  onEventCreate,
  onEventUpdate,
  onEventDelete,
  onCalendarToggle,
  currentDate: passedCurrentDate,
  view,
  userTimezone,
  loading = false,
  error = null,
  needsConnection = false,
  onRetry
}: CalendarViewProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(passedCurrentDate || new Date())
  const [events, setEvents] = useState<GoogleCalendarEvent[]>(passedEvents)
  const [showDialog, setShowDialog] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedEvent, setSelectedEvent] = useState<GoogleCalendarEvent | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  // Debug function to track selectedEvent changes
  const handleSetSelectedEvent = (event: GoogleCalendarEvent | null) => {
    console.log('Setting selectedEvent:', event)
    setSelectedEvent(event)
  }

  // Handle edit event
  const handleEditEvent = () => {
    if (selectedEvent) {
      // Navigate to edit page with event data
      const editUrl = `/dashboard/calendar/new-event?edit=${selectedEvent.id}`
      window.location.href = editUrl
    }
  }

  // Handle delete event
  const handleDeleteEvent = async () => {
    if (selectedEvent) {
      try {
        await onEventDelete(selectedEvent.id)
        setSelectedEvent(null)
        setShowDeleteConfirm(false)
      } catch (error) {
        console.error('Failed to delete event:', error)
        // You could add a toast notification here
      }
    }
  }
  const [newEvent, setNewEvent] = useState<{
    summary: string
    description: string
    startDate: string
    endDate: string
    location: string
    isAllDay: boolean
    addMeet: boolean
    attendees: string
  }>({
    summary: "",
    description: "",
    startDate: new Date().toISOString().slice(0, 16),
    endDate: new Date(Date.now() + 60 * 60 * 1000).toISOString().slice(0, 16),
    location: "",
    isAllDay: false,
    addMeet: false,
    attendees: ""
  })

  // Update local state when props change
  useEffect(() => {
    if (passedEvents) {
      setEvents(passedEvents)
    }
  }, [passedEvents])

  useEffect(() => {
    if (passedCurrentDate) {
      setSelectedDate(passedCurrentDate)
    }
  }, [passedCurrentDate])

  const getEventsForDate = (date: Date) => {
    return events.filter(event => {
      const eventDate = event.start.dateTime ? parseISO(event.start.dateTime) : parseISO(event.start.date!)
      return isSameDay(eventDate, date)
    })
  }

  const getDaysInMonth = () => {
    const start = startOfWeek(startOfMonth(selectedDate))
    const end = endOfWeek(endOfMonth(selectedDate))
    return eachDayOfInterval({ start, end })
  }

  const getWeekDays = () => {
    const start = startOfWeek(selectedDate)
    const end = endOfWeek(selectedDate)
    return eachDayOfInterval({ start, end })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const getEventColor = (event: GoogleCalendarEvent) => {
    if (event.description?.includes('sourceType:ai') || event.summary?.includes('[AI]')) {
      return 'bg-blue-100 border-blue-300 text-blue-800'
    }
    // Default Google Calendar blue
    return 'bg-blue-500 text-white'
  }

  const navigateDate = (direction: 'prev' | 'next') => {
    if (view === 'Month') {
      setSelectedDate(direction === 'prev' ? subMonths(selectedDate, 1) : addMonths(selectedDate, 1))
    } else if (view === 'Week') {
      setSelectedDate(direction === 'prev' ? addDays(selectedDate, -7) : addDays(selectedDate, 7))
    } else {
      setSelectedDate(direction === 'prev' ? addDays(selectedDate, -1) : addDays(selectedDate, 1))
    }
  }

  const renderMonthView = () => {
    const days = getDaysInMonth()
    const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

    return (
      <div className="bg-white">
        {/* Week day headers */}
        <div className="grid grid-cols-7 border-b border-gray-200">
          {weekDays.map((day) => (
            <div key={day} className="p-3 text-center text-sm font-medium text-gray-500 bg-gray-50">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7">
          {days.map((day, index) => {
            const dayEvents = getEventsForDate(day)
            const isCurrentMonth = isSameMonth(day, selectedDate)
            const isCurrentDay = isToday(day)
            
            return (
              <div
                key={day.toISOString()}
                className={`min-h-32 border-r border-b border-gray-200 p-2 ${
                  !isCurrentMonth ? 'bg-gray-50 text-gray-400' : 'bg-white'
                } ${isCurrentDay ? 'bg-blue-50' : ''}`}
              >
                <div className={`text-sm font-medium mb-1 ${
                  isCurrentDay ? 'text-blue-600' : isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                }`}>
                  {day.getDate()}
                </div>
                
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event) => (
                    <div
                      key={event.id}
                      className={`text-xs p-1 rounded cursor-pointer hover:opacity-80 ${getEventColor(event)}`}
                      onClick={() => {
                        alert('Event clicked: ' + event.summary)
                        handleSetSelectedEvent(event)
                      }}
                    >
                      <div className="truncate">
                        {event.start.dateTime && formatTime(event.start.dateTime)} {event.summary}
                      </div>
                    </div>
                  ))}
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-gray-500 pl-1">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  const renderWeekView = () => {
    const days = getWeekDays()
    const hours = Array.from({ length: 24 }, (_, i) => i)

    return (
      <div className="bg-white">
        {/* Week header */}
        <div className="grid grid-cols-8 border-b border-gray-200">
          <div className="p-3 border-r border-gray-200"></div>
          {days.map((day) => (
            <div key={day.toISOString()} className="p-3 text-center">
              <div className="text-sm font-medium text-gray-500">
                {format(day, 'EEE')}
              </div>
              <div className={`text-lg font-semibold ${
                isToday(day) ? 'text-blue-600' : 'text-gray-900'
              }`}>
                {day.getDate()}
              </div>
            </div>
          ))}
        </div>

        {/* Time slots */}
        <div className="grid grid-cols-8 divide-x divide-gray-200">
          {/* Time column */}
          <div className="border-r border-gray-200">
            {hours.map((hour) => (
              <div key={hour} className="h-16 p-2 text-xs text-gray-500 border-b border-gray-100">
                {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
              </div>
            ))}
          </div>

          {/* Day columns */}
          {days.map((day) => (
            <div key={day.toISOString()} className="relative">
              {hours.map((hour) => (
                <div key={hour} className="h-16 border-b border-gray-100 relative">
                  {/* Events for this hour */}
                  {getEventsForDate(day)
                    .filter(event => {
                      if (!event.start.dateTime) return false
                      const eventHour = new Date(event.start.dateTime).getHours()
                      return eventHour === hour
                    })
                    .map((event) => (
                      <div
                        key={event.id}
                        className={`absolute left-1 right-1 top-1 p-1 rounded text-xs cursor-pointer hover:opacity-80 ${getEventColor(event)}`}
                        onClick={() => {
                          console.log('Event clicked in week view:', event)
                          handleSetSelectedEvent(event)
                        }}
                      >
                        <div className="truncate font-medium">{event.summary}</div>
                        {event.location && (
                          <div className="truncate opacity-75">{event.location}</div>
                        )}
                      </div>
                    ))}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    )
  }

  const renderDayView = () => {
    const hours = Array.from({ length: 24 }, (_, i) => i)
    const dayEvents = getEventsForDate(selectedDate)

    return (
      <div className="bg-white">
        {/* Day header */}
        <div className="p-4 border-b border-gray-200">
          <div className="text-center">
            <div className="text-sm font-medium text-gray-500">
              {format(selectedDate, 'EEEE')}
            </div>
            <div className={`text-2xl font-semibold ${
              isToday(selectedDate) ? 'text-blue-600' : 'text-gray-900'
            }`}>
              {selectedDate.getDate()}
            </div>
          </div>
        </div>

        {/* Time slots */}
        <div className="divide-y divide-gray-100">
          {hours.map((hour) => (
            <div key={hour} className="flex">
              <div className="w-20 p-2 text-xs text-gray-500 border-r border-gray-200">
                {hour === 0 ? '12 AM' : hour < 12 ? `${hour} AM` : hour === 12 ? '12 PM' : `${hour - 12} PM`}
              </div>
              <div className="flex-1 h-16 relative">
                {/* Events for this hour */}
                {dayEvents
                  .filter(event => {
                    if (!event.start.dateTime) return false
                    const eventHour = new Date(event.start.dateTime).getHours()
                    return eventHour === hour
                  })
                  .map((event) => (
                    <div
                      key={event.id}
                      className={`absolute left-2 right-2 top-1 p-2 rounded cursor-pointer hover:opacity-80 ${getEventColor(event)}`}
                      onClick={() => {
                        console.log('Event clicked in day view:', event)
                        handleSetSelectedEvent(event)
                      }}
                    >
                      <div className="font-medium">{event.summary}</div>
                      {event.location && (
                        <div className="text-sm opacity-75">{event.location}</div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">{error}</p>
          <Button onClick={onRetry} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Calendar Content */}
      <div className="flex-1 overflow-auto">
        {view === 'Month' && renderMonthView()}
        {view === 'Week' && renderWeekView()}
        {view === 'Day' && renderDayView()}
      </div>

      {/* Enhanced Event Detail Modal */}
      {selectedEvent && (
        <Dialog open={!!selectedEvent} onOpenChange={() => handleSetSelectedEvent(null)}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader className="pb-4 border-b">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <DialogTitle className="text-2xl font-bold text-gray-900 mb-2">
                    {selectedEvent.summary || 'Untitled Event'}
                  </DialogTitle>
                  {selectedEvent.status && (
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${
                        selectedEvent.status === 'confirmed' ? 'bg-green-500' :
                        selectedEvent.status === 'tentative' ? 'bg-yellow-500' : 'bg-gray-500'
                      }`} />
                      <span className="text-sm text-gray-600 capitalize">{selectedEvent.status}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <Button variant="outline" size="sm" onClick={handleEditEvent}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => setShowDeleteConfirm(true)}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            </DialogHeader>

            <div className="space-y-6 pt-6">
              {/* Date and Time */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Clock className="h-5 w-5 text-blue-600" />
                  Date & Time
                </h3>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  {selectedEvent.start.dateTime ? (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Start:</span>
                        <span className="text-sm text-gray-900 dark:text-gray-100">
                          {format(new Date(selectedEvent.start.dateTime), 'EEEE, MMMM d, yyyy • h:mm a')}
                        </span>
                      </div>
                      {selectedEvent.end.dateTime && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">End:</span>
                          <span className="text-sm text-gray-900 dark:text-gray-100">
                            {format(new Date(selectedEvent.end.dateTime), 'EEEE, MMMM d, yyyy • h:mm a')}
                          </span>
                        </div>
                      )}
                      <div className="flex items-center justify-between pt-2 border-t border-blue-200 dark:border-blue-800">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Duration:</span>
                        <span className="text-sm text-gray-900 dark:text-gray-100">
                          {selectedEvent.end.dateTime ?
                            `${Math.round((new Date(selectedEvent.end.dateTime).getTime() - new Date(selectedEvent.start.dateTime).getTime()) / (1000 * 60))} minutes` :
                            'All day'
                          }
                        </span>
                      </div>
                    </div>
                  ) : selectedEvent.start.date ? (
                    <div className="text-center">
                      <span className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {format(new Date(selectedEvent.start.date), 'EEEE, MMMM d, yyyy')}
                      </span>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">All day event</p>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">No date specified</span>
                  )}
                </div>
              </div>
              
              {/* Location */}
              {selectedEvent.location && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-red-600" />
                    Location
                  </h3>
                  <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {selectedEvent.location}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(selectedEvent.location || '')
                        }}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        Copy
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Description */}
              {selectedEvent.description && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <MoreHorizontal className="h-5 w-5 text-green-600" />
                    Description
                  </h3>
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {selectedEvent.description}
                    </p>
                  </div>
                </div>
              )}

              {/* Meeting Link */}
              {selectedEvent.conferenceData?.entryPoints && selectedEvent.conferenceData.entryPoints.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Video className="h-5 w-5 text-purple-600" />
                    Meeting Link
                  </h3>
                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    {selectedEvent.conferenceData.entryPoints.map((entryPoint, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {entryPoint.label || entryPoint.entryPointType}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              navigator.clipboard.writeText(entryPoint.uri)
                            }}
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            Copy
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => {
                              window.open(entryPoint.uri, '_blank')
                            }}
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            Join
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Attendees */}
              {selectedEvent.attendees && selectedEvent.attendees.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Users className="h-5 w-5 text-orange-600" />
                    Attendees ({selectedEvent.attendees.length})
                  </h3>
                  <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                    <div className="space-y-3">
                      {selectedEvent.attendees.map((attendee, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                              <User className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {attendee.displayName || attendee.email}
                              </p>
                              {attendee.displayName && (
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {attendee.email}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={
                              attendee.responseStatus === 'accepted' ? 'default' :
                              attendee.responseStatus === 'declined' ? 'destructive' :
                              attendee.responseStatus === 'tentative' ? 'secondary' : 'outline'
                            }>
                              {attendee.responseStatus || 'pending'}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                window.location.href = `mailto:${attendee.email}`
                              }}
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Reminders */}
              {selectedEvent.reminders && (selectedEvent.reminders.useDefault || (selectedEvent.reminders.overrides && selectedEvent.reminders.overrides.length > 0)) && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Bell className="h-5 w-5 text-yellow-600" />
                    Reminders
                  </h3>
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                    {selectedEvent.reminders.useDefault ? (
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        Using default reminders
                      </p>
                    ) : (
                      <div className="space-y-2">
                        {selectedEvent.reminders.overrides?.map((reminder, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border">
                            <span className="text-sm text-gray-700 dark:text-gray-300">
                              {reminder.method === 'popup' ? '🔔 Popup' : '📧 Email'}
                            </span>
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {reminder.minutes === 0 ? 'At event time' :
                               reminder.minutes < 60 ? `${reminder.minutes} minutes before` :
                               reminder.minutes < 1440 ? `${Math.floor(reminder.minutes / 60)} hours before` :
                               `${Math.floor(reminder.minutes / 1440)} days before`}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Event Details */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Settings className="h-5 w-5 text-gray-600" />
                  Event Details
                </h3>
                <div className="bg-gray-50 dark:bg-gray-900/20 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {selectedEvent.creator && (
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Created by:</span>
                        <p className="text-gray-900 dark:text-gray-100">
                          {selectedEvent.creator.displayName || selectedEvent.creator.email}
                        </p>
                      </div>
                    )}
                    {selectedEvent.organizer && (
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Organizer:</span>
                        <p className="text-gray-900 dark:text-gray-100">
                          {selectedEvent.organizer.displayName || selectedEvent.organizer.email}
                        </p>
                      </div>
                    )}
                    {selectedEvent.created && (
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Created:</span>
                        <p className="text-gray-900 dark:text-gray-100">
                          {format(new Date(selectedEvent.created), 'MMM d, yyyy • h:mm a')}
                        </p>
                      </div>
                    )}
                    {selectedEvent.updated && (
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Last updated:</span>
                        <p className="text-gray-900 dark:text-gray-100">
                          {format(new Date(selectedEvent.updated), 'MMM d, yyyy • h:mm a')}
                        </p>
                      </div>
                    )}
                    {selectedEvent.visibility && (
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Visibility:</span>
                        <p className="text-gray-900 dark:text-gray-100 capitalize">
                          {selectedEvent.visibility}
                        </p>
                      </div>
                    )}
                    {selectedEvent.transparency && (
                      <div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">Show as:</span>
                        <p className="text-gray-900 dark:text-gray-100 capitalize">
                          {selectedEvent.transparency === 'transparent' ? 'Free' : 'Busy'}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 pt-6 border-t">
                <Button variant="default" size="sm" asChild>
                  <a href={selectedEvent.htmlLink} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open in Google Calendar
                  </a>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const eventText = `${selectedEvent.summary}\n${selectedEvent.start.dateTime ? format(new Date(selectedEvent.start.dateTime), 'MMM d, yyyy • h:mm a') : selectedEvent.start.date}\n${selectedEvent.location || ''}\n${selectedEvent.description || ''}`
                    navigator.clipboard.writeText(eventText)
                  }}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Details
                </Button>
                {selectedEvent.conferenceData?.entryPoints?.[0] && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      window.open(selectedEvent.conferenceData?.entryPoints?.[0]?.uri, '_blank')
                    }}
                  >
                    <Video className="h-4 w-4 mr-2" />
                    Join Meeting
                  </Button>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 className="h-5 w-5" />
              Delete Event
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-gray-700">
              Are you sure you want to delete "{selectedEvent?.summary || 'this event'}"?
              This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteEvent}
                className="bg-red-600 hover:bg-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete Event
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}