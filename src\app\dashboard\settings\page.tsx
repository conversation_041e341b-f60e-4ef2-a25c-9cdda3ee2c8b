"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  Settings, 
  Mail, 
  Shield, 
  Zap,
  CheckCircle,
  AlertTriangle,
  ExternalLink
} from "lucide-react"
import { useSession } from "next-auth/react"
import Link from "next/link"

export default function SettingsPage() {
  const { data: session } = useSession()
  const [gmailStatus, setGmailStatus] = useState<{connected: boolean, loading: boolean}>({
    connected: false,
    loading: true
  })

  // Basic preferences that could be stored locally or in future user preferences
  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    trackingEnabled: true,
    confirmBeforeSending: true
  })

  useEffect(() => {
    checkGmailStatus()
  }, [])

  const checkGmailStatus = async () => {
    try {
      const response = await fetch('/api/gmail/status')
      if (response.ok) {
        const data = await response.json()
        setGmailStatus({
          connected: data.connected,
          loading: false
        })
      }
    } catch (error) {
      console.error('Error checking Gmail status:', error)
    } finally {
      setGmailStatus(prev => ({ ...prev, loading: false }))
    }
  }

  const handleConnectGmail = async () => {
    window.location.href = '/api/gmail/connect'
  }

  const togglePreference = (key: keyof typeof preferences) => {
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
    // In a real app, this would save to user preferences API
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
          <p className="text-muted-foreground">
            Manage your account settings and email preferences.
          </p>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Gmail Integration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Gmail Integration
            </CardTitle>
            <CardDescription>
              Connect your Gmail account to send emails through the Gmail API.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium">Gmail Connection Status</p>
                <p className="text-sm text-muted-foreground">
                  {gmailStatus.loading 
                    ? "Checking connection..." 
                    : gmailStatus.connected 
                      ? "Your Gmail account is connected and ready to send emails"
                      : "Connect your Gmail account to start sending emails"
                  }
                </p>
              </div>
              <div className="flex items-center gap-2">
                {gmailStatus.loading ? (
                  <Badge variant="secondary">Checking...</Badge>
                ) : (
                  <Badge variant={gmailStatus.connected ? "default" : "secondary"}>
                    {gmailStatus.connected ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Connected
                      </>
                    ) : (
                      <>
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Not Connected
                      </>
                    )}
                  </Badge>
                )}
              </div>
            </div>

            {!gmailStatus.connected && !gmailStatus.loading && (
              <Alert>
                <Zap className="h-4 w-4" />
                <AlertTitle>Gmail API Not Connected</AlertTitle>
                <AlertDescription className="mt-2">
                  To send emails, you need to connect your Gmail account. This allows the application to send emails on your behalf using Google&apos;s secure OAuth 2.0 authentication.
                  <div className="mt-3">
                    <Button onClick={handleConnectGmail} className="w-full sm:w-auto">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Connect Gmail Account
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {gmailStatus.connected && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-green-800">Gmail Connected Successfully</h4>
                    <p className="text-sm text-green-700 mt-1">
                      You can now send emails through your Gmail account. All emails will be sent from your connected Gmail address.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Email Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Email Preferences
            </CardTitle>
            <CardDescription>
              Configure your email sending and tracking preferences.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <p className="text-sm font-medium">Email Tracking</p>
                <p className="text-sm text-muted-foreground">
                  Track email delivery status and history
                </p>
              </div>
              <Switch 
                checked={preferences.trackingEnabled}
                onCheckedChange={() => togglePreference('trackingEnabled')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <p className="text-sm font-medium">Confirm Before Sending</p>
                <p className="text-sm text-muted-foreground">
                  Show confirmation dialog before sending emails
                </p>
              </div>
              <Switch 
                checked={preferences.confirmBeforeSending}
                onCheckedChange={() => togglePreference('confirmBeforeSending')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <p className="text-sm font-medium">Email Notifications</p>
                <p className="text-sm text-muted-foreground">
                  Receive notifications about email delivery status
                </p>
              </div>
              <Switch 
                checked={preferences.emailNotifications}
                onCheckedChange={() => togglePreference('emailNotifications')}
              />
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Account Information
            </CardTitle>
            <CardDescription>
              Your account details and authentication status.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Account Email</p>
                <p className="text-sm">{session?.user?.email || "Not available"}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Authentication Method</p>
                <p className="text-sm">Google OAuth 2.0</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="text-sm font-medium">Quick Links</h4>
              <div className="flex flex-wrap gap-2">
                <Link href="/dashboard/profile">
                  <Button variant="outline" size="sm">
                    View Profile
                  </Button>
                </Link>
                <Link href="/dashboard/emails">
                  <Button variant="outline" size="sm">
                    Email History
                  </Button>
                </Link>
                <Link href="/dashboard/contacts">
                  <Button variant="outline" size="sm">
                    Manage Contacts
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Coming Soon */}
        <Card className="opacity-60">
          <CardHeader>
            <CardTitle>Advanced Settings</CardTitle>
            <CardDescription>
              Coming soon: Advanced email configuration, team management, and API settings.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="text-center p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <Shield className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm font-medium">Security Settings</p>
                <p className="text-xs text-muted-foreground">Two-factor authentication and security preferences</p>
              </div>
              <div className="text-center p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <Mail className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm font-medium">Email Configuration</p>
                <p className="text-xs text-muted-foreground">Custom SMTP settings and email signatures</p>
              </div>
              <div className="text-center p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <Settings className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm font-medium">API Settings</p>
                <p className="text-xs text-muted-foreground">API keys and webhook configurations</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 