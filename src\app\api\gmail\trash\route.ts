import { NextRequest } from "next/server"
import { getTrashEmails, withGmailAuth, parseEmailListParams } from "@/lib/gmail"

export async function GET(request: NextRequest) {
  return withGmailAuth(request, async ({ user, url }) => {
    const params = parseEmailListParams(url)
    
    // Fetch trash emails from Gmail
    const result = await getTrashEmails(
      user.id, 
      params.limit, 
      params.pageToken, 
      params.page, 
      params.dateFrom, 
      params.dateTo
    )

    return result
  })
} 