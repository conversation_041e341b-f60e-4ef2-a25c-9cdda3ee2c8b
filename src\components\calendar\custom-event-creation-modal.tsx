'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Video, 
  Settings,
  Plus,
  X,
  Info
} from 'lucide-react'
import { toast } from 'sonner'

interface EventCreationModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (eventData: any) => Promise<void>
  selectedDate?: Date
  timezone?: string
}

export function EventCreationModal({
  isOpen,
  onClose,
  onSave,
  selectedDate,
  timezone = 'UTC'
}: EventCreationModalProps) {
  // Basic event fields
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [location, setLocation] = useState('')
  const [startDate, setStartDate] = useState('')
  const [startTime, setStartTime] = useState('09:00')
  const [endDate, setEndDate] = useState('')
  const [endTime, setEndTime] = useState('10:00')
  const [isAllDay, setIsAllDay] = useState(false)
  const [attendees, setAttendees] = useState<string[]>([''])

  // Meet integration fields
  const [addGoogleMeet, setAddGoogleMeet] = useState(false)
  const [createMeetSpace, setCreateMeetSpace] = useState(false)
  const [meetAccessType, setMeetAccessType] = useState<'OPEN' | 'TRUSTED' | 'RESTRICTED'>('OPEN')
  const [meetEntryPointAccess, setMeetEntryPointAccess] = useState<'ALL' | 'CREATOR_APP_ONLY'>('ALL')

  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (selectedDate) {
      const dateStr = selectedDate.toISOString().split('T')[0]
      setStartDate(dateStr)
      setEndDate(dateStr)
    }
  }, [selectedDate])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title.trim()) {
      toast.error('Event title is required')
      return
    }

    setLoading(true)
    try {
      const eventData: any = {
        summary: title,
        description,
        location,
        start: isAllDay ? {
          date: startDate,
          timeZone: timezone
        } : {
          dateTime: new Date(`${startDate}T${startTime}`).toISOString(),
          timeZone: timezone
        },
        end: isAllDay ? {
          date: endDate || startDate,
          timeZone: timezone
        } : {
          dateTime: new Date(`${endDate}T${endTime}`).toISOString(),
          timeZone: timezone
        },
        attendees: attendees.filter(email => email && email.trim() && email.includes('@')).map(email => ({ email: email.trim() })),
        timeZone: timezone
      }

      // Add Meet integration options
      if (addGoogleMeet) {
        eventData.addMeet = true
        
        if (createMeetSpace) {
          eventData.createMeetSpace = true
          eventData.meetAccessType = meetAccessType
          eventData.meetEntryPointAccess = meetEntryPointAccess
        }
      }

      // Use Meet integration API endpoint if Meet space creation is requested
      const apiEndpoint = createMeetSpace ? '/api/calendar/meet-integration' : '/api/calendar'
      
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create event')
      }

      const result = await response.json()
      
      // Show success message with Meet space info if created
      if (result.meetSpace) {
        toast.success(`Event created with Meet space! Code: ${result.meetSpace.meetingCode}`)
      } else {
        toast.success('Event created successfully!')
      }

      await onSave(eventData)
      onClose()
      resetForm()
    } catch (error) {
      console.error('Error creating event:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create event')
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setTitle('')
    setDescription('')
    setLocation('')
    setStartDate('')
    setStartTime('09:00')
    setEndDate('')
    setEndTime('10:00')
    setIsAllDay(false)
    setAttendees([''])
    setAddGoogleMeet(false)
    setCreateMeetSpace(false)
    setMeetAccessType('OPEN')
    setMeetEntryPointAccess('ALL')
  }

  const addAttendee = () => {
    setAttendees([...attendees, ''])
  }

  const removeAttendee = (index: number) => {
    setAttendees(attendees.filter((_, i) => i !== index))
  }

  const updateAttendee = (index: number, value: string) => {
    const newAttendees = [...attendees]
    newAttendees[index] = value
    setAttendees(newAttendees)
  }

  const timeOptions = Array.from({ length: 48 }, (_, i) => {
    const hour = Math.floor(i / 2)
    const minute = i % 2 === 0 ? '00' : '30'
    const time = `${hour.toString().padStart(2, '0')}:${minute}`
    const display = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
    return { value: time, label: display }
  })

  const getMeetAccessDescription = (type: string) => {
    switch (type) {
      case 'OPEN':
        return 'Anyone with the link can join'
      case 'TRUSTED':
        return 'Only people in your organization can join'
      case 'RESTRICTED':
        return 'Only invited participants can join'
      default:
        return ''
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Create New Event
          </DialogTitle>
          <DialogDescription>
            Create a calendar event with optional Google Meet integration
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="attendees">Attendees</TabsTrigger>
              <TabsTrigger value="meet">Meet Integration</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title">Event Title *</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter event title"
                  required
                />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter event description"
                  rows={3}
                />
              </div>

              {/* Location */}
              <div className="space-y-2">
                <Label htmlFor="location" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </Label>
                <Input
                  id="location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="Enter event location"
                />
              </div>

              {/* All Day Toggle */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="all-day"
                  checked={isAllDay}
                  onCheckedChange={setIsAllDay}
                />
                <Label htmlFor="all-day">All day event</Label>
              </div>

              {/* Date and Time */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Start Date</Label>
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label>End Date</Label>
                  <Input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    required
                  />
                </div>
              </div>

              {!isAllDay && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Start Time</Label>
                    <Select value={startTime} onValueChange={setStartTime}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {timeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>End Time</Label>
                    <Select value={endTime} onValueChange={setEndTime}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {timeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="attendees" className="space-y-4">
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Attendees
                </Label>
                <div className="space-y-2">
                  {attendees.map((attendee, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        type="email"
                        value={attendee}
                        onChange={(e) => updateAttendee(index, e.target.value)}
                        placeholder="Enter email address"
                        className="flex-1"
                      />
                      {attendees.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => removeAttendee(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addAttendee}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Attendee
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="meet" className="space-y-4">
              {/* Google Meet Toggle */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="add-meet"
                  checked={addGoogleMeet}
                  onCheckedChange={setAddGoogleMeet}
                />
                <Label htmlFor="add-meet" className="flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  Add Google Meet
                </Label>
              </div>

              {addGoogleMeet && (
                <div className="space-y-4 p-4 border rounded-lg">
                  {/* Custom Meet Space Creation */}
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="create-meet-space"
                      checked={createMeetSpace}
                      onCheckedChange={setCreateMeetSpace}
                    />
                    <Label htmlFor="create-meet-space" className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Create Custom Meet Space
                    </Label>
                  </div>

                  {createMeetSpace && (
                    <div className="space-y-4 p-4 bg-muted rounded-lg">
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          Custom Meet spaces provide better control over access and features compared to regular Meet links.
                        </AlertDescription>
                      </Alert>

                      {/* Access Type */}
                      <div className="space-y-2">
                        <Label>Access Type</Label>
                        <Select value={meetAccessType} onValueChange={(value: any) => setMeetAccessType(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="OPEN">
                              <div>
                                <div className="font-medium">Open</div>
                                <div className="text-sm text-muted-foreground">Anyone with the link</div>
                              </div>
                            </SelectItem>
                            <SelectItem value="TRUSTED">
                              <div>
                                <div className="font-medium">Trusted</div>
                                <div className="text-sm text-muted-foreground">Organization only</div>
                              </div>
                            </SelectItem>
                            <SelectItem value="RESTRICTED">
                              <div>
                                <div className="font-medium">Restricted</div>
                                <div className="text-sm text-muted-foreground">Invited participants only</div>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-sm text-muted-foreground">
                          {getMeetAccessDescription(meetAccessType)}
                        </p>
                      </div>

                      {/* Entry Point Access */}
                      <div className="space-y-2">
                        <Label>Entry Point Access</Label>
                        <Select value={meetEntryPointAccess} onValueChange={(value: any) => setMeetEntryPointAccess(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ALL">
                              <div>
                                <div className="font-medium">All Entry Points</div>
                                <div className="text-sm text-muted-foreground">Web, mobile, phone, etc.</div>
                              </div>
                            </SelectItem>
                            <SelectItem value="CREATOR_APP_ONLY">
                              <div>
                                <div className="font-medium">Creator App Only</div>
                                <div className="text-sm text-muted-foreground">Through creator application only</div>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex gap-2">
                        <Badge variant="secondary">{meetAccessType}</Badge>
                        <Badge variant="outline">{meetEntryPointAccess}</Badge>
                      </div>
                    </div>
                  )}

                  {!createMeetSpace && (
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription>
                        A standard Google Meet link will be created for this event.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Calendar className="h-4 w-4 mr-2" />
                  Create Event
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
