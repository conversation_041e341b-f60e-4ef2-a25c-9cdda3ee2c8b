import { NextRequest } from "next/server"
import { getInboxEmails, withGmail<PERSON>uth } from "@/lib/gmail"
import { validateSearchParams } from "@/lib/validation/utils"
import { emailListParamsSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function GET(request: NextRequest) {
  return withGmailAuth(request, async ({ user, url }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-inbox-fetch', { userId: user.id })

    try {
      // Validate search parameters using Zod
      const validation = validateSearchParams(url.searchParams, emailListParamsSchema)
      if (!validation.success) {
        throw new Error(`Invalid parameters: ${validation.error}`)
      }

      const params = validation.data

      // Fetch emails from Gmail with validated parameters
      const result = await getInboxEmails(
        user.id,
        params.limit,
        params.pageToken,
        params.page,
        params.labelId,
        params.dateFrom ? new Date(params.dateFrom) : undefined,
        params.dateTo ? new Date(params.dateTo) : undefined
      )

      // End performance monitoring
      const duration = endPerformanceMetric('gmail-inbox-fetch')
      if (duration && duration > 3000) {
        console.warn(`Slow inbox fetch: ${duration.toFixed(2)}ms for user ${user.id}`)
      }

      return result
    } catch (error) {
      // End performance monitoring on error
      endPerformanceMetric('gmail-inbox-fetch')
      throw error
    }
  })
}