import { NextRequest } from "next/server"
import { getInboxEmails, withGmail<PERSON>uth } from "@/lib/gmail"
import { validateSearchParams } from "@/lib/validation/utils"
import { emailListParamsSchema } from "@/lib/validation/schemas"

export async function GET(request: NextRequest) {
  return withGmail<PERSON>uth(request, async ({ user, url }) => {
    // Validate search parameters using Zod
    const validation = validateSearchParams(url.searchParams, emailListParamsSchema)
    if (!validation.success) {
      throw new Error(`Invalid parameters: ${validation.error}`)
    }

    const params = validation.data

    // Fetch emails from Gmail with validated parameters
    const result = await getInboxEmails(
      user.id,
      params.limit,
      params.pageToken,
      params.page,
      params.labelId,
      params.dateFrom,
      params.dateTo
    )

    return result
  })
}