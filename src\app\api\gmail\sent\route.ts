import { NextRequest } from "next/server"
import { getSentEmails, withGmail<PERSON>uth } from "@/lib/gmail"
import { validateSearchParams } from "@/lib/validation/utils"
import { emailListParamsSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function GET(request: NextRequest) {
  return withGmailAuth(request, async ({ user, url }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-sent-fetch', { userId: user.id })

    // Validate search parameters using Zod
    const validation = validateSearchParams(url.searchParams, emailListParamsSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-sent-fetch')
      throw new Error(`Invalid parameters: ${validation.error}`)
    }

    const params = validation.data

    // Fetch sent emails from Gmail with validated parameters
    const result = await getSentEmails(
      user.id,
      params.limit,
      params.pageToken,
      params.page,
      params.dateFrom,
      params.dateTo
    )

    // End performance monitoring
    const duration = endPerformanceMetric('gmail-sent-fetch')
    if (duration && duration > 3000) {
      console.warn(`Slow sent emails fetch: ${duration.toFixed(2)}ms for user ${user.id}`)
    }

    return result
  })
}