import { NextRequest } from "next/server"
import { getSentEmails, withGmailAuth, parseEmailListParams } from "@/lib/gmail"

export async function GET(request: NextRequest) {
  return withGmailAuth(request, async ({ user, url }) => {
    const params = parseEmailListParams(url)
    
    // Fetch emails from Gmail
    const result = await getSentEmails(
      user.id, 
      params.limit, 
      params.pageToken, 
      params.page, 
      params.dateFrom, 
      params.dateTo
    )

    return result
  })
} 