import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentCalendarService } from '@/lib/ai-agent-calendar'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      context,
      attendees,
      preferredDate,
      preferredTime,
      duration = 60,
      timezone = 'UTC',
      meetingType = 'virtual',
      location,
      addMeetLink = true
    } = await request.json()

    if (!context) {
      return NextResponse.json({ 
        error: 'Meeting context is required' 
      }, { status: 400 })
    }

    const result = await aiAgentCalendarService.parseAndScheduleMeeting(session.user.id, {
      context,
      attendees,
      preferredDate,
      preferredTime,
      duration,
      timezone,
      meetingType,
      location,
      addMeetLink
    })

    return NextResponse.json({
      result,
      success: true
    })

  } catch (error) {
    console.error('Meeting scheduling error:', error)
    return NextResponse.json(
      { error: 'Failed to schedule meeting' },
      { status: 500 }
    )
  }
}
