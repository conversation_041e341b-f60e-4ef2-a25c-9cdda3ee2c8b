import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetUser, 
  handleMeetApiError, 
  formatMeetApiResponse,
  validatePaginationParams,
  listRecordings,
  getRecordingDownloadInfo
} from "@/lib/meet"

interface RouteParams {
  params: {
    conferenceId: string
  }
}

/**
 * GET /api/meet/conferences/[conferenceId]/recordings - Get conference recordings
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { conferenceId } = params
    
    // Decode the conference ID in case it's URL encoded
    const decodedConferenceId = decodeURIComponent(conferenceId)
    const searchParams = url.searchParams

    const { pageSize, pageToken } = validatePaginationParams(searchParams)
    const includeDownloadInfo = searchParams.get('includeDownloadInfo') === 'true'

    // Get recordings
    const recordings = await listRecordings(user.id, decodedConferenceId, {
      pageSize,
      pageToken
    })

    let response: any = recordings

    // Include download information if requested
    if (includeDownloadInfo && recordings.items.length > 0) {
      const recordingsWithDownloadInfo = await Promise.all(
        recordings.items.map(async (recording) => {
          try {
            const downloadInfo = await getRecordingDownloadInfo(user.id, recording.name)
            return {
              ...recording,
              downloadInfo
            }
          } catch (error) {
            console.error(`Error getting download info for recording ${recording.name}:`, error)
            return recording
          }
        })
      )

      response = {
        ...recordings,
        items: recordingsWithDownloadInfo
      }
    }

    return formatMeetApiResponse(
      response,
      "Conference recordings retrieved successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
