'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  Video, 
  Users, 
  MessageSquare,
  Share,
  Loader2,
  ExternalLink
} from 'lucide-react'

// Google Meet Add-ons SDK types
declare global {
  interface Window {
    meet?: {
      addon?: {
        createAddonSession: (config: { cloudProjectNumber: string }) => Promise<any>
      }
    }
  }
}

interface MeetAddonSidePanelProps {
  cloudProjectNumber: string
  mainStageUrl: string
}

export default function MeetAddonSidePanel({ 
  cloudProjectNumber, 
  mainStageUrl 
}: MeetAddonSidePanelProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [sidePanelClient, setSidePanelClient] = useState<any>(null)
  const [isActivityStarted, setIsActivityStarted] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const { toast } = useToast()

  useEffect(() => {
    initializeAddon()
  }, [])

  const initializeAddon = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Check if we're running in development mode
      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost'

      // Check for meet_sdk parameter (only present when running inside Google Meet)
      const urlParams = new URLSearchParams(window.location.search)
      const hasMeetSdkParam = urlParams.has('meet_sdk')

      if (isDevelopment && !hasMeetSdkParam) {
        // Running locally - show development mode
        setError('DEVELOPMENT_MODE')
        return
      }

      // Wait for Google Meet Add-ons SDK to be available
      if (!window.meet?.addon) {
        throw new Error('Google Meet Add-ons SDK not available. This add-on must run inside Google Meet.')
      }

      // Create addon session
      const session = await window.meet.addon.createAddonSession({
        cloudProjectNumber: cloudProjectNumber,
      })

      // Create side panel client
      const client = await session.createSidePanelClient()
      setSidePanelClient(client)

      toast({
        title: "Add-on Ready",
        description: "Successfully connected to Google Meet",
      })
    } catch (error) {
      console.error('Failed to initialize Meet add-on:', error)
      setError(error instanceof Error ? error.message : 'Failed to initialize add-on')
    } finally {
      setIsLoading(false)
    }
  }

  const startActivity = async () => {
    if (!sidePanelClient) {
      toast({
        title: "Error",
        description: "Add-on not initialized",
        variant: "destructive"
      })
      return
    }

    try {
      await sidePanelClient.startActivity({
        mainStageUrl: mainStageUrl
      })
      
      setIsActivityStarted(true)
      toast({
        title: "Activity Started",
        description: "Launched activity in main stage for all participants",
      })
    } catch (error) {
      console.error('Failed to start activity:', error)
      toast({
        title: "Error",
        description: "Failed to start activity",
        variant: "destructive"
      })
    }
  }

  const openMeetingInNewTab = () => {
    // This would open the current meeting in a new tab
    // The meeting URL would need to be passed from the parent component
    window.open('https://meet.google.com', '_blank')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[300px]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-sm text-muted-foreground">Initializing add-on...</p>
        </div>
      </div>
    )
  }

  if (error === 'DEVELOPMENT_MODE') {
    return (
      <div className="p-4 space-y-4">
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800 text-sm flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Development Mode
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-blue-700">
              You're viewing the add-on side panel in development mode.
            </p>
            <div className="bg-blue-100 p-3 rounded text-xs text-blue-800">
              <strong>Note:</strong> The "Missing required Meet SDK URL parameter" error is expected when running locally.
              This parameter is only provided when the add-on runs inside Google Meet.
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-blue-800">To test the full add-on:</h4>
              <ol className="text-xs text-blue-700 space-y-1 ml-4">
                <li>1. Deploy this app to an HTTPS domain</li>
                <li>2. Configure the add-on in Google Cloud Console</li>
                <li>3. Create a Google Meet and install your add-on</li>
                <li>4. Access the add-on from within the meeting</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 space-y-4">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 text-sm">Add-on Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-red-700">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={initializeAddon}
              className="mt-2"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-4 h-full">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <Video className="h-5 w-5 text-blue-600" />
          <h2 className="font-semibold text-lg">Meeting Collaboration</h2>
        </div>
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
          Connected
        </Badge>
      </div>

      {/* Main Action */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <Share className="h-4 w-4" />
            Collaborative Activity
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <p className="text-sm text-muted-foreground">
            Launch a shared activity that all meeting participants can see and interact with.
          </p>
          
          {!isActivityStarted ? (
            <Button 
              onClick={startActivity}
              className="w-full"
              disabled={!sidePanelClient}
            >
              <Share className="h-4 w-4 mr-2" />
              Launch Activity in Main Stage
            </Button>
          ) : (
            <div className="text-center space-y-2">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                Activity Running
              </Badge>
              <p className="text-xs text-muted-foreground">
                Activity is now visible to all participants in the main stage
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Features</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Users className="h-4 w-4 text-blue-600" />
              <span>Real-time collaboration</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <MessageSquare className="h-4 w-4 text-green-600" />
              <span>Shared messaging</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Share className="h-4 w-4 text-purple-600" />
              <span>Screen sharing integration</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-4">
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              📋 How it works:
            </h4>
            <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
              <li>• This add-on runs inside Google Meet</li>
              <li>• All participants can see shared activities</li>
              <li>• No need to leave the meeting interface</li>
              <li>• Real-time synchronization across all users</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center pt-4 border-t">
        <p className="text-xs text-muted-foreground mb-2">
          This is the add-on side panel. Only you can see this.
        </p>
        <Button 
          variant="outline" 
          size="sm"
          onClick={openMeetingInNewTab}
        >
          <ExternalLink className="h-3 w-3 mr-1" />
          Open Meeting
        </Button>
      </div>
    </div>
  )
}
