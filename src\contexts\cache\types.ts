// Centralized Cache System Types
// Unified types for all application data caching

// ============================================================================
// BASE CACHE TYPES
// ============================================================================

export interface BaseCacheEntry<T = any> {
  data: T
  timestamp: number
  expiresAt?: number
  version?: number
  metadata?: Record<string, any>
}

export interface CacheConfig {
  defaultTTL: number // Time to live in milliseconds
  maxSize?: number // Maximum number of entries
  enablePersistence?: boolean // Whether to persist to localStorage/IndexedDB
  enableCompression?: boolean // Whether to compress large entries
}

export interface CacheStats {
  totalEntries: number
  memoryUsage: number
  hitRate: number
  missRate: number
  lastCleanup: number
}

// ============================================================================
// EMAIL CACHE TYPES (Enhanced from existing)
// ============================================================================

export interface CachedEmail {
  id: string
  threadId: string
  from: string
  to?: string[]
  cc?: string[]
  bcc?: string[]
  subject: string
  date: Date
  snippet: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  labels?: Array<{
    id: string
    name: string
    type: string
  }>
  body?: string
  attachments?: Array<{
    filename: string
    mimeType: string
    size: number
    attachmentId: string
  }>
  importance?: 'high' | 'normal' | 'low'
  category?: string
}

export interface CachedEmailList {
  emails: CachedEmail[]
  totalCount: number
  timestamp: number
  currentPage: number
  totalPages: number
  filters?: Record<string, any>
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface EmailCacheData {
  // Dynamic cache keys for different email types and time ranges
  [key: string]: CachedEmailList | undefined
  // Specific email details cache
  emailDetails: {
    [emailId: string]: BaseCacheEntry<CachedEmail>
  }
  // Category-based email cache
  categories: {
    [category: string]: CachedEmailList
  }
  // Thread-based email cache
  threads: {
    [threadId: string]: BaseCacheEntry<CachedEmail[]>
  }
}

// ============================================================================
// CALENDAR CACHE TYPES
// ============================================================================

export interface CachedCalendarEvent {
  id: string
  summary: string
  description?: string
  start: {
    dateTime?: string
    date?: string
    timeZone?: string
  }
  end: {
    dateTime?: string
    date?: string
    timeZone?: string
  }
  attendees?: Array<{
    email: string
    displayName?: string
    responseStatus?: 'needsAction' | 'declined' | 'tentative' | 'accepted'
    organizer?: boolean
  }>
  location?: string
  conferenceData?: {
    conferenceId: string
    conferenceSolution: {
      name: string
      iconUri?: string
    }
    entryPoints: Array<{
      entryPointType: string
      uri: string
      label?: string
    }>
  }
  recurrence?: string[]
  reminders?: {
    useDefault: boolean
    overrides?: Array<{
      method: 'email' | 'popup'
      minutes: number
    }>
  }
  status?: 'confirmed' | 'tentative' | 'cancelled'
  visibility?: 'default' | 'public' | 'private'
  created?: string
  updated?: string
  creator?: {
    email: string
    displayName?: string
  }
  organizer?: {
    email: string
    displayName?: string
  }
}

export interface CachedCalendarList {
  events: CachedCalendarEvent[]
  totalCount: number
  timestamp: number
  timeMin?: string
  timeMax?: string
  calendarId?: string
  filters?: Record<string, any>
}

export interface CalendarCacheData {
  // Time-based event cache (e.g., 'today', 'week', 'month')
  [key: string]: CachedCalendarList | undefined
  // Specific event details
  eventDetails: {
    [eventId: string]: BaseCacheEntry<CachedCalendarEvent>
  }
  // Calendar-specific caches
  calendars: {
    [calendarId: string]: CachedCalendarList
  }
  // Free/busy information
  freeBusy: {
    [key: string]: BaseCacheEntry<{
      timeMin: string
      timeMax: string
      calendars: {
        [calendarId: string]: {
          busy: Array<{
            start: string
            end: string
          }>
        }
      }
    }>
  }
}

// ============================================================================
// MEET CACHE TYPES
// ============================================================================

export interface CachedMeetingSpace {
  name: string
  meetingUri: string
  meetingCode: string
  config?: {
    accessType: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
    entryPointAccess: 'ALL' | 'CREATOR_APP_ONLY'
  }
  activeConference?: {
    conferenceRecord: string
  }
}

export interface CachedConferenceRecord {
  name: string
  startTime: string
  endTime: string
  expireTime: string
  space: string
}

export interface CachedParticipant {
  signedinUser?: {
    user: string
    displayName: string
  }
  anonymousUser?: {
    displayName: string
  }
  phoneUser?: {
    displayName: string
  }
  earliestStartTime: string
  latestEndTime: string
}

export interface MeetCacheData {
  // Meeting spaces
  spaces: {
    [spaceId: string]: BaseCacheEntry<CachedMeetingSpace>
  }
  // Conference records
  conferences: {
    [conferenceId: string]: BaseCacheEntry<CachedConferenceRecord>
  }
  // Participants
  participants: {
    [conferenceId: string]: BaseCacheEntry<CachedParticipant[]>
  }
  // Meeting analytics and insights
  analytics: {
    [key: string]: BaseCacheEntry<any>
  }
}

// ============================================================================
// AI AGENT CACHE TYPES
// ============================================================================

export interface CachedConversation {
  id: string
  title: string
  userId: string
  createdAt: Date
  updatedAt: Date
  messageCount: number
  lastMessage?: string
  isActive: boolean
}

export interface CachedMessage {
  id: string
  conversationId: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  actions?: Array<{
    type: string
    data: any
    status: 'pending' | 'completed' | 'failed'
  }>
}

export interface CachedUserBehavior {
  userId: string
  emailFrequency: any
  preferredResponseStyle: any
  aiInteractionStyle: any
  commonEmailTimes?: any
  lastUpdated: Date
}

export interface CachedKnowledgeEntry {
  id: string
  userId: string
  type: string
  category: string
  key: string
  content: string
  confidence: number
  createdAt: Date
  updatedAt: Date
}

export interface AICacheData {
  // Conversations
  conversations: {
    [conversationId: string]: BaseCacheEntry<CachedConversation>
  }
  // Messages
  messages: {
    [conversationId: string]: BaseCacheEntry<CachedMessage[]>
  }
  // User behavior patterns
  userBehavior: {
    [userId: string]: BaseCacheEntry<CachedUserBehavior>
  }
  // Knowledge entries
  knowledge: {
    [userId: string]: BaseCacheEntry<CachedKnowledgeEntry[]>
  }
  // AI responses and insights
  insights: {
    [key: string]: BaseCacheEntry<any>
  }
}

// ============================================================================
// UNIFIED CACHE STRUCTURE
// ============================================================================

export interface UnifiedCacheData {
  email: EmailCacheData
  calendar: CalendarCacheData
  meet: MeetCacheData
  ai: AICacheData
  // Generic cache for other data types
  generic: {
    [key: string]: BaseCacheEntry<any>
  }
}

// ============================================================================
// CACHE OPERATION TYPES
// ============================================================================

export type CacheKey = string
export type CacheNamespace = keyof UnifiedCacheData

export interface CacheOperation<T = any> {
  namespace: CacheNamespace
  key: CacheKey
  data?: T
  ttl?: number
  metadata?: Record<string, any>
}

export interface CacheQuery {
  namespace: CacheNamespace
  pattern?: string | RegExp
  filters?: Record<string, any>
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

export interface CacheInvalidation {
  namespace?: CacheNamespace
  keys?: CacheKey[]
  pattern?: string | RegExp
  olderThan?: number
  conditions?: Record<string, any>
}
