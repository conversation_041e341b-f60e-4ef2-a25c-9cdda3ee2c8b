export interface EmailMessage {
  to: string
  subject: string
  htmlBody: string
  textBody?: string
  trackingId?: string
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
}

export interface InboxEmail {
  id: string
  threadId: string
  from: string
  subject: string
  date: Date
  snippet: string
  isRead: boolean
  isStarred: boolean
  hasAttachments: boolean
  labels?: Array<{
    id: string
    name: string
    type: string
  }>
  body?: string
  attachments?: Array<{
    filename: string
    mimeType: string
    size: number
    attachmentId: string
  }>
  rawPayload?: any
  headers?: Array<{
    name: string
    value: string
  }>
  to?: string
  cc?: string
  bcc?: string
  replyTo?: string
  messageId?: string
  references?: string
  inReplyTo?: string
}

export interface ReplyEmailData {
  to: string
  subject: string
  htmlBody: string
  textBody?: string
  threadId: string
  replyToMessageId: string
  messageId?: string // Message-ID of the message being replied to
  references?: string // References header for thread continuity
  inReplyTo?: string // In-Reply-To header for direct reply relationship
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
}

export interface EmailOperationResult {
  success: boolean
  messageId?: string
  error?: string
}

export interface BulkEmailResult {
  totalSent: number
  totalFailed: number
  results: Array<{
    email: string
    success: boolean
    messageId?: string
    error?: string
    sentEmailId?: string
  }>
}

export interface EmailQuota {
  dailyLimit: number
  dailyUsed: number
  remaining: number
  resetTime: Date
}

export interface EmailListResult {
  emails: InboxEmail[]
  nextPageToken?: string
  totalCount: number
}

export interface GmailLabel {
  id: string
  name: string
  type: string
}

export interface BulkOperationResult {
  success: number
  failed: number
}

export interface DraftData {
  to: string
  cc?: string
  bcc?: string
  subject: string
  htmlBody: string
  textBody?: string
  threadId?: string
  replyToMessageId?: string
  messageId?: string
  references?: string
  inReplyTo?: string
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
}

export interface DraftOperationResult {
  success: boolean
  draftId?: string
  messageId?: string
  error?: string
}

export interface GmailDraft {
  id: string
  message: {
    id: string
    threadId?: string
    labelIds?: string[]
    snippet?: string
    payload?: any
    sizeEstimate?: number
    historyId?: string
    internalDate?: string
  }
}