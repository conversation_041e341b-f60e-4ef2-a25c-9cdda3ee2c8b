'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, MapPin, Users, Clock, ExternalLink, Star, Archive } from "lucide-react"
import { EmailAnalysis, ActionItem } from "@/lib/gemini"
import { format } from "date-fns"

interface EmailTileProps {
  analysis: EmailAnalysis
  originalEmail: any
  onOpenEmail: (emailId: string) => void
  onAddToCalendar: (actionItem: ActionItem) => void
  onAddAllToCalendar?: (actionItems: ActionItem[]) => void
}

export function EmailTile({ analysis, originalEmail, onOpenEmail, onAddToCalendar, onAddAllToCalendar }: EmailTileProps) {
  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-300'
      case 'medium':
        return 'bg-orange-100 text-orange-800 border-orange-300'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'work': 'bg-blue-100 text-blue-800',
      'personal': 'bg-purple-100 text-purple-800',
      'bills': 'bg-yellow-100 text-yellow-800',
      'social': 'bg-pink-100 text-pink-800',
      'promotional': 'bg-green-100 text-green-800',
      'general': 'bg-gray-100 text-gray-800'
    }
    return colors[category.toLowerCase()] || colors['general']
  }

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return '😊'
      case 'negative':
        return '😟'
      default:
        return '😐'
    }
  }

  const getActionTypeIcon = (type: string) => {
    switch (type) {
      case 'meeting':
        return <Calendar className="h-4 w-4" />
      case 'task':
        return <Clock className="h-4 w-4" />
      case 'delivery':
        return <MapPin className="h-4 w-4" />
      case 'reminder':
        return <Star className="h-4 w-4" />
      case 'follow-up':
        return <ExternalLink className="h-4 w-4" />
      default:
        return <Archive className="h-4 w-4" />
    }
  }

  return (
    <Card className="w-full hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-[#202124] mb-2 line-clamp-2">
              {originalEmail.subject}
            </CardTitle>
            <div className="flex items-center gap-2 text-sm text-[#5f6368] mb-2">
              <span className="font-medium">{originalEmail.from}</span>
              <span>•</span>
              <span>{format(new Date(originalEmail.date), 'MMM d, yyyy h:mm a')}</span>
              <span>{getSentimentIcon(analysis.sentiment)}</span>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            <Badge className={getImportanceColor(analysis.importance)}>
              {analysis.importance.toUpperCase()}
            </Badge>
            <Badge variant="outline" className={getCategoryColor(analysis.category)}>
              {analysis.category}
            </Badge>
            <div className="text-xs text-[#5f6368] bg-[#f8f9fa] px-2 py-1 rounded">
              Priority: {analysis.priority}/10
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* AI Summary */}
        <div className="mb-4 p-3 bg-[#f8f9fa] rounded-lg border-l-2 border-l-[#1a73e8]">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-2 h-2 bg-[#1a73e8] rounded-full"></div>
            <span className="text-sm font-medium text-[#1a73e8]">AI Summary</span>
          </div>
          <p className="text-sm text-[#3c4043] leading-relaxed">{analysis.summary}</p>
        </div>

        {/* Action Items */}
        {analysis.actionItems.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-[#202124] flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Action Items ({analysis.actionItems.length})
              </h4>
              {onAddAllToCalendar && analysis.actionItems.length > 1 && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onAddAllToCalendar(analysis.actionItems)}
                  className="text-xs bg-[#e8f0fe] text-[#1a73e8] border-[#1a73e8] hover:bg-[#d2e3fc]"
                >
                  <Calendar className="h-3 w-3 mr-1" />
                  Add All
                </Button>
              )}
            </div>
            <div className="space-y-2">
              {analysis.actionItems.slice(0, 3).map((item, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-white border border-[#e8eaed] rounded-md">
                  <div className="flex items-center gap-2 flex-1">
                    {getActionTypeIcon(item.type)}
                    <div className="flex-1">
                      <p className="text-sm font-medium text-[#202124]">{item.title}</p>
                      {item.description && (
                        <p className="text-xs text-[#5f6368] line-clamp-1">{item.description}</p>
                      )}
                      {item.dueDate && (
                        <p className="text-xs text-[#ea4335]">
                          Due: {format(item.dueDate, 'MMM d, yyyy')}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    {item.urgency === 'high' && (
                      <Badge variant="destructive" className="text-xs">High</Badge>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onAddToCalendar(item)}
                      className="text-xs h-7"
                    >
                      <Calendar className="h-3 w-3 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>
              ))}
              {analysis.actionItems.length > 3 && (
                <p className="text-xs text-[#5f6368] text-center">
                  +{analysis.actionItems.length - 3} more action items
                </p>
              )}
            </div>
          </div>
        )}

        {/* Original Email Snippet */}
        <div className="mb-4 p-3 bg-white border border-[#e8eaed] rounded-md">
          <h4 className="text-sm font-medium text-[#202124] mb-2">Original Content</h4>
          <p className="text-sm text-[#5f6368] line-clamp-3">
            {originalEmail.snippet || originalEmail.body?.substring(0, 200) + '...'}
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-2 border-t border-[#e8eaed]">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onOpenEmail(analysis.id)}
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open Email
          </Button>
          
          <div className="flex items-center gap-2">
            {analysis.actionItems.filter(item => item.type === 'meeting').length > 0 && (
              <Badge variant="outline" className="text-xs">
                <Calendar className="h-3 w-3 mr-1" />
                {analysis.actionItems.filter(item => item.type === 'meeting').length} Meeting(s)
              </Badge>
            )}
            {analysis.actionItems.filter(item => item.type === 'task').length > 0 && (
              <Badge variant="outline" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                {analysis.actionItems.filter(item => item.type === 'task').length} Task(s)
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 