import { NextRequest, NextResponse } from "next/server"
import { google } from 'googleapis'
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state') // This should be the user ID
    const error = searchParams.get('error')

    if (error) {
      console.error('OAuth error:', error)
      return NextResponse.redirect(new URL('/dashboard/calendar?error=oauth_error', request.url))
    }

    if (!code || !state) {
      return NextResponse.redirect(new URL('/dashboard/calendar?error=invalid_callback', request.url))
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXTAUTH_URL}/api/calendar/callback`
    )

    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code)
    
    if (!tokens.refresh_token) {
      return NextResponse.redirect(new URL('/dashboard/calendar?error=no_refresh_token', request.url))
    }

    // Update user with calendar tokens
    await prisma.user.update({
      where: { id: state },
      data: {
        calendarConnected: true,
        calendarRefreshToken: tokens.refresh_token
      }
    })

    console.log('Calendar connected successfully for user:', state)
    return NextResponse.redirect(new URL('/dashboard/calendar?connected=true', request.url))

  } catch (error) {
    console.error('Calendar callback error:', error)
    return NextResponse.redirect(new URL('/dashboard/calendar?error=callback_failed', request.url))
  }
} 