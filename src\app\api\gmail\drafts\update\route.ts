import { NextRequest } from "next/server"
import {
  updateDraft,
  with<PERSON><PERSON><PERSON><PERSON>
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { draftUpdateSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function PUT(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-draft-update', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, draftUpdateSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-draft-update')
      throw new Error(`Invalid draft update data: ${validation.error}`)
    }

    const {
      draftId,
      to,
      cc,
      bcc,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments
    } = validation.data

    // Update draft
    const result = await updateDraft(user.id, draftId, {
      to,
      cc,
      bcc,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments
    })

    if (!result.success) {
      endPerformanceMetric('gmail-draft-update')
      throw new Error(result.error || 'Failed to update draft')
    }

    // End performance monitoring
    endPerformanceMetric('gmail-draft-update')

    return {
      success: true,
      draftId: result.draftId,
      messageId: result.messageId,
      message: 'Draft updated successfully'
    }
  })
}
