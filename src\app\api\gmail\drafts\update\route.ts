import { NextRequest } from "next/server"
import { 
  updateDraft, 
  with<PERSON><PERSON><PERSON><PERSON>, 
  validateRequiredFields 
} from "@/lib/gmail"

export async function PUT(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['draftId', 'to', 'subject', 'htmlBody'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { 
      draftId,
      to, 
      cc, 
      bcc, 
      subject, 
      htmlBody, 
      textBody, 
      threadId, 
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments 
    } = body

    // Update draft
    const result = await updateDraft(user.id, draftId, {
      to,
      cc,
      bcc,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to update draft')
    }

    return {
      success: true,
      draftId: result.draftId,
      messageId: result.messageId,
      message: 'Draft updated successfully'
    }
  })
}
