"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Archive, 
  Trash2, 
  <PERSON>ly, 
  ReplyAll,
  Forward, 
  Star,
  MoreVertical,
  Download,
  Flag,
  Mail,
  Paperclip,
  RefreshCw,
  Inbox,
  Send,
  AlertCircle,
  Hash,
  Folder,
  Tag
} from "lucide-react"
import EmailRenderer from "./EmailRenderer"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { EmailViewerProps, EmailAttachment, EmailLabel } from './types'

// Helper function to format email address for display
function formatEmailAddress(email: string): { name: string; address: string } {
  if (!email) {
    return { name: 'Unknown', address: '<EMAIL>' }
  }
  const match = email.match(/^(.+?)\s*<(.+)>$/)
  if (match) {
    return { name: match[1].trim().replace(/"/g, ''), address: match[2].trim() }
  }
  return { name: email, address: email }
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Helper function to get avatar initials
function getInitials(name: string): string {
  if (!name || name.trim() === '') {
    return 'U'
  }
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

// Helper function to extract sender name from email
function extractSenderName(email: string): string {
  const { name } = formatEmailAddress(email)
  return name || 'Unknown'
}

// Helper function to extract sender email from email
function extractSenderEmail(email: string): string {
  const { address } = formatEmailAddress(email)
  return address || '<EMAIL>'
}

// Helper function to format date
function formatDate(date: Date | string) {
  const d = new Date(date)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (d.toDateString() === today.toDateString()) {
    return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  } else if (d.toDateString() === yesterday.toDateString()) {
    return 'Yesterday'
  } else if (d.getFullYear() === today.getFullYear()) {
    return d.toLocaleDateString([], { month: 'short', day: 'numeric' })
  } else {
    return d.toLocaleDateString()
  }
}

// Component for email attachments
function AttachmentList({ attachments, emailId }: { attachments: EmailAttachment[], emailId: string }) {
  if (!attachments?.length) return null

  return (
    <div className="mt-4">
      <p className="text-sm font-medium mb-2 flex items-center text-gray-700">
        <Paperclip className="w-4 h-4 mr-2" />
        {attachments.length} Attachment{attachments.length > 1 ? 's' : ''}
      </p>
      <div className="grid gap-2">
        {attachments.map((attachment, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-2 border border-gray-200 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
          >
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <span className="text-xs font-medium text-blue-700">
                  {attachment.filename.split('.').pop()?.toUpperCase().slice(0, 3) || 'FILE'}
                </span>
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium truncate text-gray-900">{attachment.filename}</p>
                <p className="text-xs text-gray-500">{formatFileSize(attachment.size)}</p>
              </div>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              className="flex-shrink-0 ml-2"
              onClick={() => {
                window.open(`/api/gmail/inbox/${emailId}/attachment/${attachment.attachmentId}`, '_blank')
              }}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}

// Define label colors and icons (exactly same as EmailPageLayout)
const getLabelStyle = (labelName: string, labelType: string) => {
  const name = labelName.toUpperCase()
  
  // System labels
  if (labelType === 'system') {
    switch (name) {
      case 'IMPORTANT':
        return { bg: 'bg-yellow-100', text: 'text-yellow-700', border: 'border-yellow-200', icon: Flag }
      case 'STARRED':
        return { bg: 'bg-blue-100', text: 'text-blue-700', border: 'border-blue-200', icon: Star }
      case 'UNREAD':
        return { bg: 'bg-purple-100', text: 'text-purple-700', border: 'border-purple-200', icon: Mail }
      case 'INBOX':
        return { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-200', icon: Inbox }
      case 'SENT':
        return { bg: 'bg-green-100', text: 'text-green-700', border: 'border-green-200', icon: Send }
      case 'DRAFT':
        return { bg: 'bg-orange-100', text: 'text-orange-700', border: 'border-orange-200', icon: Mail }
      case 'SPAM':
        return { bg: 'bg-red-100', text: 'text-red-700', border: 'border-red-200', icon: AlertCircle }
      case 'TRASH':
        return { bg: 'bg-red-100', text: 'text-red-700', border: 'border-red-200', icon: Trash2 }
      default:
        return { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-200', icon: Tag }
    }
  }
  
  // User labels - use a hash of the label name to assign consistent colors
  const colors = [
    { bg: 'bg-indigo-100', text: 'text-indigo-700', border: 'border-indigo-200' },
    { bg: 'bg-pink-100', text: 'text-pink-700', border: 'border-pink-200' },
    { bg: 'bg-emerald-100', text: 'text-emerald-700', border: 'border-emerald-200' },
    { bg: 'bg-amber-100', text: 'text-amber-700', border: 'border-amber-200' },
    { bg: 'bg-cyan-100', text: 'text-cyan-700', border: 'border-cyan-200' },
    { bg: 'bg-rose-100', text: 'text-rose-700', border: 'border-rose-200' },
    { bg: 'bg-violet-100', text: 'text-violet-700', border: 'border-violet-200' },
    { bg: 'bg-teal-100', text: 'text-teal-700', border: 'border-teal-200' }
  ]
  
  const hash = labelName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
  const colorIndex = hash % colors.length
  
  return { ...colors[colorIndex], icon: labelType === 'user' ? Hash : Folder }
}

// Component for displaying labels as icons (same as EmailPageLayout)
const EmailLabelIcons = ({ labels }: { labels: EmailLabel[] }) => {
  if (!labels || labels.length === 0) return null
  
  // Filter out system labels we don't want to show
  const displayLabels = labels.filter(label => 
    !['INBOX', 'UNREAD', 'STARRED'].includes(label.name.toUpperCase()) || label.type === 'user'
  )
  
  if (displayLabels.length === 0) return null
  
  return (
    <div className="flex items-center gap-1" onClick={(e) => e.stopPropagation()}>
      {displayLabels.map((label) => {
        const style = getLabelStyle(label.name, label.type)
        const Icon = style.icon
        
        return (
          <div
            key={label.id}
            className={`
              relative group/label flex items-center justify-center h-5 w-5 rounded cursor-default
              ${style.bg} ${style.border}
              transition-all duration-200
            `}
          >
            <Icon className={`h-3 w-3 ${style.text}`} />
            
            {/* Tooltip - only shows on direct icon hover */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap opacity-0 group-hover/label:opacity-100 pointer-events-none transition-opacity duration-200 z-50">
              {label.name}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900"></div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

// Main EmailViewer component
export default function EmailViewer({ 
  email, 
  onAction,
  showActions = true,
  className = "" 
}: EmailViewerProps) {
  const [actionLoading, setActionLoading] = useState(false)

  const handleAction = async (action: string) => {
    setActionLoading(true)
    try {
      onAction?.(action, email.id)
    } finally {
      setActionLoading(false)
    }
  }

  const formatEmailAddresses = (addresses: string) => {
    if (!addresses) return ''
    
    return addresses.split(',').map(addr => {
      const trimmed = addr.trim()
      const match = trimmed.match(/^(.+?)\s*<.*>$/)
      if (match) {
        return match[1].replace(/"/g, '')
      }
      return trimmed
    }).join(', ')
  }

  return (
    <div className={`bg-white h-full flex flex-col ${className}`}>
      {/* Email Header */}
      <div className="border-b border-gray-100 p-4 flex-shrink-0">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start gap-3 flex-1">
            <Avatar className="w-10 h-10">
              <AvatarFallback className="text-sm font-medium bg-gray-100 text-gray-700">
                {extractSenderName(email.from).charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h1 className="text-lg font-semibold text-gray-900 truncate">{email.subject}</h1>
                {/* Label Icons beside title */}
                {email.labels && email.labels.length > 0 && (
                  <EmailLabelIcons labels={email.labels} />
                )}
              </div>
              <div className="space-y-1 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="font-medium truncate">{extractSenderName(email.from)}</span>
                  <span className="text-gray-400 truncate">&lt;{extractSenderEmail(email.from)}&gt;</span>
                </div>
                <div className="flex items-center gap-4 text-xs">
                  <span className="truncate">to {email.to ? formatEmailAddresses(email.to) : 'me'}</span>
                  {email.cc && (
                    <span className="truncate">cc {formatEmailAddresses(email.cc)}</span>
                  )}
                  <span className="flex-shrink-0">{formatDate(email.date)}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-1 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAction('star')}
              disabled={actionLoading}
              className="text-gray-600 hover:text-gray-900 h-8 w-8 p-0"
              title={email.isStarred ? "Remove star" : "Add star"}
            >
              {actionLoading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Star className={`w-4 h-4 ${email.isStarred ? 'fill-yellow-400 text-yellow-400' : ''}`} />
              )}
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" disabled={actionLoading} className="text-gray-600 hover:text-gray-900 h-8 w-8 p-0">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleAction('archive')}>
                  <Archive className="w-4 h-4 mr-2" />
                  Archive
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAction('trash')}>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleAction('markUnread')}>
                  <Mail className="w-4 h-4 mr-2" />
                  Mark as Unread
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

      </div>

      {/* Email Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <EmailRenderer 
            body={email.body} 
            enableQuoteToggle={true}
            className="prose prose-sm max-w-none"
          />
          
          {/* Attachments */}
          <AttachmentList attachments={email.attachments || []} emailId={email.id} />
        </div>
      </div>

      {/* Action Bar (bottom) */}
      {showActions && (
        <div className="border-t border-gray-100 px-3 py-2 bg-gray-50 flex-shrink-0">
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="default"
              size="sm"
              onClick={() => handleAction('reply')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1.5 h-8"
            >
              <Reply className="w-4 h-4 mr-2" />
              Reply
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleAction('replyAll')}
              className="border-gray-300 hover:bg-gray-100 px-4 py-1.5 h-8"
            >
              <ReplyAll className="w-4 h-4 mr-2" />
              Reply all
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleAction('forward')}
              className="border-gray-300 hover:bg-gray-100 px-4 py-1.5 h-8"
            >
              <Forward className="w-4 h-4 mr-2" />
              Forward
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
