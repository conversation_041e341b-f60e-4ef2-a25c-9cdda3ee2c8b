import { makeMeetApiRequest } from './client'
import { EventSubscription, MeetEvent, ListResponse } from './types'

/**
 * Create an event subscription for Meet events
 * Note: This requires proper webhook setup and Google Cloud Pub/Sub configuration
 */
export async function createEventSubscription(
  userId: string,
  options: {
    targetResource: string // e.g., "conferenceRecords/*"
    eventTypes: string[] // e.g., ["google.workspace.meet.conference.v2.started"]
    pubsubTopic: string // Google Cloud Pub/Sub topic
    includeResource?: boolean
    fieldMask?: string
  }
): Promise<EventSubscription> {
  const requestBody = {
    targetResource: options.targetResource,
    eventTypes: options.eventTypes,
    payloadOptions: {
      includeResource: options.includeResource || false,
      fieldMask: options.fieldMask
    },
    notificationEndpoint: {
      pubsubTopic: options.pubsubTopic
    }
  }

  const response = await makeMeetApiRequest(userId, 'subscriptions', {
    method: 'POST',
    body: JSON.stringify(requestBody)
  })

  const data = await response.json()
  return data as EventSubscription
}

/**
 * Get an event subscription
 */
export async function getEventSubscription(
  userId: string,
  subscriptionName: string
): Promise<EventSubscription> {
  const response = await makeMeetApiRequest(userId, `subscriptions/${subscriptionName}`)
  const data = await response.json()
  return data as EventSubscription
}

/**
 * List event subscriptions
 */
export async function listEventSubscriptions(
  userId: string,
  options: {
    pageSize?: number
    pageToken?: string
    filter?: string
  } = {}
): Promise<ListResponse<EventSubscription>> {
  const params = new URLSearchParams()
  
  if (options.pageSize) {
    params.append('pageSize', options.pageSize.toString())
  }
  
  if (options.pageToken) {
    params.append('pageToken', options.pageToken)
  }
  
  if (options.filter) {
    params.append('filter', options.filter)
  }

  const endpoint = `subscriptions${params.toString() ? `?${params.toString()}` : ''}`
  const response = await makeMeetApiRequest(userId, endpoint)
  const data = await response.json()
  
  return {
    items: data.subscriptions || [],
    nextPageToken: data.nextPageToken,
    totalSize: data.subscriptions?.length
  }
}

/**
 * Update an event subscription
 */
export async function updateEventSubscription(
  userId: string,
  subscriptionName: string,
  updates: {
    eventTypes?: string[]
    payloadOptions?: {
      includeResource?: boolean
      fieldMask?: string
    }
  },
  updateMask?: string[]
): Promise<EventSubscription> {
  const params = new URLSearchParams()
  if (updateMask && updateMask.length > 0) {
    params.append('updateMask', updateMask.join(','))
  }

  const endpoint = `subscriptions/${subscriptionName}${params.toString() ? `?${params.toString()}` : ''}`
  
  const response = await makeMeetApiRequest(userId, endpoint, {
    method: 'PATCH',
    body: JSON.stringify(updates)
  })

  const data = await response.json()
  return data as EventSubscription
}

/**
 * Delete an event subscription
 */
export async function deleteEventSubscription(
  userId: string,
  subscriptionName: string
): Promise<void> {
  await makeMeetApiRequest(userId, `subscriptions/${subscriptionName}`, {
    method: 'DELETE'
  })
}

/**
 * Reactivate a suspended event subscription
 */
export async function reactivateEventSubscription(
  userId: string,
  subscriptionName: string
): Promise<EventSubscription> {
  const response = await makeMeetApiRequest(userId, `subscriptions/${subscriptionName}:reactivate`, {
    method: 'POST'
  })

  const data = await response.json()
  return data as EventSubscription
}

/**
 * Process incoming Meet event from webhook
 * This would be called by your webhook endpoint
 */
export function processMeetEvent(eventData: any): MeetEvent {
  return {
    eventType: eventData.eventType,
    eventTime: eventData.eventTime,
    meetingSpace: eventData.meetingSpace,
    conferenceRecord: eventData.conferenceRecord,
    participant: eventData.participant
  }
}

/**
 * Set up common Meet event subscriptions
 */
export async function setupCommonMeetSubscriptions(
  userId: string,
  pubsubTopic: string
): Promise<{
  conferenceSubscription: EventSubscription
  participantSubscription: EventSubscription
}> {
  // Subscribe to conference events
  const conferenceSubscription = await createEventSubscription(userId, {
    targetResource: "conferenceRecords/*",
    eventTypes: [
      "google.workspace.meet.conference.v2.started",
      "google.workspace.meet.conference.v2.ended"
    ],
    pubsubTopic,
    includeResource: true
  })

  // Subscribe to participant events
  const participantSubscription = await createEventSubscription(userId, {
    targetResource: "conferenceRecords/*/participants/*",
    eventTypes: [
      "google.workspace.meet.participant.v2.joined",
      "google.workspace.meet.participant.v2.left"
    ],
    pubsubTopic,
    includeResource: false
  })

  return {
    conferenceSubscription,
    participantSubscription
  }
}

/**
 * Get available event types for Meet
 */
export function getAvailableMeetEventTypes(): {
  category: string
  eventTypes: string[]
  description: string
}[] {
  return [
    {
      category: "Conference Events",
      eventTypes: [
        "google.workspace.meet.conference.v2.started",
        "google.workspace.meet.conference.v2.ended"
      ],
      description: "Events related to conference lifecycle"
    },
    {
      category: "Participant Events", 
      eventTypes: [
        "google.workspace.meet.participant.v2.joined",
        "google.workspace.meet.participant.v2.left"
      ],
      description: "Events related to participant activity"
    },
    {
      category: "Recording Events",
      eventTypes: [
        "google.workspace.meet.recording.v2.fileGenerated"
      ],
      description: "Events related to meeting recordings"
    },
    {
      category: "Transcript Events",
      eventTypes: [
        "google.workspace.meet.transcript.v2.fileGenerated"
      ],
      description: "Events related to meeting transcripts"
    }
  ]
}

/**
 * Validate event subscription configuration
 */
export function validateEventSubscriptionConfig(config: {
  targetResource: string
  eventTypes: string[]
  pubsubTopic: string
}): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Validate target resource format
  if (!config.targetResource || !config.targetResource.includes('/')) {
    errors.push('Target resource must be in format "resource/*" or "resource/id"')
  }

  // Validate event types
  if (!config.eventTypes || config.eventTypes.length === 0) {
    errors.push('At least one event type must be specified')
  }

  const validEventTypes = getAvailableMeetEventTypes().flatMap(cat => cat.eventTypes)
  const invalidEventTypes = config.eventTypes.filter(type => !validEventTypes.includes(type))
  if (invalidEventTypes.length > 0) {
    errors.push(`Invalid event types: ${invalidEventTypes.join(', ')}`)
  }

  // Validate Pub/Sub topic format
  if (!config.pubsubTopic || !config.pubsubTopic.startsWith('projects/')) {
    errors.push('Pub/Sub topic must be in format "projects/PROJECT_ID/topics/TOPIC_NAME"')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}
