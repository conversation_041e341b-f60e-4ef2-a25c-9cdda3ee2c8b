// IndexedDB-based draft caching system for offline-first draft functionality

export interface CachedDraft {
  id: string // Local cache ID
  gmailDraftId?: string // Gmail API draft ID (when synced)
  userId: string
  to: string
  cc?: string
  bcc?: string
  subject: string
  htmlBody: string
  textBody?: string
  threadId?: string
  replyToMessageId?: string
  messageId?: string
  references?: string
  inReplyTo?: string
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
  createdAt: Date
  updatedAt: Date
  lastSyncedAt?: Date
  syncStatus: 'pending' | 'synced' | 'error' | 'conflict'
  syncError?: string
  isDeleted: boolean
  version: number // For conflict resolution
}

export interface DraftCacheStats {
  totalDrafts: number
  pendingSync: number
  syncErrors: number
  lastSyncTime?: Date
}

class DraftCacheManager {
  private dbName = 'DraftCache'
  private dbVersion = 1
  private storeName = 'drafts'
  private db: IDBDatabase | null = null

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // Create drafts store
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'id' })
          
          // Create indexes for efficient querying
          store.createIndex('userId', 'userId', { unique: false })
          store.createIndex('gmailDraftId', 'gmailDraftId', { unique: false })
          store.createIndex('syncStatus', 'syncStatus', { unique: false })
          store.createIndex('updatedAt', 'updatedAt', { unique: false })
          store.createIndex('threadId', 'threadId', { unique: false })
        }
      }
    })
  }

  private async ensureDB(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.init()
    }
    return this.db!
  }

  private generateId(): string {
    return `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  async saveDraft(draft: Omit<CachedDraft, 'id' | 'createdAt' | 'updatedAt' | 'version'>): Promise<CachedDraft> {
    const db = await this.ensureDB()
    const now = new Date()
    
    const cachedDraft: CachedDraft = {
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
      version: 1,
      // syncStatus: 'pending',
      // isDeleted: false,
      ...draft
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.add(cachedDraft)

      request.onsuccess = () => resolve(cachedDraft)
      request.onerror = () => reject(request.error)
    })
  }

  async updateDraft(id: string, updates: Partial<CachedDraft>): Promise<CachedDraft | null> {
    const db = await this.ensureDB()
    const existing = await this.getDraft(id)
    
    if (!existing) {
      return null
    }

    const updatedDraft: CachedDraft = {
      ...existing,
      ...updates,
      updatedAt: new Date(),
      version: existing.version + 1,
      syncStatus: updates.syncStatus || 'pending'
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.put(updatedDraft)

      request.onsuccess = () => resolve(updatedDraft)
      request.onerror = () => reject(request.error)
    })
  }

  async getDraft(id: string): Promise<CachedDraft | null> {
    const db = await this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.get(id)

      request.onsuccess = () => {
        const result = request.result
        if (result) {
          // Convert date strings back to Date objects
          result.createdAt = new Date(result.createdAt)
          result.updatedAt = new Date(result.updatedAt)
          if (result.lastSyncedAt) {
            result.lastSyncedAt = new Date(result.lastSyncedAt)
          }
        }
        resolve(result || null)
      }
      request.onerror = () => reject(request.error)
    })
  }

  async getDraftsByUser(userId: string): Promise<CachedDraft[]> {
    const db = await this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('userId')
      const request = index.getAll(userId)

      request.onsuccess = () => {
        const results = request.result.map((draft: any) => ({
          ...draft,
          createdAt: new Date(draft.createdAt),
          updatedAt: new Date(draft.updatedAt),
          lastSyncedAt: draft.lastSyncedAt ? new Date(draft.lastSyncedAt) : undefined
        }))
        resolve(results.filter(draft => !draft.isDeleted))
      }
      request.onerror = () => reject(request.error)
    })
  }

  async getPendingSyncDrafts(userId: string): Promise<CachedDraft[]> {
    const db = await this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.getAll()

      request.onsuccess = () => {
        const results = request.result
          .filter((draft: CachedDraft) => 
            draft.userId === userId && 
            draft.syncStatus === 'pending' && 
            !draft.isDeleted
          )
          .map((draft: any) => ({
            ...draft,
            createdAt: new Date(draft.createdAt),
            updatedAt: new Date(draft.updatedAt),
            lastSyncedAt: draft.lastSyncedAt ? new Date(draft.lastSyncedAt) : undefined
          }))
        resolve(results)
      }
      request.onerror = () => reject(request.error)
    })
  }

  async markDraftSynced(id: string, gmailDraftId: string): Promise<void> {
    await this.updateDraft(id, {
      gmailDraftId,
      syncStatus: 'synced',
      lastSyncedAt: new Date()
    })
  }

  async markDraftSyncError(id: string, error: string): Promise<void> {
    await this.updateDraft(id, {
      syncStatus: 'error',
      syncError: error
    })
  }

  async deleteDraft(id: string): Promise<void> {
    await this.updateDraft(id, {
      isDeleted: true,
      syncStatus: 'pending'
    })
  }

  async purgeDraft(id: string): Promise<void> {
    const db = await this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      const request = store.delete(id)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  async getCacheStats(userId: string): Promise<DraftCacheStats> {
    const drafts = await this.getDraftsByUser(userId)
    const pendingSync = drafts.filter(d => d.syncStatus === 'pending').length
    const syncErrors = drafts.filter(d => d.syncStatus === 'error').length
    const lastSyncTime = drafts
      .filter(d => d.lastSyncedAt)
      .sort((a, b) => (b.lastSyncedAt?.getTime() || 0) - (a.lastSyncedAt?.getTime() || 0))[0]?.lastSyncedAt

    return {
      totalDrafts: drafts.length,
      pendingSync,
      syncErrors,
      lastSyncTime
    }
  }

  async clearCache(userId?: string): Promise<void> {
    const db = await this.ensureDB()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)

      if (userId) {
        // Clear only user's drafts
        const index = store.index('userId')
        const request = index.openCursor(userId)
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result
          if (cursor) {
            cursor.delete()
            cursor.continue()
          } else {
            resolve()
          }
        }
        request.onerror = () => reject(request.error)
      } else {
        // Clear all drafts
        const request = store.clear()
        request.onsuccess = () => resolve()
        request.onerror = () => reject(request.error)
      }
    })
  }
}

// Singleton instance
export const draftCache = new DraftCacheManager()

// Initialize cache on module load
if (typeof window !== 'undefined') {
  draftCache.init().catch(console.error)
}
