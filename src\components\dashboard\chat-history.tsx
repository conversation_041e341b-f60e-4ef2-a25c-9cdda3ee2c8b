"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  MessageSquare,
  Plus,
  MoreHorizontal,
  Edit3,
  Trash2,
  Archive,
  Clock
} from "lucide-react"
import { format } from "date-fns"
import { ChatSession } from "@/lib/chat-encryption"

interface ChatHistoryProps {
  currentSessionId?: string
  onSessionSelect?: (sessionId: string) => void
  onSessionCreate?: () => void
  onSessionDelete?: (sessionId: string) => void
  onSessionRename?: (sessionId: string, newTitle: string) => void
}

export function ChatHistory({
  currentSessionId,
  onSessionSelect,
  onSessionCreate,
  onSessionDelete,
  onSessionRename
}: ChatHistoryProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState("")

  useEffect(() => {
    loadSessions()
  }, [])

  const loadSessions = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/chat/sessions', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (data.sessions && Array.isArray(data.sessions)) {
        // Sort sessions by updatedAt descending
        const sortedSessions = data.sessions.sort((a: ChatSession, b: ChatSession) => 
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        )
        setSessions(sortedSessions)
        console.log('Loaded chat sessions:', sortedSessions.length)
      } else {
        console.log('No sessions data received:', data)
        setSessions([])
      }
    } catch (error) {
      console.error('Error loading chat sessions:', error)
      setSessions([])
    } finally {
      setIsLoading(false)
    }
  }

  const createNewSession = async () => {
    try {
      const response = await fetch('/api/chat/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: 'New Chat' })
      })
      
      const data = await response.json()
      
      if (data.session) {
        setSessions(prev => [data.session, ...prev])
        
        if (onSessionCreate) {
          onSessionCreate()
        }
        
        // Navigate to AI page with new session
        router.push(`/dashboard/ai?session=${data.session.id}`)
      }
    } catch (error) {
      console.error('Error creating session:', error)
    }
  }

  const handleSessionSelect = (sessionId: string) => {
    if (onSessionSelect) {
      onSessionSelect(sessionId)
    }
    router.push(`/dashboard/ai?session=${sessionId}`)
  }

  const handleSessionDelete = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/chat/sessions?sessionId=${sessionId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setSessions(prev => prev.filter(s => s.id !== sessionId))
        
        if (onSessionDelete) {
          onSessionDelete(sessionId)
        }
        
        // If we're deleting the current session, redirect to new session
        if (currentSessionId === sessionId) {
          router.push('/dashboard/ai')
        }
      }
    } catch (error) {
      console.error('Error deleting session:', error)
    }
  }

  const handleSessionRename = async (sessionId: string, newTitle: string) => {
    try {
      const response = await fetch('/api/chat/sessions', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, title: newTitle })
      })
      
      if (response.ok) {
        setSessions(prev => prev.map(session => 
          session.id === sessionId 
            ? { ...session, title: newTitle, updatedAt: new Date() }
            : session
        ))
        
        if (onSessionRename) {
          onSessionRename(sessionId, newTitle)
        }
      }
    } catch (error) {
      console.error('Error renaming session:', error)
    }
    
    setEditingSessionId(null)
    setEditTitle("")
  }

  const startEditing = (session: ChatSession) => {
    setEditingSessionId(session.id)
    setEditTitle(session.title)
  }

  const cancelEditing = () => {
    setEditingSessionId(null)
    setEditTitle("")
  }

  const finishEditing = () => {
    if (editingSessionId && editTitle.trim()) {
      handleSessionRename(editingSessionId, editTitle.trim())
    } else {
      cancelEditing()
    }
  }

  // Group sessions by date
  const groupedSessions = sessions.reduce((groups, session) => {
    try {
      const sessionDate = new Date(session.updatedAt)
      const date = format(sessionDate, 'yyyy-MM-dd')
      const today = format(new Date(), 'yyyy-MM-dd')
      const yesterday = format(new Date(Date.now() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd')
      
      let groupKey = date
      if (date === today) {
        groupKey = 'Today'
      } else if (date === yesterday) {
        groupKey = 'Yesterday'
      } else {
        groupKey = format(sessionDate, 'MMMM d, yyyy')
      }
      
      if (!groups[groupKey]) {
        groups[groupKey] = []
      }
      groups[groupKey].push(session)
      return groups
    } catch (error) {
      console.error('Error processing session date:', session, error)
      return groups
    }
  }, {} as Record<string, ChatSession[]>)

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="p-3 border-b border-border">
        <Button
          onClick={createNewSession}
          className="w-full justify-start gap-2 h-9"
          variant="ghost"
        >
          <Plus className="h-4 w-4" />
          New Chat
        </Button>
      </div>

      {/* Chat List */}
      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {isLoading ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              Loading chats...
            </div>
          ) : Object.keys(groupedSessions).length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No chat history yet
            </div>
          ) : (
            Object.entries(groupedSessions).map(([groupKey, groupSessions]) => (
              <div key={groupKey} className="mb-4">
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  {groupKey}
                </div>
                <div className="space-y-1">
                  {groupSessions.map((session) => (
                    <div
                      key={session.id}
                      className={`group relative rounded-lg transition-colors ${
                        currentSessionId === session.id
                          ? 'bg-accent'
                          : 'hover:bg-accent/50'
                      }`}
                    >
                      {editingSessionId === session.id ? (
                        <div className="p-2">
                          <input
                            type="text"
                            value={editTitle}
                            onChange={(e) => setEditTitle(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                finishEditing()
                              } else if (e.key === 'Escape') {
                                cancelEditing()
                              }
                            }}
                            onBlur={finishEditing}
                            className="w-full bg-transparent border-none outline-none text-sm"
                            autoFocus
                          />
                        </div>
                      ) : (
                        <div
                          className="flex items-center gap-2 p-2 cursor-pointer"
                          onClick={() => handleSessionSelect(session.id)}
                        >
                          <MessageSquare className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium truncate">
                              {session.title}
                            </div>
                                                         <div className="text-xs text-muted-foreground flex items-center gap-1">
                               <Clock className="h-3 w-3" />
                               {format(new Date(session.updatedAt), 'h:mm a')}
                               {session.messages && session.messages.length > 0 && (
                                 <span>• {session.messages.length} messages</span>
                               )}
                             </div>
                          </div>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreHorizontal className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-40">
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  startEditing(session)
                                }}
                              >
                                <Edit3 className="h-3 w-3 mr-2" />
                                Rename
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleSessionDelete(session.id)
                                }}
                                className="text-destructive focus:text-destructive"
                              >
                                <Trash2 className="h-3 w-3 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  )
} 