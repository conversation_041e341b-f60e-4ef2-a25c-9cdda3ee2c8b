import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetUser, 
  handleMeetApiError, 
  formatMeetApiResponse,
  validatePaginationParams,
  validateDateRange,
  buildMeetFilter,
  listConferenceRecords,
  getRecentConferenceRecords,
  getConferenceStatistics,
  searchConferenceRecords
} from "@/lib/meet"

/**
 * GET /api/meet/conferences - List conference records with filtering
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const searchParams = url.searchParams

    // Handle different query types
    const queryType = searchParams.get('type') || 'list'

    switch (queryType) {
      case 'recent':
        return await handleRecentConferences(user.id, searchParams)
      
      case 'statistics':
        return await handleConferenceStatistics(user.id, searchParams)
      
      case 'search':
        return await handleConferenceSearch(user.id, searchParams)
      
      case 'list':
      default:
        return await handleListConferences(user.id, searchParams)
    }
  } catch (error) {
    return handleMeetApiError(error)
  }
}

async function handleListConferences(userId: string, searchParams: URLSearchParams) {
  const { pageSize, pageToken } = validatePaginationParams(searchParams)
  const filter = buildMeetFilter(searchParams)

  const conferences = await listConferenceRecords(userId, {
    pageSize,
    pageToken,
    filter
  })

  return formatMeetApiResponse(
    conferences,
    "Conference records retrieved successfully"
  )
}

async function handleRecentConferences(userId: string, searchParams: URLSearchParams) {
  const days = parseInt(searchParams.get('days') || '7')
  const pageSize = parseInt(searchParams.get('pageSize') || '20')

  if (days < 1 || days > 365) {
    return NextResponse.json({
      error: "Days parameter must be between 1 and 365"
    }, { status: 400 })
  }

  const conferences = await getRecentConferenceRecords(userId, days, pageSize)

  return formatMeetApiResponse(
    conferences,
    `Recent conference records for the last ${days} days retrieved successfully`
  )
}

async function handleConferenceStatistics(userId: string, searchParams: URLSearchParams) {
  try {
    const { startTime, endTime } = validateDateRange(searchParams)
    
    const statistics = await getConferenceStatistics(userId, startTime, endTime)

    return formatMeetApiResponse(
      statistics,
      "Conference statistics retrieved successfully"
    )
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : "Invalid date range parameters"
    }, { status: 400 })
  }
}

async function handleConferenceSearch(userId: string, searchParams: URLSearchParams) {
  const { pageSize, pageToken } = validatePaginationParams(searchParams)
  
  // Build search criteria
  const criteria: any = {}
  
  const spaceName = searchParams.get('spaceName')
  if (spaceName) criteria.spaceName = spaceName
  
  const startDate = searchParams.get('startDate')
  if (startDate) criteria.startDate = startDate
  
  const endDate = searchParams.get('endDate')
  if (endDate) criteria.endDate = endDate
  
  const minDuration = searchParams.get('minDuration')
  if (minDuration) criteria.minDuration = parseInt(minDuration)
  
  const hasRecording = searchParams.get('hasRecording')
  if (hasRecording) criteria.hasRecording = hasRecording === 'true'
  
  const hasTranscript = searchParams.get('hasTranscript')
  if (hasTranscript) criteria.hasTranscript = hasTranscript === 'true'

  const conferences = await searchConferenceRecords(userId, criteria, {
    pageSize,
    pageToken
  })

  return formatMeetApiResponse(
    conferences,
    "Conference search completed successfully"
  )
}
