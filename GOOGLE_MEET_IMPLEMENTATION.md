# Google Meet Integration - Correct Implementation Guide

## The Problem We Solved

**Original Issue**: Participants couldn't see each other in meetings - only black screens were displayed.

**Root Cause**: We were trying to embed Google Meet in our application using iframes, which doesn't work due to:
1. Google Meet's X-Frame-Options: SAMEORIGIN security policy prevents iframe embedding
2. Mock meeting interfaces don't provide real video streams
3. Participants were not actually in the same Google Meet session

## The Correct Solution: Google Meet Add-ons

Instead of trying to embed Google Meet in our app, we build our app as a **Google Meet Add-on** that runs **inside** Google Meet.

### How It Works

1. **Meeting Creation**: We still use the Google Meet REST API to create meeting spaces
2. **Meeting Join**: Users join the actual Google Meet session normally
3. **Enhanced Features**: Our add-on provides additional collaborative features within the meeting
4. **Visibility**: All participants see each other because they're in the same Google Meet session

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Google Meet Session                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Participant   │  │   Participant   │  │ Participant  │ │
│  │       A         │  │       B         │  │      C       │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Our Add-on (Side Panel)                   │ │
│  │  - Collaborative tools                                 │ │
│  │  - Launch main stage activities                       │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Main Stage (When Active)                  │ │
│  │  - Shared workspace visible to all                    │ │
│  │  - Real-time collaboration                            │ │
│  │  - Synchronized activities                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Implementation Components

### 1. Meeting Creation (Existing)
- **File**: `src/lib/meet/client.ts`
- **Purpose**: Creates Google Meet spaces using REST API
- **Status**: ✅ Already working correctly

### 2. Add-on Side Panel
- **File**: `src/components/meet/MeetAddonSidePanel.tsx`
- **Route**: `/meet-addon/sidepanel`
- **Purpose**: Private interface only the add-on user sees
- **Features**:
  - Initialize Google Meet Add-ons SDK
  - Launch collaborative activities
  - Control main stage content

### 3. Add-on Main Stage
- **File**: `src/components/meet/MeetAddonMainStage.tsx`
- **Route**: `/meet-addon/mainstage`
- **Purpose**: Shared workspace all participants see
- **Features**:
  - Collaborative chat
  - Real-time synchronization
  - Shared activities

### 4. Meeting Join Interface
- **File**: `src/components/meet/MeetingJoinInterface.tsx`
- **Purpose**: Guides users to join Google Meet and access add-on
- **Features**:
  - Clear join instructions
  - Meeting code/link copying
  - Add-on usage guide

## Key Technical Details

### Google Meet Add-ons SDK Integration

```javascript
// Load SDK
<script src="https://www.gstatic.com/meetjs/addons/1.1.0/meet.addons.js"></script>

// Initialize session
const session = await window.meet.addon.createAddonSession({
  cloudProjectNumber: 'YOUR_PROJECT_NUMBER',
});

// Create side panel client
const sidePanelClient = await session.createSidePanelClient();

// Launch main stage activity
await sidePanelClient.startActivity({
  mainStageUrl: 'https://yourapp.com/meet-addon/mainstage'
});
```

### Environment Variables Required

```env
NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_NUMBER=your_project_number
NEXT_PUBLIC_BASE_URL=https://yourapp.com
```

## Deployment Requirements

### 1. Deploy Add-on Pages
- Deploy `/meet-addon/sidepanel` and `/meet-addon/mainstage` to your domain
- Ensure HTTPS is enabled (required by Google Meet)

### 2. Google Cloud Console Configuration
1. Go to Google Cloud Console
2. Navigate to Google Workspace Add-ons
3. Create a new Meet add-on deployment
4. Configure:
   - **Side Panel URL**: `https://yourapp.com/meet-addon/sidepanel`
   - **Main Stage URL**: `https://yourapp.com/meet-addon/mainstage`
   - **Cloud Project Number**: Your GCP project number

### 3. Add-on Manifest (if using Apps Script)
```json
{
  "addOns": {
    "meet": {
      "sidePanelUrl": "https://yourapp.com/meet-addon/sidepanel",
      "mainStageUrl": "https://yourapp.com/meet-addon/mainstage"
    }
  }
}
```

## User Experience Flow

### For Meeting Organizers:
1. Create meeting in your app (existing functionality)
2. Click "Join Google Meet" 
3. In Google Meet, click meeting tools (apps icon)
4. Select your add-on from "Your add-ons"
5. Use side panel to launch collaborative activities
6. All participants see shared activities in main stage

### For Meeting Participants:
1. Join Google Meet using provided link/code
2. See video/audio of all other participants (normal Google Meet)
3. When organizer launches add-on activities, see shared content
4. Interact with collaborative features in real-time

## Benefits of This Approach

### ✅ Solves Original Problems:
- **Participant Visibility**: Everyone is in the same Google Meet session
- **Real Video/Audio**: Uses Google Meet's native capabilities
- **No Black Screens**: Actual video streams, not mock interfaces

### ✅ Additional Benefits:
- **Security**: Leverages Google Meet's security model
- **Reliability**: Built on Google's infrastructure
- **Integration**: Seamless experience within Google Meet
- **Scalability**: Handles any number of participants
- **Mobile Support**: Works on all devices Google Meet supports

## Next Steps

1. **Set Environment Variables**: Configure project number and base URL
2. **Deploy Add-on Pages**: Ensure `/meet-addon/*` routes are accessible
3. **Configure Google Cloud**: Set up add-on deployment in GCP console
4. **Test Integration**: Create meeting and test add-on functionality
5. **Add Features**: Implement specific collaborative features you need

## Testing Locally

1. Start your Next.js app: `npm run dev`
2. Visit `http://localhost:3000/meet-addon/sidepanel`
3. Visit `http://localhost:3000/meet-addon/mainstage`

### Expected Local Development Behavior

**⚠️ "Missing required Meet SDK URL parameter: meet_sdk" Error**

This error is **EXPECTED** when testing locally and indicates the components are working correctly:

- The `meet_sdk` parameter is only provided when your add-on runs inside Google Meet
- Google Meet automatically appends this parameter to the iframe URL
- Our components detect this and show a development mode interface
- This is documented behavior in Google's official documentation

**What you'll see locally:**
- Development mode interface with helpful instructions
- Demo functionality to preview the user experience
- Clear indicators that you're in development mode
- No actual Google Meet SDK functionality (requires deployment)

**To test full functionality:**
- Deploy to an HTTPS domain
- Configure the add-on in Google Cloud Console
- Test within an actual Google Meet session

## Important Notes

- **HTTPS Required**: Google Meet add-ons only work over HTTPS in production
- **Domain Verification**: Your domain must be verified in Google Cloud Console
- **Project Number**: Use the numeric project number, not project ID
- **SDK Loading**: The Google Meet Add-ons SDK must load before your components initialize
- **Session Management**: Each page (side panel and main stage) needs its own session

This implementation provides a professional, scalable solution that keeps users within Google Meet while adding powerful collaborative features through your add-on.
