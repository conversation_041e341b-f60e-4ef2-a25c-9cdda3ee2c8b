// Centralized Cache System - Main Export File
// Exports all cache-related functionality for easy importing

// Core cache service
export { CacheService, cacheService } from './CacheService'

// React Context and hooks
export { 
  UnifiedCacheProvider, 
  useUnifiedCache, 
  useEmailCache 
} from './UnifiedCacheContext'

// All types and interfaces
export type {
  // Base types
  BaseCacheEntry,
  CacheConfig,
  CacheStats,
  CacheKey,
  CacheNamespace,
  CacheOperation,
  CacheQuery,
  CacheInvalidation,
  
  // Email types
  CachedEmail,
  CachedEmailList,
  EmailCacheData,
  
  // Calendar types
  CachedCalendarEvent,
  CachedCalendarList,
  CalendarCacheData,
  
  // Meet types
  CachedMeetingSpace,
  CachedConferenceRecord,
  CachedParticipant,
  MeetCacheData,
  
  // AI types
  CachedConversation,
  CachedMessage,
  CachedUserBehavior,
  CachedKnowledgeEntry,
  AICacheData,
  
  // Unified types
  UnifiedCacheData
} from './types'

// Utility functions for cache management
export const CacheUtils = {
  /**
   * Generate a cache key with timestamp
   */
  generateTimestampKey: (base: string, timestamp?: number): string => {
    const ts = timestamp || Date.now()
    return `${base}_${ts}`
  },

  /**
   * Generate a date-based cache key
   */
  generateDateKey: (base: string, date: Date): string => {
    const dateStr = date.toISOString().split('T')[0] // YYYY-MM-DD
    return `${base}_${dateStr}`
  },

  /**
   * Generate a time range cache key
   */
  generateTimeRangeKey: (base: string, startDate: Date, endDate: Date): string => {
    const start = startDate.toISOString().split('T')[0]
    const end = endDate.toISOString().split('T')[0]
    return `${base}_${start}_to_${end}`
  },

  /**
   * Generate a user-specific cache key
   */
  generateUserKey: (base: string, userId: string): string => {
    return `${base}_user_${userId}`
  },

  /**
   * Parse a cache key to extract components
   */
  parseKey: (key: string): { base: string; components: string[] } => {
    const parts = key.split('_')
    return {
      base: parts[0],
      components: parts.slice(1)
    }
  },

  /**
   * Check if a cache key matches a pattern
   */
  matchesPattern: (key: string, pattern: string): boolean => {
    // Simple pattern matching with wildcards
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    return regex.test(key)
  },

  /**
   * Get cache key for email list with time range
   */
  getEmailListKey: (type: string, days?: number): string => {
    return days ? `${type}_${days}days` : type
  },

  /**
   * Get cache key for calendar events with time range
   */
  getCalendarEventsKey: (timeRange: string): string => {
    return `events_${timeRange}`
  },

  /**
   * Get cache key for meeting data
   */
  getMeetingKey: (type: string, id: string): string => {
    return `${type}_${id}`
  },

  /**
   * Get cache key for AI conversation
   */
  getConversationKey: (conversationId: string): string => {
    return `conversation_${conversationId}`
  }
}

// Cache configuration presets
export const CachePresets = {
  /**
   * Optimized configuration for email caching - reduced TTL for real-time updates
   */
  EMAIL: {
    defaultTTL: 2 * 60 * 1000, // 2 minutes for real-time email updates
    maxSize: 750, // Increased for better performance
    enablePersistence: false
  },

  /**
   * Optimized configuration for calendar caching - increased TTL for infrequent changes
   */
  CALENDAR: {
    defaultTTL: 18 * 60 * 1000, // 18 minutes - calendar data changes less frequently
    maxSize: 300, // Increased for better performance
    enablePersistence: false
  },

  /**
   * Optimized configuration for meeting caching
   */
  MEET: {
    defaultTTL: 12 * 60 * 1000, // 12 minutes - balanced for meeting data
    maxSize: 200, // Increased for better performance
    enablePersistence: false
  },

  /**
   * Default configuration for AI data caching
   */
  AI: {
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    maxSize: 400, // Increased for better performance
    enablePersistence: true // AI data might benefit from persistence
  },

  /**
   * High-performance configuration for frequently accessed data
   */
  HIGH_PERFORMANCE: {
    defaultTTL: 2 * 60 * 1000, // 2 minutes
    maxSize: 1000,
    enablePersistence: false
  },

  /**
   * Long-term configuration for rarely changing data
   */
  LONG_TERM: {
    defaultTTL: 60 * 60 * 1000, // 1 hour
    maxSize: 100,
    enablePersistence: true
  }
}

// Cache monitoring and debugging utilities
export const CacheDebug = {
  /**
   * Log cache statistics
   */
  logStats: (stats: CacheStats): void => {
    console.group('📊 Cache Statistics')
    console.log('Total Entries:', stats.totalEntries)
    console.log('Memory Usage:', `${(stats.memoryUsage / 1024 / 1024).toFixed(2)} MB`)
    console.log('Hit Rate:', stats.hitRate)
    console.log('Miss Rate:', stats.missRate)
    console.log('Last Cleanup:', new Date(stats.lastCleanup).toLocaleString())
    console.groupEnd()
  },

  /**
   * Log namespace sizes
   */
  logNamespaceSizes: (cache: any): void => {
    console.group('📦 Cache Namespace Sizes')
    const namespaces: CacheNamespace[] = ['email', 'calendar', 'meet', 'ai']
    namespaces.forEach(namespace => {
      const size = cache.getNamespaceSize(namespace)
      console.log(`${namespace}:`, size, 'entries')
    })
    console.groupEnd()
  },

  /**
   * Enable debug logging for cache operations
   */
  enableDebugLogging: (): void => {
    console.log('🔍 Cache debug logging enabled')
    // This would enable more verbose logging in the cache service
  },

  /**
   * Disable debug logging for cache operations
   */
  disableDebugLogging: (): void => {
    console.log('🔇 Cache debug logging disabled')
    // This would disable verbose logging in the cache service
  }
}

// Cache helpers for specific domains
export { CalendarCache } from '../../lib/cache/calendarCacheHelper'
export { MeetCache } from '../../lib/cache/meetCacheHelper'

// Export commonly used cache keys as constants
export const CacheKeys = {
  // Email cache keys
  EMAIL: {
    INBOX_7DAYS: 'inbox_7days',
    SENT_7DAYS: 'sent_7days',
    SPAM_7DAYS: 'spam_7days',
    TRASH_7DAYS: 'trash_7days',
    ARCHIVE_7DAYS: 'archive_7days',
    DRAFTS: 'drafts',
    CATEGORIES: 'categories'
  },

  // Calendar cache keys
  CALENDAR: {
    UPCOMING_WEEK: 'upcoming_week',
    TODAY: 'today',
    THIS_MONTH: 'this_month',
    FREE_BUSY: 'free_busy'
  },

  // Meet cache keys
  MEET: {
    ACTIVE_SPACES: 'active_spaces',
    RECENT_CONFERENCES: 'recent_conferences',
    UPCOMING_MEETINGS: 'upcoming_meetings'
  },

  // AI cache keys
  AI: {
    ACTIVE_CONVERSATIONS: 'active_conversations',
    USER_BEHAVIOR: 'user_behavior',
    KNOWLEDGE_BASE: 'knowledge_base',
    INSIGHTS: 'insights'
  }
}
