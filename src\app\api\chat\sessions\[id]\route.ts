import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { unifiedEncryption as chatEncryption } from '@/lib/unified-encryption'

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
 
    const session = await getServerSession(authOptions) as any
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user record to get the actual user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { id: sessionId } = await params

    // Get chat session
    const chatSession = await prisma.chatSession.findUnique({
      where: {
        id: sessionId,
        userId: user.id
      }
    })

    if (!chatSession) {
      return NextResponse.json({ error: 'Chat session not found' }, { status: 404 })
    }

    // Decrypt session
    const decryptedSession = await chatEncryption.decryptChatSession(chatSession as any)
    return NextResponse.json({ session: decryptedSession })
  } catch (error) {
    console.error('Error fetching chat session:', error)
    return NextResponse.json(
      { error: 'Failed to fetch chat session' },
      { status: 500 }
    )
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
 
    const session = await getServerSession(authOptions) as any
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { id: sessionId } = await params
    const updates = await req.json()

    // Get existing session
    const existingSession = await prisma.chatSession.findUnique({
      where: {
        id: sessionId,
        userId: user.id
      }
    })

    if (!existingSession) {
      return NextResponse.json({ error: 'Chat session not found' }, { status: 404 })
    }

    // Decrypt existing session
    const decryptedSession = await chatEncryption.decryptChatSession(existingSession as any)
    
    // Apply updates
    const updatedSession = {
      ...decryptedSession,
      ...updates,
      id: sessionId,
      userId: user.id,
      updatedAt: new Date()
    }

    // Re-encrypt updated session
    const encryptedSession = await chatEncryption.encryptChatSession(updatedSession)

    // Update in database
    const savedSession = await prisma.chatSession.update({
      where: { id: sessionId },
      data: {
        title: encryptedSession.title,
        messages: encryptedSession.messages,
        context: encryptedSession.context,
        isArchived: encryptedSession.isArchived,
        updatedAt: new Date()
      }
    })

    // Return decrypted version
    const responseSession = await chatEncryption.decryptChatSession(savedSession as any)
    return NextResponse.json({ session: responseSession })
  } catch (error) {
    console.error('Error updating chat session:', error)
    return NextResponse.json(
      { error: 'Failed to update chat session' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
 
    const session = await getServerSession(authOptions) as any
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { id: sessionId } = await params

    // Delete the session
    await prisma.chatSession.delete({
      where: {
        id: sessionId,
        userId: user.id
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting chat session:', error)
    return NextResponse.json(
      { error: 'Failed to delete chat session' },
      { status: 500 }
    )
  }
} 