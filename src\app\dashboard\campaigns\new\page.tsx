"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import {
  ArrowLeft,
  Send,
  Save,
  Users,
  Mail,
  Eye,
  Calendar,
  AlertCircle
} from "lucide-react"
import Link from "next/link"

interface ContactList {
  id: string
  name: string
  description?: string
  contactCount: number
}

export default function NewCampaignPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [contactLists, setContactLists] = useState<ContactList[]>([])
  const [previewMode, setPreviewMode] = useState(false)
  
  const [campaign, setCampaign] = useState({
    name: "",
    subject: "",
    content: "",
    selectedLists: [] as string[],
    sendNow: true,
    scheduledFor: ""
  })

  useEffect(() => {
    fetchContactLists()
  }, [])

  const fetchContactLists = async () => {
    try {
      const response = await fetch('/api/contact-lists')
      if (response.ok) {
        const data = await response.json()
        setContactLists(data)
      }
    } catch (error) {
      console.error('Error fetching contact lists:', error)
    }
  }

  const handleSubmit = async (asDraft = false) => {
    if (!campaign.name || !campaign.subject || !campaign.content) {
      alert('Please fill in all required fields')
      return
    }

    if (campaign.selectedLists.length === 0) {
      alert('Please select at least one contact list')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: campaign.name,
          subject: campaign.subject,
          content: campaign.content,
          contactListIds: campaign.selectedLists,
          status: asDraft ? 'DRAFT' : (campaign.sendNow ? 'SENDING' : 'SCHEDULED'),
          scheduledFor: campaign.sendNow ? null : campaign.scheduledFor
        })
      })

      if (response.ok) {
        const newCampaign = await response.json()
        router.push(`/dashboard/campaigns/${newCampaign.id}`)
      } else {
        const error = await response.json()
        alert('Error creating campaign: ' + error.message)
      }
    } catch (error) {
      console.error('Error creating campaign:', error)
      alert('Error creating campaign')
    } finally {
      setLoading(false)
    }
  }

  const handleListToggle = (listId: string) => {
    setCampaign(prev => ({
      ...prev,
      selectedLists: prev.selectedLists.includes(listId)
        ? prev.selectedLists.filter(id => id !== listId)
        : [...prev.selectedLists, listId]
    }))
  }

  const getTotalRecipients = () => {
    return contactLists
      .filter(list => campaign.selectedLists.includes(list.id))
      .reduce((total, list) => total + list.contactCount, 0)
  }

  const defaultEmailTemplate = `<html>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
  <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
    <h1 style="color: #333; margin: 0;">Hello {{firstName}}!</h1>
  </div>
  
  <div style="line-height: 1.6; color: #555;">
    <p>Your email content goes here...</p>
    <p>You can use these personalization tags:</p>
    <ul>
      <li>{{firstName}} - Contact's first name</li>
      <li>{{lastName}} - Contact's last name</li>
      <li>{{email}} - Contact's email address</li>
    </ul>
  </div>
  
  <div style="text-align: center; margin: 30px 0;">
    <a href="#" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">
      Call to Action
    </a>
  </div>
  
  <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; color: #999; font-size: 12px;">
    <p>You received this email because you subscribed to our newsletter.</p>
    <p><a href="{{unsubscribeUrl}}" style="color: #999;">Unsubscribe</a></p>
  </div>
</body>
</html>`

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/campaigns">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Campaign</h1>
          <p className="text-muted-foreground">
            Design and send your email marketing campaign
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Campaign Details */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Details</CardTitle>
              <CardDescription>
                Basic information about your email campaign
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Campaign Name *</Label>
                <Input
                  id="name"
                  value={campaign.name}
                  onChange={(e) => setCampaign(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Newsletter - January 2024"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="subject">Email Subject *</Label>
                <Input
                  id="subject"
                  value={campaign.subject}
                  onChange={(e) => setCampaign(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="e.g., Exciting Updates and New Features!"
                />
              </div>
            </CardContent>
          </Card>

          {/* Email Content */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Email Content</CardTitle>
                  <CardDescription>
                    Design your email using HTML or plain text
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPreviewMode(!previewMode)}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  {previewMode ? "Edit" : "Preview"}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {!previewMode ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="content">HTML Content *</Label>
                    <Textarea
                      id="content"
                      value={campaign.content}
                      onChange={(e) => setCampaign(prev => ({ ...prev, content: e.target.value }))}
                      placeholder={defaultEmailTemplate}
                      className="min-h-[400px] font-mono text-sm"
                    />
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>Available personalization tags:</p>
                    <code>{"{{firstName}}, {{lastName}}, {{email}}, {{unsubscribeUrl}}"}</code>
                  </div>
                  {!campaign.content && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCampaign(prev => ({ ...prev, content: defaultEmailTemplate }))}
                    >
                      Load Template
                    </Button>
                  )}
                </div>
              ) : (
                <div className="border rounded-lg p-4 bg-white">
                  <div className="text-sm text-muted-foreground mb-2">Preview:</div>
                  <div 
                    className="prose max-w-none"
                    dangerouslySetInnerHTML={{ 
                      __html: campaign.content || "<p>No content to preview</p>" 
                    }}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contact Lists */}
          <Card>
            <CardHeader>
              <CardTitle>Select Recipients</CardTitle>
              <CardDescription>
                Choose which contact lists to send this campaign to
              </CardDescription>
            </CardHeader>
            <CardContent>
              {contactLists.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No contact lists</h3>
                  <p className="text-gray-500 mb-4">
                    Create contact lists first to send campaigns
                  </p>
                  <Link href="/dashboard/contacts">
                    <Button variant="outline">
                      <Users className="mr-2 h-4 w-4" />
                      Manage Contacts
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="space-y-3">
                  {contactLists.map((list) => (
                    <div key={list.id} className="flex items-center space-x-2 p-3 border rounded-lg">
                      <Checkbox
                        id={list.id}
                        checked={campaign.selectedLists.includes(list.id)}
                        onCheckedChange={() => handleListToggle(list.id)}
                      />
                      <div className="flex-1">
                        <Label htmlFor={list.id} className="font-medium cursor-pointer">
                          {list.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {list.contactCount} contacts
                          {list.description && ` • ${list.description}`}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Campaign Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Recipients:</span>
                  <span className="font-medium">{getTotalRecipients()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Lists selected:</span>
                  <span className="font-medium">{campaign.selectedLists.length}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Status:</span>
                  <span className="font-medium">
                    {campaign.sendNow ? "Send immediately" : "Scheduled"}
                  </span>
                </div>
              </div>
              
              <Separator />
              
              {getTotalRecipients() > 0 && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <Mail className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-900">Ready to send</p>
                      <p className="text-blue-700">
                        Your campaign will be sent to {getTotalRecipients()} recipients
                      </p>
                    </div>
                  </div>
                </div>
              )}
              
              {campaign.selectedLists.length === 0 && (
                <div className="bg-orange-50 p-3 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-4 w-4 text-orange-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-orange-900">No recipients</p>
                      <p className="text-orange-700">
                        Select contact lists to send your campaign
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Send Options */}
          <Card>
            <CardHeader>
              <CardTitle>Send Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sendNow"
                    checked={campaign.sendNow}
                    onCheckedChange={(checked) => setCampaign(prev => ({ 
                      ...prev, 
                      sendNow: Boolean(checked) 
                    }))}
                  />
                  <Label htmlFor="sendNow">Send immediately</Label>
                </div>
                
                {!campaign.sendNow && (
                  <div className="space-y-2 ml-6">
                    <Label htmlFor="scheduledFor">Schedule for:</Label>
                    <Input
                      id="scheduledFor"
                      type="datetime-local"
                      value={campaign.scheduledFor}
                      onChange={(e) => setCampaign(prev => ({ 
                        ...prev, 
                        scheduledFor: e.target.value 
                      }))}
                      min={new Date().toISOString().slice(0, 16)}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              onClick={() => handleSubmit(false)}
              disabled={loading || !campaign.name || !campaign.subject || !campaign.content || campaign.selectedLists.length === 0}
              className="w-full"
            >
              {loading ? (
                "Creating..."
              ) : campaign.sendNow ? (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Campaign
                </>
              ) : (
                <>
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule Campaign
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleSubmit(true)}
              disabled={loading || !campaign.name || !campaign.subject || !campaign.content}
              className="w-full"
            >
              <Save className="mr-2 h-4 w-4" />
              Save as Draft
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 