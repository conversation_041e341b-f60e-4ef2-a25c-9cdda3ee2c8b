import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.redirect(new URL('/auth/signin', request.url))
    }

    // Since Meet tokens are now handled in the main OAuth flow,
    // we need to redirect to re-authenticate with the updated scopes
    return NextResponse.redirect(new URL('/auth/signin?callbackUrl=/dashboard/meet&reauth=true', request.url))

  } catch (error) {
    console.error('Meet connect error:', error)
    return NextResponse.redirect(new URL('/dashboard/meet?error=connect_failed', request.url))
  }
}
