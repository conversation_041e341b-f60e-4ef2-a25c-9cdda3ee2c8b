"use client"

import { useState, useEffect } from "react"
import { useEmailCache } from "@/contexts/cache"
import <PERSON>ailViewer from "./EmailViewer"
import EmailThreadViewer from "./EmailThreadViewer"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Paperclip,
  Send,
  RefreshCw,
  Plus,
  Mail,
  Zap,
  X,
  Minimize2,
  Maximize,
  Save,
  Clock
} from "lucide-react"
import {
  EmailDetail,
  EmailContentWrapperProps,
  GmailLabel,
  ComposerType
} from './types'
import { useDraftAutoSave } from "@/hooks/useDraftAutoSave"
import { useBeforeUnloadProtection } from "@/hooks/useBeforeUnloadProtection"

export function EmailContent({ emailId, onEmailUpdate }: EmailContentWrapperProps) {
  const [email, setEmail] = useState<EmailDetail | null>(null)
  const [loading, setLoading] = useState(false)
  const [showLabelDialog, setShowLabelDialog] = useState(false)
  const [newLabelName, setNewLabelName] = useState("")
  const [labels, setLabels] = useState<GmailLabel[]>([])
  const [actionLoading, setActionLoading] = useState(false)
  const [threadInfo, setThreadInfo] = useState<{ threadId: string; messageCount: number } | null>(null)
  const [showThreadView, setShowThreadView] = useState(false)

  // Get email cache context
  const { 
    getCachedEmailDetail, 
    setCachedEmailDetail, 
    isCacheValid,
    updateCachedEmail
  } = useEmailCache()
  
  // New state for inline composer
  const [showComposer, setShowComposer] = useState(false)
  const [composerType, setComposerType] = useState<'reply' | 'replyAll' | 'forward'>('reply')
  const [composerSubject, setComposerSubject] = useState("")
  const [composerTo, setComposerTo] = useState("")
  const [composerCc, setComposerCc] = useState("")
  const [composerBcc, setComposerBcc] = useState("")
  const [replyContent, setReplyContent] = useState("")
  const [isMinimized, setIsMinimized] = useState(false)

  // Draft auto-save for reply composer
  const replyDraftData = {
    to: composerTo,
    cc: composerCc,
    bcc: composerBcc,
    subject: composerSubject,
    htmlBody: replyContent,
    textBody: replyContent.replace(/<[^>]*>/g, ''),
    threadId: email?.threadId,
    replyToMessageId: email?.id,
    references: email?.references,
    inReplyTo: email?.messageId
  }

  const {
    draftId: replyDraftId,
    isSaving: replyIsSaving,
    lastSaved: replyLastSaved,
    hasUnsavedChanges: replyHasUnsavedChanges,
    saveDraft: saveReplyDraft,
    deleteDraft: deleteReplyDraft,
    sendDraft: sendReplyDraft,
    error: replyDraftError
  } = useDraftAutoSave(replyDraftData, {
    enabled: showComposer && !isMinimized,
    debounceMs: 3000,
    onSaveSuccess: (id) => {
      console.log('Reply draft saved successfully:', id)
    },
    onSaveError: (error) => {
      console.error('Reply draft save error:', error)
    }
  })

  // Beforeunload protection for reply drafts
  useBeforeUnloadProtection(replyHasUnsavedChanges && showComposer, {
    enabled: showComposer && !isMinimized,
    message: 'You have unsaved changes in your reply draft. Are you sure you want to leave?',
    onBeforeUnload: () => {
      // Try to save reply draft before leaving
      if (replyHasUnsavedChanges && showComposer) {
        saveReplyDraft()
      }
    }
  })

  // Add a state to track refresh trigger
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  useEffect(() => {
    if (emailId) {
      // Check if we have valid cached data for this email
      const cachedEmail = getCachedEmailDetail(emailId)
      const isValid = isCacheValid('emailDetail', emailId)
      
      if (cachedEmail && isValid && refreshTrigger === 0) {
        // Use cached email data - convert CachedEmail to EmailDetail
        const emailDetail: EmailDetail = {
          id: cachedEmail.id,
          threadId: cachedEmail.threadId,
          from: cachedEmail.from,
          to: '', // CachedEmail doesn't have 'to' field, use empty string as default
          subject: cachedEmail.subject,
          date: cachedEmail.date,
          snippet: cachedEmail.snippet,
          isRead: cachedEmail.isRead,
          isStarred: cachedEmail.isStarred,
          hasAttachments: cachedEmail.hasAttachments,
          body: cachedEmail.body || '', // Ensure body is never undefined
          labels: cachedEmail.labels?.map(label => ({
            ...label,
            type: (label.type === 'system' || label.type === 'user') ? label.type : 'user' as 'system' | 'user'
          })),
          attachments: cachedEmail.attachments,
          cc: undefined, // CachedEmail doesn't have these fields
          bcc: undefined,
          replyTo: undefined
        }
        setEmail(emailDetail)
        setLoading(false)

        // Mark as read if not already read
        if (!emailDetail.isRead) {
          markEmailAsRead()
        }
      } else {
        // Fetch fresh data
        fetchEmail()
      }
      
      fetchLabels()
    } else {
      setEmail(null)
    }
  }, [emailId, refreshTrigger])

  // Listen for email updates from parent component
  useEffect(() => {
    const handleEmailUpdate = (event: any) => {
      const { emailId: updatedEmailId } = event.detail || {}
      
      // If this is the email being displayed, force refresh
      if (updatedEmailId === emailId) {
        setRefreshTrigger(prev => prev + 1)
      }
      
      // Update cache with read state change
      if (email && updatedEmailId === emailId && emailId) {
        updateCachedEmail(emailId, { isRead: true })
      }
    }

    // Listen for the custom event that might be triggered by parent
    window.addEventListener('emailUpdated', handleEmailUpdate)
    
    return () => {
      window.removeEventListener('emailUpdated', handleEmailUpdate)
    }
  }, [emailId, email, updateCachedEmail])

  const fetchEmail = async () => {
    if (!emailId) return
    
    setLoading(true)
    try {
      const response = await fetch(`/api/gmail/inbox/${emailId}`)
      if (response.ok) {
        const responseData = await response.json()
        const emailData = responseData.email || responseData
        
        // Convert labels to proper type and ensure date is Date object
        const processedEmailData: EmailDetail = {
          ...emailData,
          date: typeof emailData.date === 'string' ? new Date(emailData.date) : emailData.date,
          labels: emailData.labels?.map((label: any) => ({
            ...label,
            type: (label.type === 'system' || label.type === 'user') ? label.type : 'user' as 'system' | 'user'
          })),
          body: emailData.body || '', // Ensure body is always a string
        }
        
        setEmail(processedEmailData)
        
        // Cache the email detail
        setCachedEmailDetail(emailId, {
          id: processedEmailData.id,
          threadId: processedEmailData.threadId,
          from: processedEmailData.from,
          subject: processedEmailData.subject,
          date: typeof processedEmailData.date === 'string' ? new Date(processedEmailData.date) : processedEmailData.date,
          snippet: processedEmailData.snippet,
          isRead: processedEmailData.isRead,
          isStarred: processedEmailData.isStarred,
          hasAttachments: processedEmailData.hasAttachments,
          body: processedEmailData.body,
          labels: processedEmailData.labels,
          attachments: processedEmailData.attachments
        })
        
        // Mark as read if not already read
        if (!processedEmailData.isRead) {
          markEmailAsRead()
        }

        // Check if this email is part of a multi-message thread
        if (processedEmailData.threadId) {
          try {
            const threadResponse = await fetch(`/api/gmail/threads/${processedEmailData.threadId}`)
            if (threadResponse.ok) {
              const threadData = await threadResponse.json()
              setThreadInfo({
                threadId: processedEmailData.threadId,
                messageCount: threadData.messageCount
              })
              // Show thread view if there are multiple messages
              setShowThreadView(threadData.messageCount > 1)
            }
          } catch (threadError) {
            console.error('Error fetching thread info:', threadError)
            // Fall back to single email view
            setShowThreadView(false)
            setThreadInfo(null)
          }
        } else {
          setShowThreadView(false)
          setThreadInfo(null)
        }
      } else {
        console.error('Failed to fetch email')
        setShowThreadView(false)
        setThreadInfo(null)
      }
    } catch (error) {
      console.error('Error fetching email:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchLabels = async () => {
    try {
      const response = await fetch('/api/gmail/labels')
      if (response.ok) {
        const data = await response.json()
        setLabels(data.labels || [])
      }
    } catch (error) {
      console.error('Error fetching labels:', error)
    }
  }

  const markEmailAsRead = async () => {
    if (!emailId || !email || email.isRead) return
    
    try {
      const response = await fetch('/api/gmail/inbox/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messageIds: [emailId],
          operation: 'markAsRead'
        })
      })
      
      if (response.ok) {
        // Update local state
        setEmail(prev => prev ? { ...prev, isRead: true } : null)
        
        // Update cache
        updateCachedEmail(emailId, { isRead: true })
        
        // Notify parent component
        onEmailUpdate?.()
      }
    } catch (error) {
      console.error('Error marking email as read:', error)
    }
  }

  const handleEmailAction = async (action: string, value?: any) => {
    if (!email || actionLoading) return
    
    setActionLoading(true)
    try {
      switch (action) {
        case 'star':
          await toggleStar()
          break
        case 'archive':
          await archiveEmail()
          break
        case 'trash':
          await deleteEmail()
          break
        case 'markUnread':
          await markAsUnread()
          break
        case 'addLabel':
          await addLabel(value)
          break
        case 'removeLabel':
          await removeLabel(value)
          break
        default:
          console.log('Unknown action:', action)
      }
    } catch (error) {
      console.error('Error handling email action:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const toggleStar = async () => {
    if (!email) return
    
    try {
      const response = await fetch('/api/gmail/inbox/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messageIds: [email.id],
          operation: email.isStarred ? 'removeStar' : 'addStar'
        })
      })
      
      if (response.ok) {
        const newStarred = !email.isStarred
        setEmail(prev => prev ? { ...prev, isStarred: newStarred } : null)
        updateCachedEmail(email.id, { isStarred: newStarred })
        // Call parent update to sync with EmailPageLayout
        onEmailUpdate?.()
      }
    } catch (error) {
      console.error('Error toggling star:', error)
    }
  }

  const archiveEmail = async () => {
    if (!email) return
    
    try {
      const response = await fetch('/api/gmail/inbox/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messageIds: [email.id],
          operation: 'archive'
        })
      })
      
      if (response.ok) {
        onEmailUpdate?.()
      }
    } catch (error) {
      console.error('Error archiving email:', error)
    }
  }

  const deleteEmail = async () => {
    if (!email) return
    
    try {
      const response = await fetch('/api/gmail/inbox/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messageIds: [email.id],
          operation: 'trash'
        })
      })
      
      if (response.ok) {
        onEmailUpdate?.()
      }
    } catch (error) {
      console.error('Error deleting email:', error)
    }
  }

  const markAsUnread = async () => {
    if (!email) return
    
    try {
      const response = await fetch('/api/gmail/inbox/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messageIds: [email.id],
          operation: 'markAsUnread'
        })
      })
      
      if (response.ok) {
        setEmail(prev => prev ? { ...prev, isRead: false } : null)
        updateCachedEmail(email.id, { isRead: false })
        onEmailUpdate?.()
      }
    } catch (error) {
      console.error('Error marking email as unread:', error)
    }
  }

  const addLabel = async (labelId: string) => {
    if (!email) return
    
    try {
      const response = await fetch('/api/gmail/inbox/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messageIds: [email.id],
          operation: 'addLabel',
          labelId
        })
      })
      
      if (response.ok) {
        onEmailUpdate?.()
        fetchEmail() // Refresh to get updated labels
      }
    } catch (error) {
      console.error('Error adding label:', error)
    }
  }

  const removeLabel = async (labelId: string) => {
    if (!email) return
    
    try {
      const response = await fetch('/api/gmail/inbox/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          messageIds: [email.id],
          operation: 'removeLabel',
          labelId
        })
      })
      
      if (response.ok) {
        onEmailUpdate?.()
        fetchEmail() // Refresh to get updated labels
      }
    } catch (error) {
      console.error('Error removing label:', error)
    }
  }

  const handleReplyClick = (type: 'reply' | 'replyAll' | 'forward') => {
    if (!email) return

    setComposerType(type)
    setShowComposer(true)
    setIsMinimized(false)

    // Set initial values based on reply type
    if (type === 'reply') {
      setComposerTo(extractSenderEmail(email.from))
      setComposerSubject(email.subject.startsWith('Re:') ? email.subject : `Re: ${email.subject}`)
    } else if (type === 'replyAll') {
      const toEmails = [extractSenderEmail(email.from)]
      if (email.to) toEmails.push(...email.to.split(',').map(e => e.trim()))
      setComposerTo(toEmails.join(', '))
      setComposerCc(email.cc || '')
      setComposerSubject(email.subject.startsWith('Re:') ? email.subject : `Re: ${email.subject}`)
    } else if (type === 'forward') {
      setComposerTo('')
      setComposerSubject(email.subject.startsWith('Fwd:') ? email.subject : `Fwd: ${email.subject}`)
    }
  }

  const handleThreadReplyClick = async (type: 'reply' | 'replyAll' | 'forward', messageId: string) => {
    if (!threadInfo) return

    try {
      // Fetch the specific message being replied to from the thread
      const response = await fetch(`/api/gmail/inbox/${messageId}`)
      if (response.ok) {
        const { email: specificMessage } = await response.json()

        setComposerType(type)
        setShowComposer(true)
        setIsMinimized(false)

        // Set initial values based on reply type using the specific message
        if (type === 'reply') {
          setComposerTo(extractSenderEmail(specificMessage.from))
          setComposerSubject(specificMessage.subject.startsWith('Re:') ? specificMessage.subject : `Re: ${specificMessage.subject}`)
        } else if (type === 'replyAll') {
          const toEmails = [extractSenderEmail(specificMessage.from)]
          if (specificMessage.to) toEmails.push(...specificMessage.to.split(',').map((e: string) => e.trim()))
          setComposerTo(toEmails.join(', '))
          setComposerCc(specificMessage.cc || '')
          setComposerSubject(specificMessage.subject.startsWith('Re:') ? specificMessage.subject : `Re: ${specificMessage.subject}`)
        } else if (type === 'forward') {
          setComposerTo('')
          setComposerSubject(specificMessage.subject.startsWith('Fwd:') ? specificMessage.subject : `Fwd: ${specificMessage.subject}`)
        }

        // Store the specific message for reply context
        setEmail(specificMessage)
      }
    } catch (error) {
      console.error('Error fetching message for reply:', error)
      // Fall back to regular reply handling
      handleReplyClick(type)
    }
  }

  const handleSendReply = async () => {
    if (!email || !replyContent.trim() || actionLoading) return

    setActionLoading(true)
    try {
      // If we have a reply draft, send it directly
      if (replyDraftId) {
        await sendReplyDraft()

        // Clear composer after successful send
        setShowComposer(false)
        setReplyContent("")
        setComposerSubject("")
        setComposerTo("")
        setComposerCc("")
        setComposerBcc("")

        // Refresh email to show the sent reply
        setRefreshTrigger(prev => prev + 1)
        return
      }
      const response = await fetch('/api/gmail/reply', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: composerTo,
          subject: composerSubject,
          htmlBody: replyContent.replace(/\n/g, '<br>'),
          textBody: replyContent,
          threadId: email.threadId,
          replyToMessageId: email.id,
          messageId: email.messageId,
          references: email.references,
          inReplyTo: email.inReplyTo,
          cc: composerCc || undefined,
          bcc: composerBcc || undefined
        })
      })
      
      if (response.ok) {
        setShowComposer(false)
        setReplyContent('')
        setComposerTo('')
        setComposerCc('')
        setComposerBcc('')
        setComposerSubject('')
        onEmailUpdate?.()
      } else {
        const error = await response.json()
        console.error('Error sending reply:', error)
      }
    } catch (error) {
      console.error('Error sending reply:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const createLabel = async () => {
    if (!newLabelName.trim() || actionLoading) return
    
    setActionLoading(true)
    try {
      const response = await fetch('/api/gmail/labels', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newLabelName })
      })
      
      if (response.ok) {
        setNewLabelName('')
        setShowLabelDialog(false)
        fetchLabels()
      }
    } catch (error) {
      console.error('Error creating label:', error)
    } finally {
      setActionLoading(false)
    }
  }

  const extractSenderName = (fromHeader: string | undefined | null) => {
    if (!fromHeader) return 'Unknown'
    const match = fromHeader.match(/^(.+?)\s*<.*>$/)
    return match ? match[1].trim().replace(/"/g, '') : fromHeader
  }

  const extractSenderEmail = (fromHeader: string | undefined | null) => {
    if (!fromHeader) return ''
    const match = fromHeader.match(/<(.+)>$/)
    return match ? match[1] : fromHeader
  }

  const formatDate = (date: Date | string) => {
    const d = new Date(date)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (d.toDateString() === today.toDateString()) {
      return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (d.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return d.toLocaleDateString()
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatEmailAddresses = (addresses: string) => {
    if (!addresses) return ''
    
    return addresses.split(',').map(addr => {
      const trimmed = addr.trim()
      const match = trimmed.match(/^(.+?)\s*<.*>$/)
      if (match) {
        return match[1].replace(/"/g, '')
      }
      return trimmed
    }).join(', ')
  }

  if (!emailId) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white">
        <div className="text-center mb-8">
          <div className="w-16 h-16 mx-auto mb-6 bg-gray-100 rounded-lg flex items-center justify-center">
            <Mail className="w-8 h-8 text-gray-400" />
          </div>
          <h2 className="text-xl font-medium text-gray-900 mb-2">It's empty here</h2>
          <p className="text-gray-500">Choose an email to view details</p>
        </div>
        
        <div className="flex gap-3">
          <Button variant="outline" className="gap-2">
            <Zap className="w-4 h-4" />
            Zero chat
          </Button>
          <Button className="gap-2 bg-gray-900 hover:bg-gray-800">
            <Send className="w-4 h-4" />
            Send email
          </Button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center bg-white">
        <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
      </div>
    )
  }

  if (!email) {
    return (
      <div className="h-full flex items-center justify-center bg-white">
        <div className="text-center">
          <Mail className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-gray-500">Unable to load email content</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white relative overflow-hidden">
      {/* Email Content Container - Use ThreadViewer or EmailViewer */}
      <div className="flex-1 flex flex-col min-h-0 overflow-hidden">
        <div className="flex-1 overflow-hidden">
          {showThreadView && threadInfo ? (
            <EmailThreadViewer
              threadId={threadInfo.threadId}
              initialEmail={email}
              onAction={(action, messageId) => {
                if (action === 'reply') handleThreadReplyClick('reply', messageId)
                else if (action === 'replyAll') handleThreadReplyClick('replyAll', messageId)
                else if (action === 'forward') handleThreadReplyClick('forward', messageId)
                else handleEmailAction(action, messageId)
              }}
              showActions={true}
              className="h-full"
            />
          ) : (
            <EmailViewer
              email={email}
              onAction={(action, emailId) => {
                if (action === 'reply') handleReplyClick('reply')
                else if (action === 'replyAll') handleReplyClick('replyAll')
                else if (action === 'forward') handleReplyClick('forward')
                else handleEmailAction(action, emailId)
              }}
              showActions={true}
              className="h-full"
            />
          )}
        </div>
      </div>

      {/* Enhanced Inline Composer */}
      {showComposer && (
        <div className={`border-t-2 border-blue-200 bg-white shadow-lg ${isMinimized ? 'h-12' : 'h-auto'} transition-all duration-300 ease-in-out flex-shrink-0`}>
          {isMinimized ? (
            <div className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50" onClick={() => setIsMinimized(false)}>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">
                  {composerType === 'reply' ? 'Reply' : composerType === 'replyAll' ? 'Reply all' : 'Forward'} - {composerSubject}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); setIsMinimized(false) }} className="hover:bg-gray-200 h-8 w-8 p-0">
                  <Maximize className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); setShowComposer(false) }} className="hover:bg-red-100 hover:text-red-600 h-8 w-8 p-0">
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gradient-to-b from-white to-gray-50">
              {/* Composer Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {composerType === 'reply' ? 'Reply' : composerType === 'replyAll' ? 'Reply all' : 'Forward'}
                  </h3>
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm" onClick={() => setIsMinimized(true)} className="hover:bg-gray-200 h-8 w-8 p-0">
                    <Minimize2 className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => setShowComposer(false)} className="hover:bg-red-100 hover:text-red-600 h-8 w-8 p-0">
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Composer Fields */}
              <div className="space-y-3 mb-4">
                <div className="grid grid-cols-12 items-center gap-3">
                  <label className="col-span-2 text-sm font-medium text-gray-700">To:</label>
                  <Input
                    value={composerTo}
                    onChange={(e) => setComposerTo(e.target.value)}
                    placeholder="Recipients"
                    className="col-span-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                
                <div className="grid grid-cols-12 items-center gap-3">
                  <label className="col-span-2 text-sm font-medium text-gray-700">Cc:</label>
                  <Input
                    value={composerCc}
                    onChange={(e) => setComposerCc(e.target.value)}
                    placeholder="Cc (optional)"
                    className="col-span-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>

                <div className="grid grid-cols-12 items-center gap-3">
                  <label className="col-span-2 text-sm font-medium text-gray-700">Subject:</label>
                  <Input
                    value={composerSubject}
                    onChange={(e) => setComposerSubject(e.target.value)}
                    placeholder="Subject"
                    className="col-span-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Composer Content */}
              <div className="mb-4">
                <Textarea
                  placeholder="Type your message here..."
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  className="min-h-[150px] border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none rounded-lg"
                />
              </div>

              {/* Draft Status */}
              {(replyDraftId || replyIsSaving || replyLastSaved || replyHasUnsavedChanges) && (
                <div className="flex items-center justify-between text-sm text-muted-foreground bg-gray-50 p-2 rounded mb-3">
                  <div className="flex items-center space-x-2">
                    {replyIsSaving ? (
                      <>
                        <Clock className="h-3 w-3 animate-spin" />
                        <span>Saving draft...</span>
                      </>
                    ) : replyDraftId ? (
                      <>
                        <Save className="h-3 w-3 text-green-600" />
                        <span>Draft saved {replyLastSaved && `at ${replyLastSaved.toLocaleTimeString()}`}</span>
                      </>
                    ) : replyHasUnsavedChanges ? (
                      <>
                        <Clock className="h-3 w-3 text-orange-600" />
                        <span>Unsaved changes</span>
                      </>
                    ) : null}
                  </div>
                  {replyDraftId && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={deleteReplyDraft}
                      className="text-red-600 hover:text-red-700 h-6 text-xs"
                    >
                      Delete Draft
                    </Button>
                  )}
                </div>
              )}

              {/* Draft Error */}
              {replyDraftError && (
                <div className="text-sm text-red-600 bg-red-50 p-2 rounded mb-3">
                  Draft error: {replyDraftError}
                </div>
              )}

              {/* Composer Actions */}
              <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handleSendReply}
                    disabled={actionLoading || !replyContent.trim()}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-5 py-2 font-medium"
                  >
                    {actionLoading ? (
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Send className="w-4 h-4 mr-2" />
                    )}
                    {replyDraftId ? "Send Draft" : "Send"}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={saveReplyDraft}
                    disabled={replyIsSaving || !replyHasUnsavedChanges}
                    className="hover:bg-gray-200"
                  >
                    {replyIsSaving ? (
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="w-4 h-4 mr-2" />
                    )}
                    Save Draft
                  </Button>
                  <Button variant="ghost" size="sm" className="hover:bg-gray-200">
                    <Paperclip className="w-4 h-4 mr-2" />
                    Attach
                  </Button>
                </div>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span>⌘⏎ to send</span>
                  <span>Auto-save enabled</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Label Dialog */}
      <Dialog open={showLabelDialog} onOpenChange={setShowLabelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Label</DialogTitle>
            <DialogDescription>
              Enter a name for the new label
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              placeholder="Label name"
              value={newLabelName}
              onChange={(e) => setNewLabelName(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowLabelDialog(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={createLabel}
              disabled={actionLoading || !newLabelName.trim()}
            >
              {actionLoading ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Plus className="w-4 h-4 mr-2" />
              )}
              Create Label
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
