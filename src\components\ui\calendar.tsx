"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center h-12 mb-4",
        caption_label: "text-lg font-semibold text-slate-900 dark:text-slate-100",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium",
          "transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring",
          "disabled:pointer-events-none disabled:opacity-50",
          "h-9 w-9 bg-transparent hover:bg-slate-100 dark:hover:bg-slate-800",
          "text-slate-500 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100",
          "border border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600",
          "shadow-sm hover:shadow transition-all duration-200"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex mb-2",
        head_cell: cn(
          "text-slate-500 dark:text-slate-400 rounded-md w-11 h-11 font-medium text-sm",
          "flex items-center justify-center uppercase tracking-wide"
        ),
        row: "flex w-full mt-1",
        cell: cn(
          "relative h-11 w-11 text-center text-sm p-0 rounded-md",
          "hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors duration-150",
          "[&:has([aria-selected].day-range-end)]:rounded-r-md",
          "[&:has([aria-selected].day-outside)]:bg-blue-50 dark:[&:has([aria-selected].day-outside)]:bg-blue-950/30",
          "[&:has([aria-selected])]:bg-blue-50 dark:[&:has([aria-selected])]:bg-blue-950/30",
          "first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md",
          "focus-within:relative focus-within:z-20"
        ),
        day: cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium",
          "transition-all duration-150 h-11 w-11 p-0 font-normal aria-selected:opacity-100",
          "hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-1"
        ),
        day_selected: cn(
          "bg-blue-600 text-white font-semibold",
          "hover:bg-blue-700 hover:text-white focus:bg-blue-700 focus:text-white",
          "shadow-lg hover:shadow-xl transition-all duration-200"
        ),
        day_today: cn(
          "bg-blue-600 text-white font-bold",
          "ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-slate-950",
          "shadow-lg relative before:absolute before:inset-0 before:rounded-md before:bg-blue-600"
        ),
        day_outside: cn(
          "day-outside text-slate-400 dark:text-slate-600 opacity-50",
          "aria-selected:bg-blue-100/50 dark:aria-selected:bg-blue-950/30",
          "aria-selected:text-blue-600 dark:aria-selected:text-blue-400 aria-selected:opacity-70"
        ),
        day_disabled: "text-slate-300 dark:text-slate-700 opacity-30 cursor-not-allowed",
        day_range_middle: cn(
          "aria-selected:bg-blue-100 dark:aria-selected:bg-blue-950/30",
          "aria-selected:text-blue-700 dark:aria-selected:text-blue-300"
        ),
        day_range_start: cn(
          "aria-selected:bg-blue-600 aria-selected:text-white",
          "aria-selected:rounded-l-md aria-selected:font-semibold"
        ),
        day_range_end: cn(
          "aria-selected:bg-blue-600 aria-selected:text-white",
          "aria-selected:rounded-r-md aria-selected:font-semibold"
        ),
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        Chevron: ({ orientation, ...props }) => 
          orientation === "left" ? (
            <ChevronLeft className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          ),
      }}
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar } 