import { Gmail<PERSON>abel, BulkOperationResult } from './types'
import { 
  modifyGmailMessage, 
  executeGmailOperation, 
  executeGmailOperationSafe,
  handleGmailErrorSafe,
  formatErrorMessage
} from './utils'

export async function markEmailAsRead(userId: string, messageId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { removeLabelIds: ['UNREAD'] }, 
    'marking email as read'
  )
}

export async function markEmailAsUnread(userId: string, messageId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { addLabelIds: ['UNREAD'] }, 
    'marking email as unread'
  )
}

export async function deleteEmail(userId: string, messageId: string): Promise<boolean> {
  return executeGmailOperationSafe(
    userId,
    async (gmail) => {
      await gmail.users.messages.trash({
        userId: 'me',
        id: messageId
      })
      return true
    },
    'deleting email'
  ).then(result => result !== null)
}

export async function archiveEmail(userId: string, messageId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { removeLabelIds: ['INBOX'] }, 
    'archiving email'
  )
}

export async function starEmail(userId: string, messageId: string, star: boolean): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    star ? { addLabelIds: ['STARRED'] } : { removeLabelIds: ['STARRED'] }, 
    `${star ? 'starring' : 'unstarring'} email`
  )
}

export async function addEmailLabel(userId: string, messageId: string, labelId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { addLabelIds: [labelId] }, 
    'adding label to email'
  )
}

export async function removeEmailLabel(userId: string, messageId: string, labelId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { removeLabelIds: [labelId] }, 
    'removing label from email'
  )
}

export async function moveToInbox(userId: string, messageId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { 
      addLabelIds: ['INBOX'],
      removeLabelIds: ['SPAM', 'TRASH']
    }, 
    'moving email to inbox'
  )
}

export async function deletePermanently(userId: string, messageId: string): Promise<boolean> {
  return executeGmailOperationSafe(
    userId,
    async (gmail) => {
      await gmail.users.messages.delete({
        userId: 'me',
        id: messageId
      })
      return true
    },
    'deleting email permanently'
  ).then(result => result !== null)
}

export async function restoreEmail(userId: string, messageId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { removeLabelIds: ['TRASH'] }, 
    'restoring email from trash'
  )
}

export async function markAsNotSpam(userId: string, messageId: string): Promise<boolean> {
  return modifyGmailMessage(
    userId, 
    messageId, 
    { 
      removeLabelIds: ['SPAM'],
      addLabelIds: ['INBOX']
    }, 
    'marking email as not spam'
  )
}

export async function getGmailLabels(userId: string): Promise<GmailLabel[]> {
  const result = await executeGmailOperationSafe(
    userId,
    async (gmail) => {
      const response = await gmail.users.labels.list({
        userId: 'me'
      })
      
      return response.data.labels?.map((label: any) => ({
        id: label.id || '',
        name: label.name || '',
        type: label.type || 'user'
      })) || []
    },
    'fetching Gmail labels'
  )
  
  return result || []
}

export async function createGmailLabel(userId: string, name: string, color?: string): Promise<{id: string, name: string} | null> {
  return executeGmailOperationSafe(
    userId,
    async (gmail) => {
      const labelData: any = {
        name,
        labelListVisibility: 'labelShow',
        messageListVisibility: 'show'
      }
      
      if (color) {
        labelData.color = {
          backgroundColor: color,
          textColor: '#ffffff'
        }
      }
      
      const response = await gmail.users.labels.create({
        userId: 'me',
        requestBody: labelData
      })
      
      return {
        id: response.data.id || '',
        name: response.data.name || ''
      }
    },
    'creating Gmail label'
  )
}

export async function bulkDeletePermanently(userId: string, messageIds: string[]): Promise<BulkOperationResult> {
  let success = 0
  let failed = 0

  // Execute delete operations in batches for performance
  for (const messageId of messageIds) {
    try {
      const result = await deletePermanently(userId, messageId)
      if (result) {
        success++
      } else {
        failed++
      }
    } catch (error) {
      console.error(`Error permanently deleting message ${messageId}:`, formatErrorMessage(error))
      failed++
    }
  }

  return { success, failed }
}

export async function bulkEmailOperations(
  userId: string, 
  messageIds: string[], 
  operation: 'delete' | 'archive' | 'markAsRead' | 'markAsUnread' | 'addStar' | 'removeStar' | 'trash' | 'addLabel' | 'removeLabel' | 'moveToInbox' | 'deletePermanently' | 'notSpam',
  labelId?: string
): Promise<BulkOperationResult> {
  let success = 0
  let failed = 0

  // Create operation map for cleaner code
  const operationMap = {
    'delete': (messageId: string) => deleteEmail(userId, messageId),
    'trash': (messageId: string) => deleteEmail(userId, messageId),
    'archive': (messageId: string) => archiveEmail(userId, messageId),
    'markAsRead': (messageId: string) => markEmailAsRead(userId, messageId),
    'markAsUnread': (messageId: string) => markEmailAsUnread(userId, messageId),
    'addStar': (messageId: string) => starEmail(userId, messageId, true),
    'removeStar': (messageId: string) => starEmail(userId, messageId, false),
    'addLabel': (messageId: string) => labelId ? addEmailLabel(userId, messageId, labelId) : Promise.resolve(false),
    'removeLabel': (messageId: string) => labelId ? removeEmailLabel(userId, messageId, labelId) : Promise.resolve(false),
    'moveToInbox': (messageId: string) => moveToInbox(userId, messageId),
    'deletePermanently': (messageId: string) => deletePermanently(userId, messageId),
    'notSpam': (messageId: string) => markAsNotSpam(userId, messageId)
  }

  const operationFunction = operationMap[operation]
  if (!operationFunction) {
    console.error('Unknown operation:', operation)
    return { success: 0, failed: messageIds.length }
  }

  // Execute operations with consistent error handling
  for (const messageId of messageIds) {
    try {
      const result = await operationFunction(messageId)
      if (result) {
        success++
      } else {
        failed++
      }
    } catch (error) {
      console.error(`Error performing ${operation} on message ${messageId}:`, formatErrorMessage(error))
      failed++
    }
  }

  return { success, failed }
} 