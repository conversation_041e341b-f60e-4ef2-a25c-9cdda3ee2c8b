import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentTaskService } from '@/lib/ai-agent-tasks'
import { startPerformanceMetric, endPerformanceMetric } from '@/lib/performance/monitor'

export async function GET(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('ai-agent-tasks-list', { endpoint: '/api/ai-agent/tasks' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('ai-agent-tasks-list')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    
    const filter = {
      status: searchParams.get('status')?.split(',') || undefined,
      type: searchParams.get('type')?.split(',') || undefined,
      priority: searchParams.get('priority')?.split(',') || undefined,
      dueDateFrom: searchParams.get('dueDateFrom') ? new Date(searchParams.get('dueDateFrom')!) : undefined,
      dueDateTo: searchParams.get('dueDateTo') ? new Date(searchParams.get('dueDateTo')!) : undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const result = await aiAgentTaskService.getTasks(session.user.id, filter)

    // End performance monitoring
    const duration = endPerformanceMetric('ai-agent-tasks-list')
    if (duration && duration > 1000) {
      console.warn(`Slow AI tasks list: ${duration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('ai-agent-tasks-list')
    console.error('Get tasks error:', error)
    return NextResponse.json(
      { error: 'Failed to get tasks' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('ai-agent-tasks-create', { endpoint: '/api/ai-agent/tasks' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('ai-agent-tasks-create')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      title,
      description,
      type = 'task',
      priority = 'medium',
      dueDate,
      reminderDate,
      relatedEmails,
      relatedContacts,
      relatedMeetings,
      addToCalendar = false,
      calendarEventTitle,
      calendarEventDuration,
      conversationId
    } = await request.json()

    if (!title) {
      endPerformanceMetric('ai-agent-tasks-create')
      return NextResponse.json({
        error: 'Task title is required'
      }, { status: 400 })
    }

    const result = await aiAgentTaskService.createTask(session.user.id, {
      title,
      description,
      type,
      priority,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      reminderDate: reminderDate ? new Date(reminderDate) : undefined,
      relatedEmails,
      relatedContacts,
      relatedMeetings,
      addToCalendar,
      calendarEventTitle,
      calendarEventDuration
    }, conversationId)

    // End performance monitoring
    const duration = endPerformanceMetric('ai-agent-tasks-create')
    if (duration && duration > 2000) {
      console.warn(`Slow AI task creation: ${duration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('ai-agent-tasks-create')
    console.error('Create task error:', error)
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    )
  }
}
