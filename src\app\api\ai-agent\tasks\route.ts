import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentTaskService } from '@/lib/ai-agent-tasks'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    
    const filter = {
      status: searchParams.get('status')?.split(',') || undefined,
      type: searchParams.get('type')?.split(',') || undefined,
      priority: searchParams.get('priority')?.split(',') || undefined,
      dueDateFrom: searchParams.get('dueDateFrom') ? new Date(searchParams.get('dueDateFrom')!) : undefined,
      dueDateTo: searchParams.get('dueDateTo') ? new Date(searchParams.get('dueDateTo')!) : undefined,
      search: searchParams.get('search') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined
    }

    const result = await aiAgentTaskService.getTasks(session.user.id, filter)

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    console.error('Get tasks error:', error)
    return NextResponse.json(
      { error: 'Failed to get tasks' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      title,
      description,
      type = 'task',
      priority = 'medium',
      dueDate,
      reminderDate,
      relatedEmails,
      relatedContacts,
      relatedMeetings,
      addToCalendar = false,
      calendarEventTitle,
      calendarEventDuration,
      conversationId
    } = await request.json()

    if (!title) {
      return NextResponse.json({ 
        error: 'Task title is required' 
      }, { status: 400 })
    }

    const result = await aiAgentTaskService.createTask(session.user.id, {
      title,
      description,
      type,
      priority,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      reminderDate: reminderDate ? new Date(reminderDate) : undefined,
      relatedEmails,
      relatedContacts,
      relatedMeetings,
      addToCalendar,
      calendarEventTitle,
      calendarEventDuration
    }, conversationId)

    return NextResponse.json({
      ...result,
      success: true
    })

  } catch (error) {
    console.error('Create task error:', error)
    return NextResponse.json(
      { error: 'Failed to create task' },
      { status: 500 }
    )
  }
}
