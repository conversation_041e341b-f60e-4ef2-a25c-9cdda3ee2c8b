import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentCalendarService } from '@/lib/ai-agent-calendar'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')

    const insights = await aiAgentCalendarService.getCalendarInsights(session.user.id, days)

    return NextResponse.json({
      insights,
      success: true
    })

  } catch (error) {
    console.error('Calendar insights error:', error)
    return NextResponse.json(
      { error: 'Failed to get calendar insights' },
      { status: 500 }
    )
  }
}
