import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentCalendarService } from '@/lib/ai-agent-calendar'
import { startPerformanceMetric, endPerformanceMetric } from '@/lib/performance/monitor'

export async function GET(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('ai-agent-calendar-insights', { endpoint: '/api/ai-agent/calendar/insights' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('ai-agent-calendar-insights')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')

    const insights = await aiAgentCalendarService.getCalendarInsights(session.user.id, days)

    // End performance monitoring
    const duration = endPerformanceMetric('ai-agent-calendar-insights')
    if (duration && duration > 1500) {
      console.warn(`Slow AI calendar insights: ${duration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      insights,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('ai-agent-calendar-insights')
    console.error('Calendar insights error:', error)
    return NextResponse.json(
      { error: 'Failed to get calendar insights' },
      { status: 500 }
    )
  }
}
