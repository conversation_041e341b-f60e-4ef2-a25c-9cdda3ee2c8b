"use client"


import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  User, 
  Mail, 
  Calendar,
  Shield,
  Settings
} from "lucide-react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function ProfilePage() {
  const { data: session } = useSession()

  if (!session?.user) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium">Loading Profile...</h3>
            <p className="text-muted-foreground">Please wait while we load your profile information.</p>
          </div>
        </div>
      </div>
    )
  }

  const user = session.user

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Profile</h2>
          <p className="text-muted-foreground">
            View your account information and status.
          </p>
        </div>
        <Link href="/dashboard/settings">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Account Settings
          </Button>
        </Link>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Overview */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile Overview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={user.image || ""} alt={user.name || "User"} />
                <AvatarFallback className="text-lg">
                  {user.name?.split(' ').map(n => n[0]).join('') || "U"}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <h3 className="text-lg font-semibold">{user.name || "User"}</h3>
                <p className="text-sm text-muted-foreground">{user.email}</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
                <Badge variant="secondary">
                  Verified
                </Badge>
              </div>

              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Account Type</p>
                  <p className="text-sm text-muted-foreground">
                    Google Account
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Authentication</p>
                  <p className="text-sm text-muted-foreground">
                    OAuth 2.0 via Google
                  </p>
                </div>
                <Badge variant="default">
                  Secure
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>
              Your account details and platform access information.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Display Name</p>
                <p className="text-base">{user.name || "Not provided"}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">Email Address</p>
                <p className="text-base">{user.email}</p>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h4 className="text-sm font-medium">Platform Access</h4>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="text-sm font-medium">Gmail Integration</p>
                    <p className="text-xs text-muted-foreground">Send emails via Gmail API</p>
                  </div>
                  <Badge variant={session?.user?.email ? "default" : "secondary"}>
                    {session?.user?.email ? "Connected" : "Not Connected"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="text-sm font-medium">Email Tracking</p>
                    <p className="text-xs text-muted-foreground">Track email delivery status</p>
                  </div>
                  <Badge variant="default">
                    Enabled
                  </Badge>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h4 className="text-sm font-medium">Quick Actions</h4>
              <div className="flex flex-wrap gap-2">
                <Link href="/dashboard/compose">
                  <Button variant="outline" size="sm">
                    <Mail className="mr-2 h-4 w-4" />
                    Compose Email
                  </Button>
                </Link>
                <Link href="/dashboard/contacts">
                  <Button variant="outline" size="sm">
                    <User className="mr-2 h-4 w-4" />
                    Manage Contacts
                  </Button>
                </Link>
                <Link href="/dashboard/emails">
                  <Button variant="outline" size="sm">
                    <Calendar className="mr-2 h-4 w-4" />
                    Email History
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Coming Soon Features */}
      <Card className="opacity-60">
        <CardHeader>
          <CardTitle>Additional Profile Features</CardTitle>
          <CardDescription>
            Coming soon: Custom profile fields, team management, and advanced preferences.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg">
              <User className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm font-medium">Custom Fields</p>
              <p className="text-xs text-muted-foreground">Add custom profile information</p>
            </div>
            <div className="text-center p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg">
              <Shield className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm font-medium">Security Settings</p>
              <p className="text-xs text-muted-foreground">Two-factor authentication</p>
            </div>
            <div className="text-center p-4 border-2 border-dashed border-muted-foreground/25 rounded-lg">
              <Settings className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm font-medium">Preferences</p>
              <p className="text-xs text-muted-foreground">Customize your experience</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 