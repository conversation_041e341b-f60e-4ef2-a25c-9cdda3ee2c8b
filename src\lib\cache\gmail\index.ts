/**
 * Gmail Cache Module
 * Centralized exports for Gmail caching functionality
 */

// Export all Gmail cache functions
export {
  // Email list caching
  getCachedEmails,
  setCachedEmails,
  isEmailCacheValid,
  
  // Email detail caching
  getCachedEmailDetail,
  setCachedEmailDetail,
  updateCachedEmail,
  
  // Thread caching
  getCachedThread,
  setCachedThread,
  
  // Cache invalidation
  invalidateEmailCache,
  invalidateEmailDetail,
  invalidateThread,
  
  // Bulk operations
  cacheMultipleEmails,
  getMultipleCachedEmails,
  
  // Preloading
  preloadGmailData,
  
  // Statistics and cleanup
  getGmailCacheStats,
  cleanupGmailCache,
  
  // Constants
  GMAIL_CACHE_KEYS,
  GMAIL_CACHE_TTL
} from './gmailCacheHelper'

// Re-export types for convenience
export type {
  CachedEmail,
  CachedEmailList
} from '@/contexts/cache/types'
