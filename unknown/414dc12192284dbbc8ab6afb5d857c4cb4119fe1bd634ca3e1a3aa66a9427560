// ============================================================================
// API CALL OPTIMIZATION UTILITIES
// ============================================================================

interface PendingRequest {
  promise: Promise<any>
  timestamp: number
  resolve: (value: any) => void
  reject: (error: any) => void
}

interface BatchRequest {
  requests: Array<{
    id: string
    params: any
    resolve: (value: any) => void
    reject: (error: any) => void
  }>
  timer: NodeJS.Timeout
}

class ApiOptimizer {
  private pendingRequests: Map<string, PendingRequest> = new Map()
  private batchRequests: Map<string, BatchRequest> = new Map()
  private requestCache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map()

  /**
   * Deduplicate identical API calls that are made within a short time window
   */
  async deduplicateRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    windowMs: number = 1000
  ): Promise<T> {
    const existing = this.pendingRequests.get(key)
    
    if (existing && (Date.now() - existing.timestamp) < windowMs) {
      // Return the existing promise
      return existing.promise
    }

    // Create new request
    let resolve: (value: T) => void
    let reject: (error: any) => void

    const promise = new Promise<T>((res, rej) => {
      resolve = res
      reject = rej
    })

    const pendingRequest: PendingRequest = {
      promise,
      timestamp: Date.now(),
      resolve: resolve!,
      reject: reject!
    }

    this.pendingRequests.set(key, pendingRequest)

    try {
      const result = await requestFn()
      resolve!(result)
      return result
    } catch (error) {
      reject!(error)
      throw error
    } finally {
      this.pendingRequests.delete(key)
    }
  }

  /**
   * Batch multiple requests together to reduce API calls
   */
  batchRequest<T>(
    batchKey: string,
    requestId: string,
    params: any,
    batchFn: (requests: Array<{ id: string; params: any }>) => Promise<Record<string, T>>,
    batchWindowMs: number = 100
  ): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      let batch = this.batchRequests.get(batchKey)

      if (!batch) {
        batch = {
          requests: [],
          timer: setTimeout(async () => {
            const currentBatch = this.batchRequests.get(batchKey)
            if (!currentBatch) return

            this.batchRequests.delete(batchKey)

            try {
              const results = await batchFn(
                currentBatch.requests.map(req => ({ id: req.id, params: req.params }))
              )

              // Resolve individual requests
              currentBatch.requests.forEach(req => {
                if (results[req.id]) {
                  req.resolve(results[req.id])
                } else {
                  req.reject(new Error(`No result for request ${req.id}`))
                }
              })
            } catch (error) {
              // Reject all requests in the batch
              currentBatch.requests.forEach(req => req.reject(error))
            }
          }, batchWindowMs)
        }

        this.batchRequests.set(batchKey, batch)
      }

      batch.requests.push({
        id: requestId,
        params,
        resolve,
        reject
      })
    })
  }

  /**
   * Cache API responses with TTL
   */
  async cacheRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    ttlMs: number = 5 * 60 * 1000 // 5 minutes default
  ): Promise<T> {
    const cached = this.requestCache.get(key)
    
    if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
      return cached.data
    }

    const result = await requestFn()
    
    this.requestCache.set(key, {
      data: result,
      timestamp: Date.now(),
      ttl: ttlMs
    })

    return result
  }

  /**
   * Prefetch data in the background
   */
  prefetch<T>(
    key: string,
    requestFn: () => Promise<T>,
    ttlMs: number = 5 * 60 * 1000
  ): void {
    // Only prefetch if not already cached or cache is stale
    const cached = this.requestCache.get(key)
    if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
      return
    }

    // Prefetch in background
    requestFn()
      .then(result => {
        this.requestCache.set(key, {
          data: result,
          timestamp: Date.now(),
          ttl: ttlMs
        })
      })
      .catch(error => {
        console.warn(`Prefetch failed for ${key}:`, error)
      })
  }

  /**
   * Parallel request execution with concurrency limit
   */
  async parallelRequests<T>(
    requests: Array<() => Promise<T>>,
    concurrencyLimit: number = 5
  ): Promise<T[]> {
    const results: T[] = []
    const executing: Promise<void>[] = []

    for (let i = 0; i < requests.length; i++) {
      const request = requests[i]
      
      const promise = request().then(result => {
        results[i] = result
      })

      executing.push(promise)

      if (executing.length >= concurrencyLimit) {
        await Promise.race(executing)
        executing.splice(executing.findIndex(p => p === promise), 1)
      }
    }

    await Promise.all(executing)
    return results
  }

  /**
   * Optimistic updates - return cached data immediately and update in background
   */
  async optimisticUpdate<T>(
    key: string,
    updateFn: () => Promise<T>,
    fallbackData?: T
  ): Promise<{ data: T; isStale: boolean }> {
    const cached = this.requestCache.get(key)
    
    // Start the update in background
    const updatePromise = updateFn().then(result => {
      this.requestCache.set(key, {
        data: result,
        timestamp: Date.now(),
        ttl: 5 * 60 * 1000 // 5 minutes
      })
      return result
    })

    // Return cached data immediately if available
    if (cached) {
      return {
        data: cached.data,
        isStale: (Date.now() - cached.timestamp) > cached.ttl
      }
    }

    // If no cached data and fallback provided, return fallback
    if (fallbackData !== undefined) {
      return {
        data: fallbackData,
        isStale: true
      }
    }

    // Otherwise wait for the update
    const result = await updatePromise
    return {
      data: result,
      isStale: false
    }
  }

  /**
   * Clean up expired cache entries
   */
  cleanup(): void {
    const now = Date.now()
    
    for (const [key, cached] of this.requestCache.entries()) {
      if ((now - cached.timestamp) > cached.ttl) {
        this.requestCache.delete(key)
      }
    }

    // Clean up old pending requests (older than 30 seconds)
    for (const [key, pending] of this.pendingRequests.entries()) {
      if ((now - pending.timestamp) > 30000) {
        pending.reject(new Error('Request timeout'))
        this.pendingRequests.delete(key)
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    cacheSize: number
    pendingRequests: number
    batchRequests: number
    hitRate: number
  } {
    return {
      cacheSize: this.requestCache.size,
      pendingRequests: this.pendingRequests.size,
      batchRequests: this.batchRequests.size,
      hitRate: 0 // TODO: Implement hit rate tracking
    }
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.requestCache.clear()
    this.pendingRequests.clear()
    
    // Clear batch timers
    for (const batch of this.batchRequests.values()) {
      clearTimeout(batch.timer)
    }
    this.batchRequests.clear()
  }
}

// Global API optimizer instance
export const apiOptimizer = new ApiOptimizer()

// Utility functions for easy use
export function deduplicateApiCall<T>(
  key: string,
  requestFn: () => Promise<T>,
  windowMs?: number
): Promise<T> {
  return apiOptimizer.deduplicateRequest(key, requestFn, windowMs)
}

export function batchApiCall<T>(
  batchKey: string,
  requestId: string,
  params: any,
  batchFn: (requests: Array<{ id: string; params: any }>) => Promise<Record<string, T>>,
  batchWindowMs?: number
): Promise<T> {
  return apiOptimizer.batchRequest(batchKey, requestId, params, batchFn, batchWindowMs)
}

export function cacheApiCall<T>(
  key: string,
  requestFn: () => Promise<T>,
  ttlMs?: number
): Promise<T> {
  return apiOptimizer.cacheRequest(key, requestFn, ttlMs)
}

export function prefetchData<T>(
  key: string,
  requestFn: () => Promise<T>,
  ttlMs?: number
): void {
  apiOptimizer.prefetch(key, requestFn, ttlMs)
}

export function parallelApiCalls<T>(
  requests: Array<() => Promise<T>>,
  concurrencyLimit?: number
): Promise<T[]> {
  return apiOptimizer.parallelRequests(requests, concurrencyLimit)
}

export function optimisticApiUpdate<T>(
  key: string,
  updateFn: () => Promise<T>,
  fallbackData?: T
): Promise<{ data: T; isStale: boolean }> {
  return apiOptimizer.optimisticUpdate(key, updateFn, fallbackData)
}

// Automatic cleanup every 10 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    apiOptimizer.cleanup()
  }, 10 * 60 * 1000)
}
