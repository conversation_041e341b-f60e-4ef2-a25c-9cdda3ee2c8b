import { NextRequest } from "next/server"
import { 
  bulkEmailOperations, 
  withGmail<PERSON>uth, 
  validateRequiredFields,
  formatBulkOperationResult 
} from "@/lib/gmail"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['emailIds'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { emailIds, addLabels = [], removeLabels = [] } = body

    if (!Array.isArray(emailIds) || emailIds.length === 0) {
      throw new Error("emailIds must be a non-empty array")
    }

    let totalOperations = 0
    let successfulOperations = 0
    let failedOperations = 0

    // Handle adding labels
    for (const labelId of addLabels) {
      const result = await bulkEmailOperations(user.id, emailIds, 'addLabel', labelId)
      totalOperations += emailIds.length
      successfulOperations += result.success
      failedOperations += result.failed
      
      if (result.failed > 0) {
        console.warn(`Failed to add label ${labelId} to ${result.failed} emails`)
      }
    }

    // Handle removing labels
    for (const labelId of removeLabels) {
      const result = await bulkEmailOperations(user.id, emailIds, 'removeLabel', labelId)
      totalOperations += emailIds.length
      successfulOperations += result.success
      failedOperations += result.failed
      
      if (result.failed > 0) {
        console.warn(`Failed to remove label ${labelId} from ${result.failed} emails`)
      }
    }

    // If no operations were performed, just return success for the email count
    if (totalOperations === 0) {
      return {
        success: true,
        message: `No label operations performed on ${emailIds.length} email(s)`,
        stats: {
          total: emailIds.length,
          success: emailIds.length,
          failed: 0
        }
      }
    }

    return formatBulkOperationResult(
      totalOperations,
      successfulOperations,
      failedOperations,
      'modified'
    )
  })
} 