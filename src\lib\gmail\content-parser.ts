export function extractEnhancedEmailBody(payload: any): string {
  let htmlBody = ''
  let textBody = ''
  
  function extractFromPart(part: any, depth = 0): void {
    if (depth > 10) return // Prevent infinite recursion
    
    const mimeType = part.mimeType
    
    if (mimeType === 'text/html' && part.body?.data) {
      const decoded = Buffer.from(part.body.data, 'base64').toString('utf-8')
      if (decoded.length > htmlBody.length) {
        htmlBody = decoded
      }
    } else if (mimeType === 'text/plain' && part.body?.data) {
      const decoded = Buffer.from(part.body.data, 'base64').toString('utf-8')
      if (decoded.length > textBody.length) {
        textBody = decoded
      }
    } else if (part.parts && Array.isArray(part.parts)) {
      part.parts.forEach((subPart: any) => extractFromPart(subPart, depth + 1))
    }
  }
  
  extractFromPart(payload)
  
  // Return HTML if available, otherwise text with basic HTML formatting
  if (htmlBody) {
    return htmlBody
  } else if (textBody) {
    return textBody
      .replace(/\n/g, '<br>')
      .replace(/  /g, '&nbsp;&nbsp;')
  }
  
  // Fallback: try to extract from snippet or body data
  if (payload.body?.data) {
    try {
      return Buffer.from(payload.body.data, 'base64').toString('utf-8')
    } catch (e) {
      console.error('Failed to decode body data:', e)
    }
  }
  
  // Last resort: recursive search for any body data
  const findBodyData = (obj: any): string | null => {
    if (!obj || typeof obj !== 'object') return null
    
    if (obj.data && typeof obj.data === 'string') {
      try {
        const decoded = Buffer.from(obj.data, 'base64').toString('utf-8')
        if (decoded.length > 50) { // Only return if substantial content
          return decoded
        }
      } catch (e) {
        // Skip invalid base64
      }
    }
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const result = findBodyData(obj[key])
        if (result) return result
      }
    }
    
    return null
  }
  
  const fallbackBody = findBodyData(payload)
  return fallbackBody || 'Email content could not be extracted'
}

export function checkForAttachments(payload: any): boolean {
  if (!payload) return false
  
  function checkPart(part: any): boolean {
    if (part.filename && part.filename.length > 0) {
      return true
    }
    if (part.parts && Array.isArray(part.parts)) {
      return part.parts.some((subPart: any) => checkPart(subPart))
    }
    return false
  }
  
  return checkPart(payload)
}

export function extractAttachments(payload: any): Array<{
  filename: string
  mimeType: string
  size: number
  attachmentId: string
}> {
  const attachments: Array<{
    filename: string
    mimeType: string
    size: number
    attachmentId: string
  }> = []
  
  function extractFromPart(part: any) {
    if (part.filename && part.filename.length > 0 && part.body?.attachmentId) {
      attachments.push({
        filename: part.filename,
        mimeType: part.mimeType || 'application/octet-stream',
        size: part.body.size || 0,
        attachmentId: part.body.attachmentId
      })
    }
    
    if (part.parts && Array.isArray(part.parts)) {
      part.parts.forEach((subPart: any) => extractFromPart(subPart))
    }
  }
  
  extractFromPart(payload)
  return attachments
}

export function createEmailMessage({
  from,
  to,
  subject,
  htmlBody,
  textBody,
  trackingId,
  attachments,
  messageId,
  references,
  inReplyTo
}: {
  from: string
  to: string
  subject: string
  htmlBody: string
  textBody?: string
  trackingId?: string
  messageId?: string
  references?: string
  inReplyTo?: string
  attachments?: Array<{
    filename: string
    content: string
    mimeType: string
  }>
}): string {
  const boundary = '----emailboundary' + Date.now()

  let message = [
    `From: ${from}`,
    `To: ${to}`,
    `Subject: ${subject}`,
    'MIME-Version: 1.0'
  ]

  // Add threading headers for replies
  if (inReplyTo) {
    message.push(`In-Reply-To: ${inReplyTo}`)
  }
  if (references) {
    message.push(`References: ${references}`)
  }
  if (messageId) {
    message.push(`Message-ID: ${messageId}`)
  }

  message.push(
    `Content-Type: multipart/mixed; boundary="${boundary}"`,
    '',
    `--${boundary}`,
    'Content-Type: multipart/alternative; boundary="alt' + boundary + '"',
    '',
    `--alt${boundary}`
  )

  // Add text part if provided
  if (textBody) {
    message.push(
      'Content-Type: text/plain; charset=utf-8',
      'Content-Transfer-Encoding: quoted-printable',
      '',
      textBody,
      '',
      `--alt${boundary}`
    )
  }

  // Add HTML part with tracking pixel if trackingId is provided
  let htmlContent = htmlBody
  if (trackingId) {
    const trackingPixel = `<img src="${process.env.NEXTAUTH_URL}/api/track/${trackingId}" width="1" height="1" style="display:none;" />`
    htmlContent = htmlBody + trackingPixel
  }

  message.push(
    'Content-Type: text/html; charset=utf-8',
    'Content-Transfer-Encoding: quoted-printable',
    '',
    htmlContent,
    '',
    `--alt${boundary}--`
  )

  // Add attachments if provided
  if (attachments && attachments.length > 0) {
    attachments.forEach(attachment => {
      message.push(
        `--${boundary}`,
        `Content-Type: ${attachment.mimeType}`,
        `Content-Disposition: attachment; filename="${attachment.filename}"`,
        'Content-Transfer-Encoding: base64',
        '',
        attachment.content,
        ''
      )
    })
  }

  message.push(`--${boundary}--`)

  const emailMessage = message.join('\r\n')
  return Buffer.from(emailMessage).toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '')
} 