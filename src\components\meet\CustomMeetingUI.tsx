'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Input } from '@/components/ui/input'
import {
  Video,
  VideoOff,
  PhoneOff,
  MessageSquare,
  Users,
  Copy,
  ExternalLink,
  Send,
  Clock,
  Link,
  Circle,
  MicOff,
  Hand,
  AlertTriangle
} from 'lucide-react'
import { useToast } from '../../hooks/use-toast'
import { useSession } from 'next-auth/react'

interface MeetingSpace {
  name: string
  meetingUri: string
  meetingCode: string
  displayName?: string
  description?: string
  config?: {
    accessType?: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
    entryPointAccess?: 'ALL' | 'CREATOR_APP_ONLY'
  }
}

interface Participant {
  id: string
  name: string
  email: string
  avatar?: string
  isHost?: boolean
  isMuted?: boolean
  isVideoOn?: boolean
  isHandRaised?: boolean
  joinTime: string
  lastSeen: string
}

interface ChatMessage {
  id: string
  sender: string
  senderEmail: string
  message: string
  timestamp: string
  type: 'text' | 'system'
}

interface MeetingState {
  isRecording: boolean
  isScreenSharing: boolean
  screenSharerName?: string
  meetingDuration: number
  meetingStatus: 'waiting' | 'active' | 'ended'
  hostId?: string
  settings: {
    allowUnmute: boolean
    allowVideo: boolean
    allowScreenShare: boolean
    allowChat: boolean
    waitingRoomEnabled: boolean
  }
}

interface SyncData {
  participants: Participant[]
  chatMessages: ChatMessage[]
  meetingState: MeetingState
  lastUpdated: string
}

interface CustomMeetingUIProps {
  meetingSpace: MeetingSpace
  onEndMeeting?: () => void
  onLeaveMeeting?: () => void
  className?: string
}

export default function CustomMeetingUI({
  meetingSpace,
  onEndMeeting,
  onLeaveMeeting,
  className
}: CustomMeetingUIProps) {
  const { data: session } = useSession()
  const [showChat, setShowChat] = useState(false)
  const [showParticipants, setShowParticipants] = useState(false)
  const [newMessage, setNewMessage] = useState('')
  const [isInMeeting, setIsInMeeting] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting')
  
  // Real-time sync state
  const [syncData, setSyncData] = useState<SyncData>({
    participants: [],
    chatMessages: [],
    meetingState: {
      isRecording: false,
      isScreenSharing: false,
      meetingDuration: 0,
      meetingStatus: 'waiting',
      settings: {
        allowUnmute: true,
        allowVideo: true,
        allowScreenShare: true,
        allowChat: true,
        waitingRoomEnabled: false
      }
    },
    lastUpdated: new Date().toISOString()
  })
  
  const chatScrollRef = useRef<HTMLDivElement>(null)
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const { toast } = useToast()

  // Get space name from meeting URI
  const getSpaceName = () => {
    const match = meetingSpace.meetingUri.match(/spaces\/([^\/]+)/)
    return match ? match[1] : meetingSpace.meetingCode
  }

  // Real-time synchronization
  useEffect(() => {
    const spaceName = getSpaceName()
    
    // Initial sync
    syncMeetingState()
    
    // Set up polling for real-time updates
    syncIntervalRef.current = setInterval(syncMeetingState, 2000)
    
    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current)
      }
    }
  }, [])

  // Handle cleanup when component unmounts
  useEffect(() => {
    return () => {
      if (isInMeeting) {
                 updateMeetingState('leaveRoom', {}).catch((error) => console.error('Cleanup error:', error))
      }
    }
  }, [isInMeeting])

  // Auto-scroll chat to bottom
  useEffect(() => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight
    }
  }, [syncData.chatMessages])

  const syncMeetingState = async () => {
    try {
      const spaceName = getSpaceName()
      const response = await fetch(`/api/meet/spaces/${spaceName}/sync`)
      
      if (response.ok) {
        const result = await response.json()
        setSyncData(result.data)
        setConnectionStatus('connected')
      } else {
        setConnectionStatus('disconnected')
      }
    } catch (error) {
      console.error('Sync error:', error)
      setConnectionStatus('disconnected')
    }
  }

  const updateMeetingState = async (action: string, data: any) => {
    try {
      const spaceName = getSpaceName()
      const response = await fetch(`/api/meet/spaces/${spaceName}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, data })
      })
      
      if (response.ok) {
        const result = await response.json()
        setSyncData(result.data)
        setConnectionStatus('connected')
      }
    } catch (error) {
      console.error('Update error:', error)
      setConnectionStatus('disconnected')
    }
  }

  const formatDuration = (seconds: number) => {
    const hrs = Math.floor(seconds / 3600)
    const mins = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const copyMeetingLink = () => {
    navigator.clipboard.writeText(meetingSpace.meetingUri)
    toast({
      title: "Meeting link copied",
      description: "The meeting link has been copied to your clipboard.",
    })
  }

  const copyMeetingCode = () => {
    navigator.clipboard.writeText(meetingSpace.meetingCode)
    toast({
      title: "Meeting code copied",
      description: "The meeting code has been copied to your clipboard.",
    })
  }

  const joinMeeting = () => {
    setIsInMeeting(true)
    toast({
      title: "Joined meeting",
      description: "You have successfully joined the meeting.",
    })
  }

  const leaveMeeting = async () => {
    await updateMeetingState('leaveRoom', {})
    setIsInMeeting(false)
    onLeaveMeeting?.()
    toast({
      title: "Left meeting",
      description: "You have left the meeting.",
    })
  }

  const endMeeting = async () => {
    try {
      const spaceName = getSpaceName()
      const response = await fetch(`/api/meet/spaces/${spaceName}/sync`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setIsInMeeting(false)
        onEndMeeting?.()
        toast({
          title: "Meeting ended",
          description: "The meeting has been ended for all participants.",
        })
      }
    } catch (error) {
      console.error('End meeting error:', error)
    }
  }



  const sendMessage = async () => {
    if (newMessage.trim()) {
      await updateMeetingState('sendMessage', { message: newMessage.trim() })
      setNewMessage('')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const openInGoogleMeet = () => {
    window.open(meetingSpace.meetingUri, '_blank')
  }

  const currentUser = syncData.participants.find(p => p.email === session?.user?.email)
  const isHost = currentUser?.isHost || false

  return (
    <div className={`w-full h-full flex flex-col bg-background ${className}`}>
      {/* Meeting Header */}
      <Card className="flex-shrink-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <CardTitle className="text-xl flex items-center gap-2">
                  {meetingSpace.displayName || 'Meeting Space'}
                  {isHost && <Badge variant="secondary">Host</Badge>}
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {meetingSpace.description || 'Google Meet Integration'}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'}>
                  <Circle className={`h-2 w-2 mr-1 ${connectionStatus === 'connected' ? 'fill-green-500' : 'fill-red-500'}`} />
                  {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
                </Badge>
                {syncData.meetingState.meetingStatus === 'active' && (
                  <Badge variant="outline">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatDuration(syncData.meetingState.meetingDuration)}
                  </Badge>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={copyMeetingCode}>
                <Copy className="h-4 w-4 mr-2" />
                Copy Code
              </Button>
              <Button variant="outline" size="sm" onClick={openInGoogleMeet}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Open in Meet
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Meeting Area */}
      <div className="flex-1 flex gap-4 p-4 overflow-hidden">
        {/* Meeting Join Area */}
        <div className="flex-1 flex flex-col">
          <Card className="flex-1 flex flex-col">
            <CardContent className="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-950 rounded-lg relative">
              <div className="text-center max-w-md">
                <div className="mb-6">
                  <Video className="h-20 w-20 mx-auto mb-4 text-blue-600" />
                  <h2 className="text-2xl font-bold mb-2">Google Meet Session</h2>
                  <p className="text-muted-foreground mb-4">
                    Join the meeting to start collaborating with other participants
                  </p>
                </div>

                {/* Meeting Information */}
                <div className="bg-background/80 backdrop-blur-sm rounded-lg p-4 mb-6 text-left">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Meeting Code:</span>
                      <code className="text-sm bg-muted px-2 py-1 rounded">{meetingSpace.meetingCode}</code>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Access Type:</span>
                      <Badge variant="outline">{meetingSpace.config?.accessType || 'OPEN'}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Participants:</span>
                      <span className="text-sm">{syncData.participants.length}</span>
                    </div>
                  </div>
                </div>

                {/* Join Actions */}
                <div className="space-y-3">
                  <Button
                    onClick={openInGoogleMeet}
                    size="lg"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <ExternalLink className="h-5 w-5 mr-2" />
                    Join Google Meet
                  </Button>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={copyMeetingCode} className="flex-1">
                      <Copy className="h-4 w-4 mr-2" />
                      Copy Code
                    </Button>
                    <Button variant="outline" size="sm" onClick={copyMeetingLink} className="flex-1">
                      <Link className="h-4 w-4 mr-2" />
                      Copy Link
                    </Button>
                  </div>
                </div>

                {/* Instructions */}
                <div className="mt-6 text-xs text-muted-foreground space-y-2">
                  <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                    <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">📋 Meeting Instructions:</p>
                    <ul className="text-blue-700 dark:text-blue-300 space-y-1">
                      <li>• Click "Join Google Meet" to open the meeting in a new tab</li>
                      <li>• The meeting creator will need to admit external participants</li>
                      <li>• Make sure you're signed in with your Google account</li>
                      <li>• If you can't see other participants, ask the host to admit you</li>
                    </ul>
                  </div>
                  <div className="bg-amber-50 dark:bg-amber-950 p-3 rounded-lg border border-amber-200 dark:border-amber-800">
                    <p className="font-medium text-amber-800 dark:text-amber-200 mb-1">⚠️ Visibility Issue?</p>
                    <p className="text-amber-700 dark:text-amber-300">
                      If participants can't see each other, the meeting host needs to admit external users.
                      This is a Google Meet security feature for meetings with participants from different organizations.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Meeting Management Controls */}
          <div className="flex items-center justify-center gap-2 mt-4">
            <Button variant="outline" size="sm" onClick={() => setShowParticipants(!showParticipants)}>
              <Users className="h-4 w-4 mr-2" />
              Participants ({syncData.participants.length})
            </Button>

            <Button variant="outline" size="sm" onClick={() => setShowChat(!showChat)}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat
            </Button>

            <Separator orientation="vertical" className="h-6" />

            <Button variant="outline" size="sm" onClick={leaveMeeting}>
              <PhoneOff className="h-4 w-4 mr-2" />
              Leave
            </Button>

            {isHost && (
              <Button variant="destructive" size="sm" onClick={endMeeting}>
                End Meeting
              </Button>
            )}
          </div>
        </div>

        {/* Side Panel */}
        {(showParticipants || showChat) && (
          <div className="w-80 flex flex-col">
            {showParticipants && (
              <Card className="flex-1 mb-4">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Participants ({syncData.participants.length})
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <ScrollArea className="h-64">
                    <div className="space-y-2 p-4">
                      {syncData.participants.map((participant) => (
                        <div key={participant.id} className="flex items-center justify-between p-2 rounded-lg border">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={participant.avatar} />
                              <AvatarFallback>{participant.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium">{participant.name}</p>
                              {participant.isHost && <Badge variant="secondary" className="text-xs">Host</Badge>}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            {participant.isMuted && <MicOff className="h-3 w-3 text-red-500" />}
                            {!participant.isVideoOn && <VideoOff className="h-3 w-3 text-red-500" />}
                            {participant.isHandRaised && <Hand className="h-3 w-3 text-blue-500" />}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            )}

            {showChat && (
              <Card className="flex-1 flex flex-col">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Chat
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col p-0">
                  <ScrollArea className="flex-1 p-4" ref={chatScrollRef}>
                    <div className="space-y-3">
                      {syncData.chatMessages.map((message) => (
                        <div key={message.id} className={`${message.type === 'system' ? 'text-center' : ''}`}>
                          {message.type === 'system' ? (
                            <p className="text-xs text-muted-foreground italic">
                              {message.message}
                            </p>
                          ) : (
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xs font-medium">{message.sender}</span>
                                <span className="text-xs text-muted-foreground">
                                  {new Date(message.timestamp).toLocaleTimeString()}
                                </span>
                              </div>
                              <p className="text-sm bg-muted p-2 rounded-lg">{message.message}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                  
                  {syncData.meetingState.settings.allowChat && (
                    <div className="p-4 border-t">
                      <div className="flex gap-2">
                        <Input
                          placeholder="Type a message..."
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          onKeyDown={handleKeyDown}
                          className="flex-1"
                        />
                        <Button size="sm" onClick={sendMessage} disabled={!newMessage.trim()}>
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* Connection Status Warning */}
      {connectionStatus === 'disconnected' && (
        <div className="p-4 bg-red-50 border-t border-red-200 flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <p className="text-sm text-red-800">
            Connection lost. Trying to reconnect...
          </p>
        </div>
      )}
    </div>
  )
} 