import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentCalendarService } from '@/lib/ai-agent-calendar'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      attendees = [],
      startDate,
      endDate,
      duration = 60,
      workingHours = { start: '09:00', end: '17:00' },
      timezone = 'UTC'
    } = await request.json()

    if (!startDate || !endDate) {
      return NextResponse.json({ 
        error: 'Start date and end date are required' 
      }, { status: 400 })
    }

    const availableSlots = await aiAgentCalendarService.findAvailableSlots(session.user.id, {
      attendees,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      duration,
      workingHours,
      timezone
    })

    return NextResponse.json({
      availableSlots,
      success: true
    })

  } catch (error) {
    console.error('Availability check error:', error)
    return NextResponse.json(
      { error: 'Failed to check availability' },
      { status: 500 }
    )
  }
}
