import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentCalendarService } from '@/lib/ai-agent-calendar'
import { startPerformanceMetric, endPerformanceMetric } from '@/lib/performance/monitor'

export async function POST(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('ai-agent-calendar-availability', { endpoint: '/api/ai-agent/calendar/availability' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('ai-agent-calendar-availability')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      attendees = [],
      startDate,
      endDate,
      duration = 60,
      workingHours = { start: '09:00', end: '17:00' },
      timezone = 'UTC'
    } = await request.json()

    if (!startDate || !endDate) {
      endPerformanceMetric('ai-agent-calendar-availability')
      return NextResponse.json({
        error: 'Start date and end date are required'
      }, { status: 400 })
    }

    const availableSlots = await aiAgentCalendarService.findAvailableSlots(session.user.id, {
      attendees,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      duration,
      workingHours,
      timezone
    })

    // End performance monitoring
    const duration = endPerformanceMetric('ai-agent-calendar-availability')
    if (duration && duration > 2000) {
      console.warn(`Slow AI calendar availability: ${duration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      availableSlots,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('ai-agent-calendar-availability')
    console.error('Availability check error:', error)
    return NextResponse.json(
      { error: 'Failed to check availability' },
      { status: 500 }
    )
  }
}
