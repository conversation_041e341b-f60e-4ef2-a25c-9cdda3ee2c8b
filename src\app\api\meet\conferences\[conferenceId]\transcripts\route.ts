import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetU<PERSON>, 
  handleMeetApiError, 
  formatMeetApiResponse,
  validatePaginationParams,
  listTranscripts,
  getTranscriptDownloadInfo
} from "@/lib/meet"

interface RouteParams {
  params: {
    conferenceId: string
  }
}

/**
 * GET /api/meet/conferences/[conferenceId]/transcripts - Get conference transcripts
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { conferenceId } = params
    
    // Decode the conference ID in case it's URL encoded
    const decodedConferenceId = decodeURIComponent(conferenceId)
    const searchParams = url.searchParams

    const { pageSize, pageToken } = validatePaginationParams(searchParams)
    const includeDownloadInfo = searchParams.get('includeDownloadInfo') === 'true'

    // Get transcripts
    const transcripts = await listTranscripts(user.id, decodedConferenceId, {
      pageSize,
      pageToken
    })

    let response: any = transcripts

    // Include download information if requested
    if (includeDownloadInfo && transcripts.items.length > 0) {
      const transcriptsWithDownloadInfo = await Promise.all(
        transcripts.items.map(async (transcript) => {
          try {
            const downloadInfo = await getTranscriptDownloadInfo(user.id, transcript.name)
            return {
              ...transcript,
              downloadInfo
            }
          } catch (error) {
            console.error(`Error getting download info for transcript ${transcript.name}:`, error)
            return transcript
          }
        })
      )

      response = {
        ...transcripts,
        items: transcriptsWithDownloadInfo
      }
    }

    return formatMeetApiResponse(
      response,
      "Conference transcripts retrieved successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
