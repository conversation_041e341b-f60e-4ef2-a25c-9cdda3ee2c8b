import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(_request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Fetch contacts with their contact lists
    const contacts = await prisma.contact.findMany({
      where: { userId: user.id },
      include: {
        lists: {
          include: {
            contactList: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: "desc" }
    })

    // Transform the data to match the expected format
    const transformedContacts = contacts.map(contact => ({
      id: contact.id,
      firstName: contact.firstName,
      lastName: contact.lastName,
      email: contact.email,
      customFields: contact.customFields,
      contactLists: contact.lists.map(cl => cl.contactList),
      createdAt: contact.createdAt.toISOString(),
      updatedAt: contact.updatedAt.toISOString()
    }))

    return NextResponse.json(transformedContacts)
  } catch (error) {
    console.error("Error fetching contacts:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const { firstName, lastName, email, customFields } = await request.json()

    // Validate required fields
    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 })
    }

    // Check if contact with this email already exists for this user
    const existingContact = await prisma.contact.findFirst({
      where: {
        userId: user.id,
        email: email
      }
    })

    if (existingContact) {
      return NextResponse.json(
        { error: "Contact with this email already exists" },
        { status: 409 }
      )
    }

    // Create new contact
    const newContact = await prisma.contact.create({
      data: {
        userId: user.id,
        firstName: firstName || "",
        lastName: lastName || "",
        email,
        customFields: customFields || {}
      },
      include: {
        lists: {
          include: {
            contactList: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    })

    // Transform the response to match expected format
    const transformedContact = {
      id: newContact.id,
      firstName: newContact.firstName,
      lastName: newContact.lastName,
      email: newContact.email,
      customFields: newContact.customFields,
      contactLists: newContact.lists.map(cl => cl.contactList),
      createdAt: newContact.createdAt.toISOString(),
      updatedAt: newContact.updatedAt.toISOString()
    }

    return NextResponse.json(transformedContact, { status: 201 })
  } catch (error) {
    console.error("Error adding contact:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 