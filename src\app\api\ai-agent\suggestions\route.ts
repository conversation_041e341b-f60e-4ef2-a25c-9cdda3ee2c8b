import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentService } from '@/lib/ai-agent'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const suggestions = await aiAgentService.getPersonalizedSuggestions(session.user.id)

    return NextResponse.json({
      ...suggestions,
      success: true
    })

  } catch (error) {
    console.error('Get personalized suggestions error:', error)
    return NextResponse.json(
      { error: 'Failed to get personalized suggestions' },
      { status: 500 }
    )
  }
}
