'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  Video, 
  Users, 
  MessageSquare,
  Share,
  Loader2,
  Send,
  Mic,
  MicOff
} from 'lucide-react'
import { Input } from '@/components/ui/input'

// Google Meet Add-ons SDK types
declare global {
  interface Window {
    meet?: {
      addon?: {
        createAddonSession: (config: { cloudProjectNumber: string }) => Promise<any>
      }
    }
  }
}

interface MeetAddonMainStageProps {
  cloudProjectNumber: string
}

interface ChatMessage {
  id: string
  user: string
  message: string
  timestamp: Date
}

export default function MeetAddonMainStage({ cloudProjectNumber }: MeetAddonMainStageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [mainStageClient, setMainStageClient] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [participants, setParticipants] = useState<string[]>(['You'])
  
  const { toast } = useToast()

  useEffect(() => {
    initializeMainStage()
  }, [])

  const initializeMainStage = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Check if we're running in development mode
      const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost'

      // Check for meet_sdk parameter (only present when running inside Google Meet)
      const urlParams = new URLSearchParams(window.location.search)
      const hasMeetSdkParam = urlParams.has('meet_sdk')

      if (isDevelopment && !hasMeetSdkParam) {
        // Running locally - show development mode
        setError('DEVELOPMENT_MODE')
        // Still add demo messages for development
        setMessages([
          {
            id: '1',
            user: 'System',
            message: 'Welcome to the collaborative workspace! Everyone in the meeting can see this.',
            timestamp: new Date()
          },
          {
            id: '2',
            user: 'Development',
            message: 'This is development mode. Deploy to test full functionality.',
            timestamp: new Date()
          }
        ])
        return
      }

      // Wait for Google Meet Add-ons SDK to be available
      if (!window.meet?.addon) {
        throw new Error('Google Meet Add-ons SDK not available. This add-on must run inside Google Meet.')
      }

      // Create addon session
      const session = await window.meet.addon.createAddonSession({
        cloudProjectNumber: cloudProjectNumber,
      })

      // Create main stage client
      const client = await session.createMainStageClient()
      setMainStageClient(client)

      // Add some demo messages
      setMessages([
        {
          id: '1',
          user: 'System',
          message: 'Welcome to the collaborative workspace! Everyone in the meeting can see this.',
          timestamp: new Date()
        }
      ])

      toast({
        title: "Main Stage Ready",
        description: "Collaborative workspace is now active",
      })
    } catch (error) {
      console.error('Failed to initialize main stage:', error)
      setError(error instanceof Error ? error.message : 'Failed to initialize main stage')
    } finally {
      setIsLoading(false)
    }
  }

  const sendMessage = () => {
    if (!newMessage.trim()) return

    const message: ChatMessage = {
      id: Date.now().toString(),
      user: 'You',
      message: newMessage.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, message])
    setNewMessage('')

    // In a real implementation, this would broadcast to all participants
    // via the Meet Add-ons SDK collaboration APIs
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin mx-auto text-blue-600" />
          <p className="text-lg font-medium">Initializing collaborative workspace...</p>
          <p className="text-sm text-muted-foreground">Setting up main stage for all participants</p>
        </div>
      </div>
    )
  }

  if (error === 'DEVELOPMENT_MODE') {
    // Show development mode with demo functionality
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Development Mode Header */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Video className="h-8 w-8 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900">Collaborative Workspace</h1>
            </div>
            <Card className="bg-blue-50 border-blue-200 max-w-2xl mx-auto">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <h3 className="font-medium text-blue-800">🚧 Development Mode</h3>
                  <p className="text-sm text-blue-700">
                    You're viewing the add-on main stage in development mode. The "Missing required Meet SDK URL parameter" error is expected when running locally.
                  </p>
                  <div className="text-xs text-blue-600 bg-blue-100 p-2 rounded">
                    <strong>To test full functionality:</strong> Deploy to HTTPS and configure in Google Cloud Console
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Continue with the rest of the component but in development mode */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card className="bg-white shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Share className="h-5 w-5 text-blue-600" />
                    Shared Activity Space (Demo)
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg text-center">
                    <h3 className="text-xl font-semibold mb-2">Hello, World!</h3>
                    <p className="text-blue-100">
                      This is a preview of the shared workspace that would be visible to all meeting participants.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-pink-100">
        <Card className="w-full max-w-md border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Main Stage Error</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-red-700">{error}</p>
            <Button
              variant="outline"
              onClick={initializeMainStage}
              className="w-full"
            >
              Retry Initialization
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <Video className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Collaborative Workspace</h1>
          </div>
          <div className="flex items-center justify-center gap-4">
            <Badge variant="secondary" className="bg-green-100 text-green-800 px-3 py-1">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
              Live Session
            </Badge>
            <Badge variant="outline" className="px-3 py-1">
              <Users className="h-3 w-3 mr-1" />
              {participants.length} Participants
            </Badge>
          </div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            This is the main stage that everyone in the Google Meet can see and interact with. 
            All activities here are synchronized across all participants.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Welcome Card */}
            <Card className="bg-white shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share className="h-5 w-5 text-blue-600" />
                  Shared Activity Space
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-lg text-center">
                  <h3 className="text-xl font-semibold mb-2">Hello, World!</h3>
                  <p className="text-blue-100">
                    This is a shared workspace visible to all meeting participants.
                    You can build any collaborative features here!
                  </p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h4 className="font-medium text-green-800 mb-2">Real-time Sync</h4>
                    <p className="text-sm text-green-700">
                      All changes are synchronized across participants
                    </p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <h4 className="font-medium text-purple-800 mb-2">Collaborative</h4>
                    <p className="text-sm text-purple-700">
                      Multiple users can interact simultaneously
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Interactive Demo */}
            <Card className="bg-white shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-green-600" />
                  Shared Chat
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Messages */}
                  <div className="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto space-y-3">
                    {messages.map((msg) => (
                      <div key={msg.id} className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{msg.user}</span>
                          <span className="text-xs text-muted-foreground">
                            {msg.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <div className="bg-white p-2 rounded border text-sm">
                          {msg.message}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Message Input */}
                  <div className="flex gap-2">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type a message for all participants..."
                      className="flex-1"
                    />
                    <Button onClick={sendMessage} disabled={!newMessage.trim()}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Participants */}
            <Card className="bg-white shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4" />
                  Participants ({participants.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {participants.map((participant, index) => (
                    <div key={index} className="flex items-center gap-3 p-2 rounded-lg bg-gray-50">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {participant.charAt(0).toUpperCase()}
                      </div>
                      <span className="text-sm font-medium">{participant}</span>
                      {index === 0 && (
                        <Badge variant="secondary" className="text-xs ml-auto">You</Badge>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Status */}
            <Card className="bg-white shadow-lg">
              <CardHeader>
                <CardTitle className="text-sm">Session Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span>Connection</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Connected
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Sync Status</span>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    Real-time
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Participants</span>
                  <Badge variant="outline">{participants.length} Active</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-blue-800">
                    💡 About this workspace:
                  </h4>
                  <ul className="text-xs text-blue-700 space-y-1">
                    <li>• Everyone in the meeting can see this</li>
                    <li>• Changes sync in real-time</li>
                    <li>• Built with Google Meet Add-ons SDK</li>
                    <li>• Runs inside Google Meet interface</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
