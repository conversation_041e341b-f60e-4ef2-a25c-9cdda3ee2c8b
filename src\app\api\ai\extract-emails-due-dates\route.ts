import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { geminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { emails } = await request.json()

    if (!emails || !Array.isArray(emails)) {
      return NextResponse.json({ error: 'Emails array is required' }, { status: 400 })
    }

    // Extract due dates and important calendar items from emails
    const dueDateItems = await geminiService.extractDueDatesFromEmails(emails)

    return NextResponse.json({
      items: dueDateItems,
      success: true,
      count: dueDateItems.length
    })

  } catch (error) {
    console.error('Email due date extraction error:', error)
    return NextResponse.json(
      { error: 'Failed to extract due dates from emails' },
      { status: 500 }
    )
  }
} 