import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentService } from '@/lib/ai-agent'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { conversationId, content, context } = await request.json()

    if (!conversationId || !content) {
      return NextResponse.json({ 
        error: 'Conversation ID and content are required' 
      }, { status: 400 })
    }

    // Send message and get AI response
    const aiMessage = await aiAgentService.sendMessage(
      conversationId,
      session.user.id,
      content,
      context
    )

    // Create user message object for response
    const userMessage = {
      id: `user-${Date.now()}`,
      role: 'user' as const,
      content,
      timestamp: new Date()
    }

    return NextResponse.json({
      userMessage,
      aiMessage,
      success: true
    })

  } catch (error) {
    console.error('Chat message error:', error)
    return NextResponse.json(
      { error: 'Failed to process message' },
      { status: 500 }
    )
  }
}
