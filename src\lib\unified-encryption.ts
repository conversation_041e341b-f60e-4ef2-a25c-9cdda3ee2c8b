import crypto from 'crypto'

export type EncryptionContext = 'chat' | 'calendar' | 'email-analysis' | 'draft' | 'contact' | 'campaign'

// Production encryption configuration - AES-256-GCM with HKDF
const ENCRYPTION_CONFIG = {
  algorithm: 'aes-256-gcm' as const,
  keyDerivation: 'hkdf',
  hashAlgorithm: 'sha256',
  ivLength: 12,        // 96 bits for AES-GCM
  saltLength: 32,      // 256 bits
  keyLength: 32,       // 256 bits
  tagLength: 16,       // 128 bits authentication tag
  maxPlaintextSize: 1024 * 1024 * 10 // 10MB limit
}

export interface EncryptedDataFormat {
  algorithm: string
  salt: string
  iv: string
  authTag: string
  ciphertext: string
  version: string
}

// Chat-specific types
export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  metadata?: Record<string, any>
}

export interface ChatSession {
  id: string
  userId: string
  title: string
  messages: ChatMessage[]
  context?: string
  createdAt: Date
  updatedAt: Date
  isArchived: boolean
}

export interface EncryptedChatSession {
  id: string
  userId: string
  title: string
  messages: string
  context?: string
  createdAt: Date
  updatedAt: Date
  isArchived: boolean
}

/**
 * Unified encryption service for all data types in the application
 * Supports chat, calendar, email analysis, contacts, campaigns, and drafts
 */
class UnifiedEncryptionService {
  private readonly version = '2.0'
  
  /**
   * Generate cryptographically secure random bytes
   */
  generateSecureRandom(length: number): string {
    return crypto.randomBytes(length).toString('hex')
  }
  
  /**
   * Create cryptographic hash for salts and identifiers
   */
  hashData(data: string): string {
    return crypto.createHash('sha256').update(data, 'utf8').digest('hex')
  }
  
  /**
   * Generate master key for user and context
   */
  private async generateMasterKey(
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<Buffer> {
    const keyMaterial = [
      userId,
      'eagle-mass-mailer',
      context,
      'v2.0',
      additionalEntropy || ''
    ].join(':')
    
    return crypto.createHash('sha256').update(keyMaterial, 'utf8').digest()
  }
  
  /**
   * Derive encryption and authentication keys using HKDF
   */
  private deriveKeys(
    masterKey: Buffer,
    salt: Buffer,
    context: string,
    userId: string
  ): { encryptionKey: Buffer; authKey: Buffer } {
    const info = Buffer.from(`${context}:${userId}:keys`, 'utf8')
    
    // Derive 64 bytes (32 for encryption + 32 for auth)
    const derivedKeyArrayBuffer = crypto.hkdfSync(
      ENCRYPTION_CONFIG.hashAlgorithm,
      masterKey,
      salt,
      info,
      64
    )
    
    // Convert ArrayBuffer to Buffer
    const derivedKey = Buffer.from(derivedKeyArrayBuffer)
    
    return {
      encryptionKey: derivedKey.subarray(0, 32),
      authKey: derivedKey.subarray(32, 64)
    }
  }
  
  /**
   * Check if data is in modern encrypted format
   */
  isEncrypted(data: string): boolean {
    try {
      const parsed = JSON.parse(data)
      return parsed.algorithm === ENCRYPTION_CONFIG.algorithm && 
             parsed.version === this.version &&
             parsed.salt && parsed.iv && parsed.authTag && parsed.ciphertext
    } catch {
      return false
    }
  }
  
  /**
   * Encrypt data using AES-256-GCM
   */
  async encryptData(
    plaintext: string,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<string> {
    if (!plaintext || typeof plaintext !== 'string') {
      throw new Error('Invalid plaintext: must be a non-empty string')
    }
    
    if (Buffer.byteLength(plaintext, 'utf8') > ENCRYPTION_CONFIG.maxPlaintextSize) {
      throw new Error(`Plaintext too large: maximum ${ENCRYPTION_CONFIG.maxPlaintextSize} bytes`)
    }
    
    try {
      // Generate master key
      const masterKey = await this.generateMasterKey(userId, context, additionalEntropy)
      
      // Generate cryptographically secure random values
      const salt = crypto.randomBytes(ENCRYPTION_CONFIG.saltLength)
      const iv = crypto.randomBytes(ENCRYPTION_CONFIG.ivLength)
      
      // Derive keys using HKDF
      const derivedKeys = this.deriveKeys(masterKey, salt, context, userId)
      
      // Create cipher
      const cipher = crypto.createCipheriv(ENCRYPTION_CONFIG.algorithm, derivedKeys.encryptionKey, iv) as crypto.CipherGCM
      
      // Encrypt data
      let ciphertext = cipher.update(plaintext, 'utf8')
      ciphertext = Buffer.concat([ciphertext, cipher.final()])
      
      // Get authentication tag
      const authTag = cipher.getAuthTag()
      
      // Create encrypted data structure
      const encryptedData: EncryptedDataFormat = {
        algorithm: ENCRYPTION_CONFIG.algorithm,
        salt: salt.toString('hex'),
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        ciphertext: ciphertext.toString('hex'),
        version: this.version
      }
      
      return JSON.stringify(encryptedData)
    } catch (error) {
      throw new Error(`Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Decrypt data using AES-256-GCM
   */
  async decryptData(
    encryptedData: string,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<string> {
    if (!encryptedData || typeof encryptedData !== 'string') {
      throw new Error('Invalid encrypted data: must be a non-empty string')
    }
    
    try {
      // Parse encrypted data
      const parsed: EncryptedDataFormat = JSON.parse(encryptedData)
      
      // Validate format
      if (parsed.algorithm !== ENCRYPTION_CONFIG.algorithm) {
        throw new Error(`Unsupported algorithm: ${parsed.algorithm}`)
      }
      
      if (parsed.version !== this.version) {
        throw new Error(`Unsupported version: ${parsed.version}`)
      }
      
      // Convert hex strings back to buffers
      const salt = Buffer.from(parsed.salt, 'hex')
      const iv = Buffer.from(parsed.iv, 'hex')
      const authTag = Buffer.from(parsed.authTag, 'hex')
      const ciphertext = Buffer.from(parsed.ciphertext, 'hex')
      
      // Generate master key
      const masterKey = await this.generateMasterKey(userId, context, additionalEntropy)
      
      // Derive keys
      const derivedKeys = this.deriveKeys(masterKey, salt, context, userId)
      
      // Create decipher
      const decipher = crypto.createDecipheriv(ENCRYPTION_CONFIG.algorithm, derivedKeys.encryptionKey, iv) as crypto.DecipherGCM
      decipher.setAuthTag(authTag)
      
      // Decrypt data
      let plaintext = decipher.update(ciphertext, undefined, 'utf8')
      plaintext += decipher.final('utf8')
      
      return plaintext
    } catch (error) {
      throw new Error(`Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
  
  /**
   * Generate encryption salt for specific resources
   */
  generateEncryptionSalt(userId: string, resourceId: string, context: EncryptionContext): string {
    const saltMaterial = `${userId}:${resourceId}:${context}:${this.version}`
    return this.hashData(saltMaterial)
  }
  
  /**
   * Encrypt JSON data
   */
  async encryptJSON<T>(
    data: T,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<string> {
    return this.encryptData(JSON.stringify(data), userId, context, additionalEntropy)
  }
  
  /**
   * Decrypt JSON data
   */
  async decryptJSON<T>(
    encryptedData: string,
    userId: string,
    context: EncryptionContext,
    additionalEntropy?: string
  ): Promise<T> {
    const decryptedString = await this.decryptData(encryptedData, userId, context, additionalEntropy)
    return JSON.parse(decryptedString)
  }
  
  /**
   * Encrypt multiple items in batch
   */
  async encryptBatch(
    items: Array<{ data: string; entropy?: string }>,
    userId: string,
    context: EncryptionContext
  ): Promise<string[]> {
    return Promise.all(
      items.map(item => this.encryptData(item.data, userId, context, item.entropy))
    )
  }
  
  /**
   * Decrypt multiple items in batch
   */
  async decryptBatch(
    encryptedItems: Array<{ data: string; entropy?: string }>,
    userId: string,
    context: EncryptionContext
  ): Promise<string[]> {
    return Promise.all(
      encryptedItems.map(item => this.decryptData(item.data, userId, context, item.entropy))
    )
  }

  // ============================================================================
  // CHAT-SPECIFIC METHODS
  // ============================================================================

  /**
   * Encrypt a chat session
   */
  async encryptChatSession(session: ChatSession): Promise<EncryptedChatSession> {
    const encryptedMessages = await this.encryptJSON(
      session.messages,
      session.userId,
      'chat',
      session.id
    )

    return {
      id: session.id,
      userId: session.userId,
      title: session.title, // Keep title unencrypted for search/display
      messages: encryptedMessages,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      isArchived: session.isArchived
    }
  }

  /**
   * Decrypt a chat session
   */
  async decryptChatSession(encryptedSession: EncryptedChatSession): Promise<ChatSession> {
    const decryptedMessages = await this.decryptJSON<ChatMessage[]>(
      encryptedSession.messages,
      encryptedSession.userId,
      'chat',
      encryptedSession.id
    )

    return {
      id: encryptedSession.id,
      userId: encryptedSession.userId,
      title: encryptedSession.title,
      messages: decryptedMessages,
      createdAt: encryptedSession.createdAt,
      updatedAt: encryptedSession.updatedAt,
      isArchived: encryptedSession.isArchived
    }
  }

  /**
   * Decrypt multiple chat sessions
   */
  async decryptChatSessions(encryptedSessions: EncryptedChatSession[]): Promise<ChatSession[]> {
    const decryptedSessions: ChatSession[] = []
    
    for (const encryptedSession of encryptedSessions) {
      try {
        const decrypted = await this.decryptChatSession(encryptedSession)
        decryptedSessions.push(decrypted)
      } catch (error) {
        console.error(`Failed to decrypt chat session ${encryptedSession.id}:`, error)
        // Skip sessions that can't be decrypted rather than failing entirely
      }
    }
    
    return decryptedSessions
  }

  /**
   * Encrypt a single chat message
   */
  async encryptMessage(message: ChatMessage, userId: string, sessionId: string): Promise<string> {
    return this.encryptJSON(message, userId, 'chat', sessionId)
  }

  /**
   * Decrypt a single chat message
   */
  async decryptMessage(encryptedMessage: string, userId: string, sessionId: string): Promise<ChatMessage> {
    return this.decryptJSON<ChatMessage>(encryptedMessage, userId, 'chat', sessionId)
  }

  // ============================================================================
  // CONVENIENCE METHODS FOR OTHER DATA TYPES
  // ============================================================================

  /**
   * Encrypt calendar event data
   */
  async encryptCalendarData<T>(data: T, userId: string, eventId?: string): Promise<string> {
    return this.encryptJSON(data, userId, 'calendar', eventId)
  }

  /**
   * Decrypt calendar event data
   */
  async decryptCalendarData<T>(encryptedData: string, userId: string, eventId?: string): Promise<T> {
    return this.decryptJSON<T>(encryptedData, userId, 'calendar', eventId)
  }

  /**
   * Encrypt email analysis data
   */
  async encryptEmailAnalysis<T>(data: T, userId: string, emailId?: string): Promise<string> {
    return this.encryptJSON(data, userId, 'email-analysis', emailId)
  }

  /**
   * Decrypt email analysis data
   */
  async decryptEmailAnalysis<T>(encryptedData: string, userId: string, emailId?: string): Promise<T> {
    return this.decryptJSON<T>(encryptedData, userId, 'email-analysis', emailId)
  }

  /**
   * Encrypt draft data
   */
  async encryptDraft<T>(data: T, userId: string, draftId?: string): Promise<string> {
    return this.encryptJSON(data, userId, 'draft', draftId)
  }

  /**
   * Decrypt draft data
   */
  async decryptDraft<T>(encryptedData: string, userId: string, draftId?: string): Promise<T> {
    return this.decryptJSON<T>(encryptedData, userId, 'draft', draftId)
  }

  /**
   * Encrypt contact data
   */
  async encryptContact<T>(data: T, userId: string, contactId?: string): Promise<string> {
    return this.encryptJSON(data, userId, 'contact', contactId)
  }

  /**
   * Decrypt contact data
   */
  async decryptContact<T>(encryptedData: string, userId: string, contactId?: string): Promise<T> {
    return this.decryptJSON<T>(encryptedData, userId, 'contact', contactId)
  }

  /**
   * Encrypt campaign data
   */
  async encryptCampaign<T>(data: T, userId: string, campaignId?: string): Promise<string> {
    return this.encryptJSON(data, userId, 'campaign', campaignId)
  }

  /**
   * Decrypt campaign data
   */
  async decryptCampaign<T>(encryptedData: string, userId: string, campaignId?: string): Promise<T> {
    return this.decryptJSON<T>(encryptedData, userId, 'campaign', campaignId)
  }
}

// Export singleton instance
export const unifiedEncryption = new UnifiedEncryptionService()

// For backward compatibility, export the class and old interfaces
export { UnifiedEncryptionService as ModernEncryptionService }
export const chatEncryption = unifiedEncryption 