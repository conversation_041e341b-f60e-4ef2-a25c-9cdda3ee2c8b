'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Video, 
  FileText, 
  Download, 
  Search, 
  Calendar,
  Clock,
  User,
  Play,
  Pause
} from 'lucide-react'
import { toast } from 'sonner'

interface MeetingArtifactsProps {
  conferenceId: string
  className?: string
}

interface Recording {
  name: string
  driveDestination?: {
    file: string
    exportUri: string
  }
  state: 'RECORDING_STATE_UNSPECIFIED' | 'STARTED' | 'ENDED' | 'FILE_GENERATED'
  startTime: string
  endTime?: string
}

interface Transcript {
  name: string
  driveDestination?: {
    file: string
    exportUri: string
  }
  state: 'TRANSCRIPT_STATE_UNSPECIFIED' | 'STARTED' | 'ENDED' | 'FILE_GENERATED'
  startTime: string
  endTime?: string
}

interface TranscriptEntry {
  name: string
  participant: string
  text: string
  startTime: string
  endTime: string
  languageCode: string
}

export function MeetingArtifacts({ conferenceId, className }: MeetingArtifactsProps) {
  const [recordings, setRecordings] = useState<Recording[]>([])
  const [transcripts, setTranscripts] = useState<Transcript[]>([])
  const [transcriptEntries, setTranscriptEntries] = useState<TranscriptEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTranscript, setSelectedTranscript] = useState<string | null>(null)

  useEffect(() => {
    fetchArtifacts()
  }, [conferenceId])

  const fetchArtifacts = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch recordings and transcripts in parallel
      const [recordingsResponse, transcriptsResponse] = await Promise.all([
        fetch(`/api/meet/conferences/${encodeURIComponent(conferenceId)}/recordings?includeDownloadInfo=true`),
        fetch(`/api/meet/conferences/${encodeURIComponent(conferenceId)}/transcripts?includeDownloadInfo=true`)
      ])

      if (!recordingsResponse.ok || !transcriptsResponse.ok) {
        throw new Error('Failed to fetch artifacts')
      }

      const recordingsData = await recordingsResponse.json()
      const transcriptsData = await transcriptsResponse.json()

      setRecordings(recordingsData.data?.items || [])
      setTranscripts(transcriptsData.data?.items || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const fetchTranscriptEntries = async (transcriptId: string) => {
    try {
      const response = await fetch(
        `/api/meet/conferences/${encodeURIComponent(conferenceId)}/transcripts/${encodeURIComponent(transcriptId)}?type=entries`
      )

      if (!response.ok) {
        throw new Error('Failed to fetch transcript entries')
      }

      const data = await response.json()
      setTranscriptEntries(data.data?.items || [])
      setSelectedTranscript(transcriptId)
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to load transcript')
    }
  }

  const searchTranscript = async (transcriptId: string, query: string) => {
    if (!query.trim()) {
      fetchTranscriptEntries(transcriptId)
      return
    }

    try {
      const response = await fetch(
        `/api/meet/conferences/${encodeURIComponent(conferenceId)}/transcripts/${encodeURIComponent(transcriptId)}?type=search&q=${encodeURIComponent(query)}`
      )

      if (!response.ok) {
        throw new Error('Search failed')
      }

      const data = await response.json()
      setTranscriptEntries(data.data?.entries || [])
      toast.success(`Found ${data.data?.totalMatches || 0} matches`)
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Search failed')
    }
  }

  const downloadArtifact = async (downloadUri: string, filename: string) => {
    try {
      const link = document.createElement('a')
      link.href = downloadUri
      link.download = filename
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      toast.success('Download started')
    } catch (err) {
      toast.error('Download failed')
    }
  }

  const formatDuration = (startTime: string, endTime?: string) => {
    if (!endTime) return 'In progress'
    
    const start = new Date(startTime)
    const end = new Date(endTime)
    const duration = Math.round((end.getTime() - start.getTime()) / 1000 / 60)
    
    return `${duration} min`
  }

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString()
  }

  const getStateColor = (state: string) => {
    switch (state) {
      case 'FILE_GENERATED':
        return 'bg-green-100 text-green-800'
      case 'STARTED':
        return 'bg-blue-100 text-blue-800'
      case 'ENDED':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h2 className="text-2xl font-bold mb-2">Meeting Artifacts</h2>
        <p className="text-muted-foreground">
          Recordings and transcripts from this meeting
        </p>
      </div>

      <Tabs defaultValue="recordings" className="space-y-4">
        <TabsList>
          <TabsTrigger value="recordings" className="flex items-center gap-2">
            <Video className="h-4 w-4" />
            Recordings ({recordings.length})
          </TabsTrigger>
          <TabsTrigger value="transcripts" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Transcripts ({transcripts.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="recordings" className="space-y-4">
          {recordings.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  <Video className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No recordings available for this meeting</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {recordings.map((recording, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Video className="h-5 w-5" />
                        Recording {index + 1}
                      </CardTitle>
                      <Badge className={getStateColor(recording.state)}>
                        {recording.state.replace('RECORDING_STATE_', '').replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>Started: {formatTime(recording.startTime)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>Duration: {formatDuration(recording.startTime, recording.endTime)}</span>
                      </div>
                    </div>

                    {recording.driveDestination && (
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => downloadArtifact(
                            recording.driveDestination!.exportUri,
                            `recording-${index + 1}.mp4`
                          )}
                          className="flex items-center gap-2"
                        >
                          <Download className="h-4 w-4" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(recording.driveDestination!.file, '_blank')}
                          className="flex items-center gap-2"
                        >
                          <Play className="h-4 w-4" />
                          View in Drive
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="transcripts" className="space-y-4">
          {transcripts.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No transcripts available for this meeting</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {transcripts.map((transcript, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Transcript {index + 1}
                      </CardTitle>
                      <Badge className={getStateColor(transcript.state)}>
                        {transcript.state.replace('TRANSCRIPT_STATE_', '').replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>Started: {formatTime(transcript.startTime)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>Duration: {formatDuration(transcript.startTime, transcript.endTime)}</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchTranscriptEntries(transcript.name)}
                        className="flex items-center gap-2"
                      >
                        <FileText className="h-4 w-4" />
                        View Transcript
                      </Button>
                      {transcript.driveDestination && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadArtifact(
                              transcript.driveDestination!.exportUri,
                              `transcript-${index + 1}.txt`
                            )}
                            className="flex items-center gap-2"
                          >
                            <Download className="h-4 w-4" />
                            Download
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(transcript.driveDestination!.file, '_blank')}
                            className="flex items-center gap-2"
                          >
                            <FileText className="h-4 w-4" />
                            View in Drive
                          </Button>
                        </>
                      )}
                    </div>

                    {/* Transcript Entries */}
                    {selectedTranscript === transcript.name && (
                      <div className="border-t pt-4 space-y-4">
                        <div className="flex gap-2">
                          <Input
                            placeholder="Search transcript..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="flex-1"
                          />
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => searchTranscript(transcript.name, searchQuery)}
                          >
                            <Search className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="max-h-96 overflow-y-auto space-y-2">
                          {transcriptEntries.map((entry, entryIndex) => (
                            <div key={entryIndex} className="p-3 bg-muted rounded-lg">
                              <div className="flex items-center gap-2 mb-2 text-sm text-muted-foreground">
                                <User className="h-3 w-3" />
                                <span>{entry.participant}</span>
                                <span>•</span>
                                <span>{formatTime(entry.startTime)}</span>
                              </div>
                              <p className="text-sm">{entry.text}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
