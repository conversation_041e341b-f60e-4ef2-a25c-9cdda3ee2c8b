const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testUserBehaviorUpdates() {
  let testUser = null
  
  try {
    console.log('Testing UserBehavior field updates...')
    
    // Create a test user
    testUser = await prisma.user.create({
      data: {
        email: 'test-behavior-' + Date.now() + '@example.com',
        name: 'Test User Behavior'
      }
    })
    
    console.log('✅ Test user created:', testUser.id)
    
    // Create initial UserBehavior record
    const userBehavior = await prisma.userBehavior.create({
      data: {
        userId: testUser.id,
        totalEmails: 0,
        totalMeetings: 0,
        totalTasks: 0,
        totalChatMessages: 0,
        encryptionSalt: 'test_salt_' + Date.now()
      }
    })
    
    console.log('✅ UserBehavior created successfully')
    
    // Test incrementing email count
    const updatedBehavior = await prisma.userBehavior.update({
      where: { userId: testUser.id },
      data: {
        totalEmails: { increment: 1 },
        totalMeetings: { increment: 1 },
        totalTasks: { increment: 1 },
        totalChatMessages: { increment: 1 }
      }
    })
    
    console.log('✅ UserBehavior counters incremented successfully')
    
    if (updatedBehavior.totalEmails === 1 && 
        updatedBehavior.totalMeetings === 1 && 
        updatedBehavior.totalTasks === 1 && 
        updatedBehavior.totalChatMessages === 1) {
      console.log('✅ All counter fields working correctly')
      console.log('🎉 UserBehavior schema update successful!')
    } else {
      console.log('❌ Counter fields not working as expected')
    }
    
  } catch (error) {
    console.error('❌ Error with UserBehavior:', error.message)
    if (error.message.includes('Unknown argument `totalEmails`') || 
        error.message.includes('Unknown argument `totalMeetings`')) {
      console.error('❌ New counter fields not in schema')
    }
  } finally {
    // Clean up
    try {
      if (testUser) {
        await prisma.user.delete({
          where: { id: testUser.id }
        })
        console.log('✅ Test user cleaned up')
      }
    } catch (cleanupError) {
      console.error('Warning: Cleanup failed:', cleanupError.message)
    }
    
    await prisma.$disconnect()
  }
}

testUserBehaviorUpdates() 