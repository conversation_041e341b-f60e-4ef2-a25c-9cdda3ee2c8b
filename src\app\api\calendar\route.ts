import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { getCalendarClient } from '@/lib/gmail/client'
import { CalendarCacheHelper } from '@/lib/cache/calendarCacheHelper'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any

    console.log('Calendar API - Session:', session?.user?.id)

    if (!session?.user?.id) {
      console.log('Calendar API - No session or user ID')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const timeMin = searchParams.get('timeMin') || new Date().toISOString()
    const timeMax = searchParams.get('timeMax')
    const maxResults = parseInt(searchParams.get('maxResults') || '50')
    const singleEvents = searchParams.get('singleEvents') !== 'false'
    const orderBy = searchParams.get('orderBy') || 'startTime'

    console.log('Calendar API - Params:', { timeMin, timeMax, maxResults })

    // Create cache key based on parameters
    const cacheKey = `events_${timeMin}_${timeMax || 'nomax'}_${maxResults}_${orderBy}`

    // Try to get from cache first
    const cachedData = CalendarCacheHelper.getCachedCalendarEvents(cacheKey)
    if (cachedData) {
      console.log('Calendar API - Returning cached data')
      return NextResponse.json({
        events: cachedData.events,
        stats: {
          totalEvents: cachedData.totalCount,
          thisWeekEvents: cachedData.events.filter(event => {
            const eventDate = new Date(event.start?.dateTime || event.start?.date || '')
            const now = new Date()
            const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
            return eventDate >= now && eventDate <= weekFromNow
          }).length,
          upcomingEvents: cachedData.events.filter(event => {
            const eventDate = new Date(event.start?.dateTime || event.start?.date || '')
            return eventDate >= new Date()
          }).length,
          calendarsCount: 1
        },
        count: cachedData.totalCount,
        cached: true
      })
    }

    try {
      console.log('Calendar API - Getting calendar client for user:', session.user.id)
      const { calendar } = await getCalendarClient(session.user.id)

      const listParams: any = {
        calendarId: 'primary',
        timeMin: timeMin,
        maxResults: maxResults,
        singleEvents: singleEvents,
        orderBy: orderBy,
      }

      if (timeMax) {
        listParams.timeMax = timeMax
      }

      console.log('Calendar API - Calling calendar.events.list with params:', listParams)
      const response = await calendar.events.list(listParams)
      const events = response.data.items || []

      console.log('Calendar API - Success, found', events.length, 'events')

      // Cache the events
      CalendarCacheHelper.cacheCalendarEvents(cacheKey, events, timeMin, timeMax || undefined, 'primary')

      // Calculate basic stats
      const now = new Date()
      const stats = {
        totalEvents: events.length,
        thisWeekEvents: events.filter(event => {
          const eventDate = new Date(event.start?.dateTime || event.start?.date || '')
          const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
          return eventDate >= now && eventDate <= weekFromNow
        }).length,
        upcomingEvents: events.filter(event => {
          const eventDate = new Date(event.start?.dateTime || event.start?.date || '')
          return eventDate >= now
        }).length,
        calendarsCount: 1
      }

      return NextResponse.json({
        events: events,
        stats: stats,
        count: events.length,
        nextPageToken: response.data.nextPageToken,
        summary: response.data.summary
      })

    } catch (error) {
      console.error('Google Calendar API error:', error)
      
      // Check if it's a connection/auth error
      if (error instanceof Error) {
        if (error.message.includes('not connected') || 
            error.message.includes('insufficient authentication') ||
            error.message.includes('invalid_grant')) {
          return NextResponse.json(
            { error: 'Calendar not connected. Please reconnect your Google Calendar.' },
            { status: 403 }
          )
        }
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch calendar events. Please try again.' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Calendar fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch calendar events' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const eventData = await request.json()

    // Validate required fields
    if (!eventData.summary && !eventData.title) {
      return NextResponse.json({ error: 'Event title/summary is required' }, { status: 400 })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Map our event data to Google Calendar format
      const googleEvent: any = {
        summary: eventData.summary || eventData.title,
        description: eventData.description,
        start: {
          dateTime: eventData.start?.dateTime || eventData.startDate,
          timeZone: eventData.timeZone || eventData.start?.timeZone || 'UTC',
        },
        end: {
          dateTime: eventData.end?.dateTime || eventData.endDate || 
                   new Date(new Date(eventData.start?.dateTime || eventData.startDate).getTime() + 60 * 60 * 1000).toISOString(),
          timeZone: eventData.timeZone || eventData.end?.timeZone || 'UTC',
        },
        location: eventData.location,
        attendees: eventData.attendees?.filter((attendee: any) => {
          // Handle both string emails and attendee objects
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return email && email.trim() && email.includes('@')
        }).map((attendee: any) => {
          // Handle both string emails and attendee objects
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return { email: email.trim() }
        }) || [],
        reminders: {
          useDefault: false,
          overrides: eventData.reminders || [
            { method: 'email', minutes: 60 },
            { method: 'popup', minutes: 10 }
          ]
        }
      }

      // Handle all-day events
      if (eventData.isAllDay) {
        googleEvent.start = {
          date: eventData.startDate ? new Date(eventData.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        }
        googleEvent.end = {
          date: eventData.endDate ? new Date(eventData.endDate).toISOString().split('T')[0] : 
                new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        }
      }

      // Add conference data for Google Meet if requested
      if (eventData.addMeet) {
        if (eventData.meetSpaceName) {
          // Use existing Meet space
          googleEvent.conferenceData = {
            conferenceSolution: {
              key: { type: 'hangoutsMeet' },
              name: 'Google Meet',
              iconUri: 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png'
            },
            conferenceId: eventData.meetSpaceName,
            entryPoints: [{
              entryPointType: 'video',
              uri: eventData.meetUri || `https://meet.google.com/${eventData.meetCode}`,
              label: eventData.meetCode
            }]
          }
        } else {
          // Create new Meet link (legacy method)
          googleEvent.conferenceData = {
            createRequest: {
              requestId: `meet-${Date.now()}`,
              conferenceSolutionKey: { type: 'hangoutsMeet' }
            }
          }
        }
      }

      const insertParams: any = {
        calendarId: 'primary',
        requestBody: googleEvent,
        sendUpdates: 'all'
      }

      if (eventData.addMeet) {
        insertParams.conferenceDataVersion = 1
      }

      const response = await calendar.events.insert(insertParams)

      // Invalidate calendar cache after creating event
      CalendarCacheHelper.invalidateCalendarCache()

      return NextResponse.json({
        event: response.data,
        message: 'Event created successfully'
      })

    } catch (error) {
      console.error('Google Calendar create error:', error)
      return NextResponse.json(
        { error: 'Failed to create calendar event. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar create error:', error)
    return NextResponse.json(
      { error: 'Failed to create calendar event' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    const eventData = await request.json()

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      // Get existing event first
      const existingEvent = await calendar.events.get({
        calendarId: 'primary',
        eventId: eventId
      })

      // Update the event with new data
      const updatedEvent = {
        ...existingEvent.data,
        summary: eventData.summary || eventData.title || existingEvent.data.summary,
        description: eventData.description !== undefined ? eventData.description : existingEvent.data.description,
        location: eventData.location !== undefined ? eventData.location : existingEvent.data.location,
      }

      if (eventData.startDate || eventData.start) {
        updatedEvent.start = {
          dateTime: eventData.startDate || eventData.start?.dateTime,
          timeZone: eventData.timeZone || 'UTC',
        }
      }

      if (eventData.endDate || eventData.end) {
        updatedEvent.end = {
          dateTime: eventData.endDate || eventData.end?.dateTime,
          timeZone: eventData.timeZone || 'UTC',
        }
      }

      if (eventData.attendees) {
        updatedEvent.attendees = eventData.attendees.filter((attendee: any) => {
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return email && email.trim() && email.includes('@')
        }).map((attendee: any) => {
          const email = typeof attendee === 'string' ? attendee : attendee?.email
          return { email: email.trim() }
        })
      }

      const response = await calendar.events.update({
        calendarId: 'primary',
        eventId: eventId,
        requestBody: updatedEvent,
        sendUpdates: 'all'
      })

      // Invalidate calendar cache after updating event
      CalendarCacheHelper.invalidateCalendarCache()

      return NextResponse.json({
        event: response.data,
        message: 'Event updated successfully'
      })

    } catch (error) {
      console.error('Google Calendar update error:', error)
      return NextResponse.json(
        { error: 'Failed to update calendar event. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar update error:', error)
    return NextResponse.json(
      { error: 'Failed to update calendar event' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')

    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 })
    }

    try {
      const { calendar } = await getCalendarClient(session.user.id)

      await calendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
        sendUpdates: 'all'
      })

      // Invalidate calendar cache after deleting event
      CalendarCacheHelper.invalidateCalendarCache()

      return NextResponse.json({
        message: 'Event deleted successfully'
      })

    } catch (error) {
      console.error('Google Calendar delete error:', error)
      return NextResponse.json(
        { error: 'Failed to delete calendar event. Please check your permissions.' },
        { status: 403 }
      )
    }

  } catch (error) {
    console.error('Calendar delete error:', error)
    return NextResponse.json(
      { error: 'Failed to delete calendar event' },
      { status: 500 }
    )
  }
} 