import { NextRequest } from "next/server"
import {
  bulkDeletePermanently,
  withGmailAuth,
  formatBulkOperationResult
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { emailDeletePermanentlySchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function POST(request: NextRequest) {
  return withGmailA<PERSON>(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-email-delete-permanently', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, emailDeletePermanentlySchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-email-delete-permanently')
      throw new Error(`Invalid email delete data: ${validation.error}`)
    }

    const { emailIds } = validation.data

    // Delete emails permanently
    const result = await bulkDeletePermanently(user.id, emailIds)

    // End performance monitoring
    endPerformanceMetric('gmail-email-delete-permanently')

    return formatBulkOperationResult(
      emailIds.length,
      result.success,
      result.failed,
      'deleted permanently'
    )
  })
}
