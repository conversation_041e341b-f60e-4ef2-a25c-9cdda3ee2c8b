import { NextRequest } from "next/server"
import { 
  bulkDeletePermanently, 
  withGmailAuth, 
  validateRequiredFields,
  formatBulkOperationResult 
} from "@/lib/gmail"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['emailIds'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { emailIds } = body

    if (!Array.isArray(emailIds) || emailIds.length === 0) {
      throw new Error("emailIds must be a non-empty array")
    }

    // Delete emails permanently
    const result = await bulkDeletePermanently(user.id, emailIds)

    return formatBulkOperationResult(
      emailIds.length,
      result.success,
      result.failed,
      'deleted permanently'
    )
  })
}
