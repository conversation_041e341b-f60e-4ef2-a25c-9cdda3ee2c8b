import { NextRequest } from "next/server"
import { getArchivedEmails, withGmail<PERSON>uth } from "@/lib/gmail"
import { validateSearchParams } from "@/lib/validation/utils"
import { emailListParamsSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function GET(request: NextRequest) {
  return withGmailAuth(request, async ({ user, url }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-archive-fetch', { userId: user.id })

    // Validate search parameters using Zod
    const validation = validateSearchParams(url.searchParams, emailListParamsSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-archive-fetch')
      throw new Error(`Invalid parameters: ${validation.error}`)
    }

    const params = validation.data

    // Fetch archived emails from Gmail with validated parameters
    const result = await getArchivedEmails(
      user.id,
      params.limit,
      params.pageToken,
      params.page,
      params.dateFrom ? new Date(params.dateFrom) : undefined,
      params.dateTo ? new Date(params.dateTo) : undefined
    )

    // End performance monitoring
    const duration = endPerformanceMetric('gmail-archive-fetch')
    if (duration && duration > 3000) {
      console.warn(`Slow archive fetch: ${duration.toFixed(2)}ms for user ${user.id}`)
    }

    return result
  })
}