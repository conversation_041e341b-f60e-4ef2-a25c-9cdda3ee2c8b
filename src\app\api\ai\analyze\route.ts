import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { geminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { emails, userPrompt, days = 2 } = await request.json()

    if (!emails || !Array.isArray(emails)) {
      return NextResponse.json({ error: 'Invalid emails data' }, { status: 400 })
    }

    // Filter emails from the last N days
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)
    
    const recentEmails = emails.filter(email => {
      const emailDate = new Date(email.date)
      return emailDate >= cutoffDate
    })

    if (recentEmails.length === 0) {
      return NextResponse.json({ 
        analyses: [], 
        message: `No emails found in the last ${days} days` 
      })
    }

    // Analyze emails with Gemini
    const analyses = await geminiService.analyzeEmails(recentEmails, userPrompt)

    // Sort by priority (high to low)
    const sortedAnalyses = analyses.sort((a, b) => {
      const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 }
      return priorityOrder[b.importance] - priorityOrder[a.importance] || b.priority - a.priority
    })

    // Filter to show only important emails (high or medium importance, or priority >= 6)
    const importantAnalyses = sortedAnalyses.filter(analysis => 
      analysis.importance === 'high' || 
      analysis.importance === 'medium' || 
      analysis.priority >= 6
    )

    return NextResponse.json({
      analyses: importantAnalyses,
      totalAnalyzed: recentEmails.length,
      importantCount: importantAnalyses.length,
      dayRange: days
    })

  } catch (error) {
    console.error('AI Analysis Error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze emails' },
      { status: 500 }
    )
  }
} 