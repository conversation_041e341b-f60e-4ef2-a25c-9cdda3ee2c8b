import MeetAddonSidePanel from '@/components/meet/MeetAddonSidePanel'

export default function MeetAddonSidePanelPage() {
  // These would typically come from environment variables
  const cloudProjectNumber = process.env.NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_NUMBER || ''
  const mainStageUrl = process.env.NEXT_PUBLIC_BASE_URL 
    ? `${process.env.NEXT_PUBLIC_BASE_URL}/meet-addon/mainstage`
    : 'http://localhost:3000/meet-addon/mainstage'

  return (
    <html>
      <head>
        <title>Meet Add-on Side Panel</title>
        <script src="https://www.gstatic.com/meetjs/addons/1.1.0/meet.addons.js"></script>
      </head>
      <body style={{ width: '100%', height: '100%', margin: 0 }}>
        <MeetAddonSidePanel 
          cloudProjectNumber={cloudProjectNumber}
          mainStageUrl={mainStageUrl}
        />
      </body>
    </html>
  )
}
