import { getCalendarClient } from './gmail/client'
import { createMeetingSpace } from './meet'
import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API || "AIzaSyAwHr4qa8SLtb7RMqaSRFzgAo6YwpP2Ga0")
const model = genAI.getGenerativeModel({ model: process.env.GEMINI_MODEL || "gemini-2.5-flash-lite-preview-06-17" })

export interface MeetingRequest {
  title?: string
  description?: string
  attendees: string[]
  startTime: Date
  endTime: Date
  location?: string
  addMeetLink?: boolean
  meetAccessType?: 'OPEN' | 'TRUSTED' | 'RESTRICTED'
  timezone?: string
  reminders?: Array<{
    method: 'email' | 'popup'
    minutes: number
  }>
}

export interface MeetingScheduleRequest {
  context: string
  attendees?: string[]
  preferredDate?: string
  preferredTime?: string
  duration?: number // in minutes
  timezone?: string
  meetingType?: 'in-person' | 'virtual' | 'hybrid'
  location?: string
  addMeetLink?: boolean
}

export interface AvailabilityRequest {
  attendees: string[]
  startDate: Date
  endDate: Date
  duration: number // in minutes
  workingHours?: {
    start: string // "09:00"
    end: string   // "17:00"
  }
  timezone?: string
}

export interface TimeSlot {
  start: Date
  end: Date
  available: boolean
  attendeeAvailability: { [email: string]: boolean }
}

class AIAgentCalendarService {

  async parseAndScheduleMeeting(userId: string, request: MeetingScheduleRequest): Promise<{
    meetingDetails: MeetingRequest
    event?: any
    meetSpace?: any
    success: boolean
    error?: string
  }> {
    try {
      // Use AI to parse the meeting request and extract details
      const parsePrompt = `
      Parse the following meeting request and extract structured meeting details:

      Context: "${request.context}"
      ${request.attendees ? `Suggested Attendees: ${request.attendees.join(', ')}` : ''}
      ${request.preferredDate ? `Preferred Date: ${request.preferredDate}` : ''}
      ${request.preferredTime ? `Preferred Time: ${request.preferredTime}` : ''}
      ${request.duration ? `Duration: ${request.duration} minutes` : ''}
      ${request.timezone ? `Timezone: ${request.timezone}` : ''}
      ${request.meetingType ? `Meeting Type: ${request.meetingType}` : ''}
      ${request.location ? `Location: ${request.location}` : ''}

      Extract and return JSON with:
      {
        "title": "Meeting title/subject",
        "description": "Meeting description/agenda",
        "attendees": ["<EMAIL>", "<EMAIL>"],
        "startTime": "2024-01-15T14:00:00.000Z",
        "endTime": "2024-01-15T15:00:00.000Z",
        "location": "Meeting location or 'Virtual'",
        "addMeetLink": true/false,
        "timezone": "UTC or specified timezone",
        "duration": 60,
        "reminders": [
          {"method": "email", "minutes": 15},
          {"method": "popup", "minutes": 10}
        ]
      }

      Guidelines:
      - Generate appropriate meeting title from context
      - Extract or infer attendee emails
      - Parse date/time or suggest reasonable defaults
      - Default duration is 60 minutes if not specified
      - Add Meet link for virtual meetings
      - Include standard reminders
      - Use proper ISO date format for times
      `

      const result = await model.generateContent(parsePrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('Failed to parse meeting request')
      }

      const meetingDetails: MeetingRequest = JSON.parse(jsonMatch[0])

      // Validate and adjust meeting details
      meetingDetails.startTime = new Date(meetingDetails.startTime)
      meetingDetails.endTime = new Date(meetingDetails.endTime)
      
      // Override with explicit request parameters
      if (request.attendees) meetingDetails.attendees = request.attendees
      if (request.location) meetingDetails.location = request.location
      if (request.addMeetLink !== undefined) meetingDetails.addMeetLink = request.addMeetLink

      // Schedule the meeting
      const scheduleResult = await this.scheduleMeeting(userId, meetingDetails)

      return {
        meetingDetails,
        event: scheduleResult.event,
        meetSpace: scheduleResult.meetSpace,
        success: scheduleResult.success,
        error: scheduleResult.error
      }

    } catch (error) {
      console.error('Error parsing and scheduling meeting:', error)
      return {
        meetingDetails: {} as MeetingRequest,
        success: false,
        error: error instanceof Error ? error.message : 'Failed to schedule meeting'
      }
    }
  }

  async scheduleMeeting(userId: string, meeting: MeetingRequest): Promise<{
    event?: any
    meetSpace?: any
    success: boolean
    error?: string
  }> {
    try {
      const { calendar } = await getCalendarClient(userId)

      // Create Meet space if requested
      let meetSpace = null
      if (meeting.addMeetLink) {
        try {
          meetSpace = await createMeetingSpace(userId, {
            accessType: meeting.meetAccessType || 'OPEN',
            entryPointAccess: 'ALL'
          })
        } catch (meetError) {
          console.error('Failed to create Meet space:', meetError)
          // Continue with regular Meet link creation
        }
      }

      // Prepare calendar event
      const googleEvent: any = {
        summary: meeting.title,
        description: meeting.description,
        location: meeting.location,
        start: {
          dateTime: meeting.startTime.toISOString(),
          timeZone: meeting.timezone || 'UTC'
        },
        end: {
          dateTime: meeting.endTime.toISOString(),
          timeZone: meeting.timezone || 'UTC'
        },
        attendees: meeting.attendees.filter(email => 
          email && email.trim() && email.includes('@')
        ).map(email => ({ email: email.trim() })),
        reminders: meeting.reminders ? {
          useDefault: false,
          overrides: meeting.reminders
        } : {
          useDefault: true
        }
      }

      // Add Meet integration
      if (meeting.addMeetLink) {
        if (meetSpace) {
          // Add Meet space info to description
          const meetInfo = `\n\n--- Google Meet Information ---\nMeeting Link: ${meetSpace.meetingUri}\nMeeting Code: ${meetSpace.meetingCode}\nAccess Type: ${meeting.meetAccessType || 'OPEN'}`
          googleEvent.description = (googleEvent.description || '') + meetInfo
        } else {
          // Fallback to regular Meet link creation
          googleEvent.conferenceData = {
            createRequest: {
              requestId: `meet-${Date.now()}`,
              conferenceSolutionKey: { type: 'hangoutsMeet' }
            }
          }
        }
      }

      const insertParams: any = {
        calendarId: 'primary',
        requestBody: googleEvent,
        sendUpdates: 'all',
        conferenceDataVersion: 1
      }

      const response = await calendar.events.insert(insertParams)

      return {
        event: response.data,
        meetSpace: meetSpace ? {
          name: meetSpace.name,
          meetingUri: meetSpace.meetingUri,
          meetingCode: meetSpace.meetingCode,
          accessType: meeting.meetAccessType || 'OPEN'
        } : null,
        success: true
      }

    } catch (error) {
      console.error('Error scheduling meeting:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to schedule meeting'
      }
    }
  }

  async findAvailableSlots(userId: string, request: AvailabilityRequest): Promise<TimeSlot[]> {
    try {
      const { calendar } = await getCalendarClient(userId)

      // Prepare freebusy request
      const freeBusyRequest = {
        timeMin: request.startDate.toISOString(),
        timeMax: request.endDate.toISOString(),
        timeZone: request.timezone || 'UTC',
        items: [
          { id: 'primary' }, // User's primary calendar
          ...request.attendees.map(email => ({ id: email }))
        ]
      }

      const freeBusyResponse = await calendar.freebusy.query({
        requestBody: freeBusyRequest
      })

      const busyTimes = freeBusyResponse.data.calendars || {}
      const workingHours = request.workingHours || { start: '09:00', end: '17:00' }

      // Generate time slots
      const slots: TimeSlot[] = []
      const current = new Date(request.startDate)
      const end = new Date(request.endDate)

      while (current < end) {
        // Skip weekends (optional - could be configurable)
        if (current.getDay() === 0 || current.getDay() === 6) {
          current.setDate(current.getDate() + 1)
          current.setHours(parseInt(workingHours.start.split(':')[0]), parseInt(workingHours.start.split(':')[1]), 0, 0)
          continue
        }

        // Check working hours
        const currentHour = current.getHours()
        const currentMinute = current.getMinutes()
        const workStart = parseInt(workingHours.start.split(':')[0]) * 60 + parseInt(workingHours.start.split(':')[1])
        const workEnd = parseInt(workingHours.end.split(':')[0]) * 60 + parseInt(workingHours.end.split(':')[1])
        const currentTime = currentHour * 60 + currentMinute

        if (currentTime < workStart || currentTime >= workEnd) {
          // Move to next day or next working hour
          if (currentTime >= workEnd) {
            current.setDate(current.getDate() + 1)
            current.setHours(parseInt(workingHours.start.split(':')[0]), parseInt(workingHours.start.split(':')[1]), 0, 0)
          } else {
            current.setHours(parseInt(workingHours.start.split(':')[0]), parseInt(workingHours.start.split(':')[1]), 0, 0)
          }
          continue
        }

        const slotStart = new Date(current)
        const slotEnd = new Date(current.getTime() + request.duration * 60000)

        // Check if this slot conflicts with any busy times
        let available = true
        const attendeeAvailability: { [email: string]: boolean } = {}

        // Check user's availability
        const userBusyTimes = busyTimes['primary']?.busy || []
        for (const busyTime of userBusyTimes) {
          const busyStart = new Date(busyTime.start!)
          const busyEnd = new Date(busyTime.end!)
          
          if ((slotStart >= busyStart && slotStart < busyEnd) ||
              (slotEnd > busyStart && slotEnd <= busyEnd) ||
              (slotStart <= busyStart && slotEnd >= busyEnd)) {
            available = false
            break
          }
        }

        // Check attendees' availability
        for (const attendee of request.attendees) {
          const attendeeBusyTimes = busyTimes[attendee]?.busy || []
          let attendeeAvailable = true

          for (const busyTime of attendeeBusyTimes) {
            const busyStart = new Date(busyTime.start!)
            const busyEnd = new Date(busyTime.end!)
            
            if ((slotStart >= busyStart && slotStart < busyEnd) ||
                (slotEnd > busyStart && slotEnd <= busyEnd) ||
                (slotStart <= busyStart && slotEnd >= busyEnd)) {
              attendeeAvailable = false
              available = false
              break
            }
          }

          attendeeAvailability[attendee] = attendeeAvailable
        }

        slots.push({
          start: slotStart,
          end: slotEnd,
          available,
          attendeeAvailability
        })

        // Move to next 30-minute slot
        current.setTime(current.getTime() + 30 * 60000)
      }

      // Return only available slots, limited to reasonable number
      return slots.filter(slot => slot.available).slice(0, 20)

    } catch (error) {
      console.error('Error finding available slots:', error)
      return []
    }
  }

  async suggestMeetingTimes(userId: string, context: string, attendees: string[], duration: number = 60): Promise<{
    suggestions: Array<{
      start: Date
      end: Date
      confidence: number
      reason: string
    }>
    success: boolean
    error?: string
  }> {
    try {
      // Get availability for the next 7 days
      const startDate = new Date()
      const endDate = new Date()
      endDate.setDate(endDate.getDate() + 7)

      const availableSlots = await this.findAvailableSlots(userId, {
        attendees,
        startDate,
        endDate,
        duration,
        workingHours: { start: '09:00', end: '17:00' }
      })

      // Use AI to suggest the best times based on context
      const suggestionPrompt = `
      Based on the meeting context and available time slots, suggest the best meeting times:

      Meeting Context: "${context}"
      Duration: ${duration} minutes
      Attendees: ${attendees.join(', ')}

      Available Time Slots:
      ${availableSlots.slice(0, 10).map((slot, index) => 
        `${index + 1}. ${slot.start.toISOString()} - ${slot.end.toISOString()}`
      ).join('\n')}

      Suggest the top 3 time slots with confidence scores and reasons. Consider:
      - Meeting urgency from context
      - Optimal meeting times (avoid early morning, late afternoon)
      - Day of week preferences
      - Meeting type and duration

      Return JSON:
      {
        "suggestions": [
          {
            "start": "2024-01-15T14:00:00.000Z",
            "end": "2024-01-15T15:00:00.000Z",
            "confidence": 0.9,
            "reason": "Optimal afternoon time, all attendees available"
          }
        ]
      }
      `

      const result = await model.generateContent(suggestionPrompt)
      const response = await result.response
      const text = response.text()

      const jsonMatch = text.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[0])
        return {
          suggestions: suggestions.suggestions.map((s: any) => ({
            ...s,
            start: new Date(s.start),
            end: new Date(s.end)
          })),
          success: true
        }
      }

      // Fallback: return first 3 available slots
      return {
        suggestions: availableSlots.slice(0, 3).map(slot => ({
          start: slot.start,
          end: slot.end,
          confidence: 0.7,
          reason: 'Available time slot'
        })),
        success: true
      }

    } catch (error) {
      console.error('Error suggesting meeting times:', error)
      return {
        suggestions: [],
        success: false,
        error: error instanceof Error ? error.message : 'Failed to suggest meeting times'
      }
    }
  }

  async getCalendarInsights(userId: string, days: number = 7): Promise<{
    totalEvents: number
    upcomingMeetings: number
    busyHours: Array<{ hour: number; count: number }>
    meetingTypes: { [type: string]: number }
    averageMeetingDuration: number
  }> {
    try {
      const { calendar } = await getCalendarClient(userId)

      const timeMin = new Date()
      const timeMax = new Date()
      timeMax.setDate(timeMax.getDate() + days)

      const response = await calendar.events.list({
        calendarId: 'primary',
        timeMin: timeMin.toISOString(),
        timeMax: timeMax.toISOString(),
        singleEvents: true,
        orderBy: 'startTime'
      })

      const events = response.data.items || []

      // Calculate insights
      const totalEvents = events.length
      const upcomingMeetings = events.filter(event => 
        event.attendees && event.attendees.length > 1
      ).length

      // Busy hours analysis
      const hourCounts: { [hour: number]: number } = {}
      let totalDuration = 0
      let meetingCount = 0

      events.forEach(event => {
        if (event.start?.dateTime) {
          const startHour = new Date(event.start.dateTime).getHours()
          hourCounts[startHour] = (hourCounts[startHour] || 0) + 1

          if (event.end?.dateTime) {
            const duration = new Date(event.end.dateTime).getTime() - new Date(event.start.dateTime).getTime()
            totalDuration += duration
            meetingCount++
          }
        }
      })

      const busyHours = Object.entries(hourCounts)
        .map(([hour, count]) => ({ hour: parseInt(hour), count }))
        .sort((a, b) => b.count - a.count)

      const averageMeetingDuration = meetingCount > 0 ? totalDuration / meetingCount / (1000 * 60) : 0

      return {
        totalEvents,
        upcomingMeetings,
        busyHours,
        meetingTypes: { 'meetings': upcomingMeetings, 'events': totalEvents - upcomingMeetings },
        averageMeetingDuration
      }

    } catch (error) {
      console.error('Error getting calendar insights:', error)
      throw error
    }
  }
}

export const aiAgentCalendarService = new AIAgentCalendarService()
