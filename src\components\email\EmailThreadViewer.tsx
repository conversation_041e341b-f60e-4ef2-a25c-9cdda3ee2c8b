"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  Archive,
  Trash2,
  Reply,
  ReplyAll,
  Forward,
  Star,
  MoreVertical,
  Download,
  Flag,
  Mail,
  Paperclip,
  RefreshCw,
  Inbox,
  Send,
  AlertCircle,
  Hash,
  Folder,
  Tag,
  ChevronDown,
  ChevronUp,
  Clock,
  FileText
} from "lucide-react"
import EmailRenderer from "./EmailRenderer"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { EmailThreadViewerProps, ThreadMessage, EmailAttachment, EmailLabel } from './types'

// Attachment list component (reused from EmailViewer)
function AttachmentList({ attachments, emailId }: { attachments: EmailAttachment[], emailId: string }) {
  if (!attachments || attachments.length === 0) return null

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
      <div className="flex items-center gap-2 mb-2">
        <Paperclip className="h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">
          {attachments.length} attachment{attachments.length > 1 ? 's' : ''}
        </span>
      </div>
      <div className="space-y-2">
        {attachments.map((attachment, index) => (
          <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                <Paperclip className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium truncate text-gray-900">{attachment.filename}</p>
                <p className="text-xs text-gray-500">{formatFileSize(attachment.size)}</p>
              </div>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              className="flex-shrink-0 ml-2"
              onClick={() => {
                window.open(`/api/gmail/inbox/${emailId}/attachment/${attachment.attachmentId}`, '_blank')
              }}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}

// Label icons component (reused from EmailViewer)
function EmailLabelIcons({ labels }: { labels: EmailLabel[] }) {
  if (!labels || labels.length === 0) return null

  const getLabelStyle = (labelName: string, labelType: string) => {
    const name = labelName.toUpperCase()
    
    if (labelType === 'system') {
      switch (name) {
        case 'INBOX':
          return { icon: Inbox, color: 'text-blue-600', bg: 'bg-blue-100' }
        case 'SENT':
          return { icon: Send, color: 'text-green-600', bg: 'bg-green-100' }
        case 'DRAFT':
          return { icon: FileText, color: 'text-orange-600', bg: 'bg-orange-100' }
        case 'SPAM':
          return { icon: AlertCircle, color: 'text-red-600', bg: 'bg-red-100' }
        case 'TRASH':
          return { icon: Trash2, color: 'text-gray-600', bg: 'bg-gray-100' }
        case 'STARRED':
          return { icon: Star, color: 'text-yellow-600', bg: 'bg-yellow-100' }
        case 'IMPORTANT':
          return { icon: Flag, color: 'text-red-600', bg: 'bg-red-100' }
        case 'CATEGORY_PERSONAL':
          return { icon: Hash, color: 'text-purple-600', bg: 'bg-purple-100' }
        case 'CATEGORY_SOCIAL':
          return { icon: Hash, color: 'text-blue-600', bg: 'bg-blue-100' }
        case 'CATEGORY_PROMOTIONS':
          return { icon: Tag, color: 'text-green-600', bg: 'bg-green-100' }
        case 'CATEGORY_UPDATES':
          return { icon: RefreshCw, color: 'text-orange-600', bg: 'bg-orange-100' }
        case 'CATEGORY_FORUMS':
          return { icon: Hash, color: 'text-indigo-600', bg: 'bg-indigo-100' }
        default:
          return { icon: Folder, color: 'text-gray-600', bg: 'bg-gray-100' }
      }
    } else {
      return { icon: Tag, color: 'text-purple-600', bg: 'bg-purple-100' }
    }
  }

  const visibleLabels = labels.filter(label => 
    !['UNREAD', 'INBOX'].includes(label.name.toUpperCase())
  ).slice(0, 3)

  return (
    <div className="flex items-center gap-1">
      {visibleLabels.map((label, index) => {
        const { icon: Icon, color, bg } = getLabelStyle(label.name, label.type)
        return (
          <TooltipProvider key={index}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className={`p-1 rounded ${bg}`}>
                  <Icon className={`h-3 w-3 ${color}`} />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{label.name}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )
      })}
      {labels.length > 3 && (
        <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-5">
          +{labels.length - 3}
        </Badge>
      )}
    </div>
  )
}

// Individual thread message component
function ThreadMessageItem({ 
  message, 
  isExpanded, 
  onToggle, 
  onAction,
  showActions = true,
  isLatest = false
}: {
  message: ThreadMessage
  isExpanded: boolean
  onToggle: () => void
  onAction?: (action: string, messageId: string) => void
  showActions?: boolean
  isLatest?: boolean
}) {
  const [actionLoading, setActionLoading] = useState(false)

  const handleAction = async (action: string) => {
    setActionLoading(true)
    try {
      onAction?.(action, message.id)
    } finally {
      setActionLoading(false)
    }
  }

  const extractSenderName = (fromHeader: string) => {
    const match = fromHeader.match(/^(.+?)\s*<.*>$/)
    return match ? match[1].trim().replace(/"/g, '') : fromHeader
  }

  const formatEmailAddresses = (addresses: string) => {
    if (!addresses) return ''
    
    return addresses.split(',').map(addr => {
      const trimmed = addr.trim()
      const match = trimmed.match(/^(.+?)\s*<.*>$/)
      if (match) {
        return match[1].replace(/"/g, '')
      }
      return trimmed
    }).join(', ')
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const messageDate = new Date(date)
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffInHours < 24 * 7) {
      return messageDate.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' })
    } else {
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' })
    }
  }

  return (
    <div className={`border-b border-gray-100 ${isLatest ? 'bg-white' : 'bg-gray-50/50'}`}>
      {/* Message Header - Always Visible */}
      <div 
        className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="text-xs font-medium bg-gray-100 text-gray-700">
                {extractSenderName(message.from).charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className={`text-sm ${!message.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`}>
                  {extractSenderName(message.from)}
                </span>
                {message.labels && message.labels.length > 0 && (
                  <EmailLabelIcons labels={message.labels} />
                )}
                {!message.isRead && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                )}
              </div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-gray-500 truncate">
                  {isExpanded ? `To: ${formatEmailAddresses(message.to || '')}` : message.snippet}
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500 whitespace-nowrap">
                    {formatDate(new Date(message.date))}
                  </span>
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Message Content */}
      {isExpanded && (
        <div className="px-4 pb-4">
          {/* Message Details */}
          <div className="mb-4 text-xs text-gray-600 space-y-1">
            <div><strong>From:</strong> {message.from}</div>
            {message.to && <div><strong>To:</strong> {message.to}</div>}
            {message.cc && <div><strong>CC:</strong> {message.cc}</div>}
            {message.bcc && <div><strong>BCC:</strong> {message.bcc}</div>}
            <div><strong>Date:</strong> {new Date(message.date).toLocaleString()}</div>
          </div>

          {/* Message Content */}
          <div className="mb-4">
            <EmailRenderer 
              body={message.body} 
              enableQuoteToggle={true}
              className="prose prose-sm max-w-none"
            />
          </div>

          {/* Attachments */}
          <AttachmentList attachments={message.attachments || []} emailId={message.id} />

          {/* Message Actions */}
          {showActions && (
            <div className="flex items-center gap-2 mt-4 pt-3 border-t border-gray-100">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAction('reply')}
                disabled={actionLoading}
                className="flex items-center gap-2"
              >
                <Reply className="h-4 w-4" />
                Reply
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAction('replyAll')}
                disabled={actionLoading}
                className="flex items-center gap-2"
              >
                <ReplyAll className="h-4 w-4" />
                Reply All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleAction('forward')}
                disabled={actionLoading}
                className="flex items-center gap-2"
              >
                <Forward className="h-4 w-4" />
                Forward
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Main EmailThreadViewer component
export default function EmailThreadViewer({ 
  threadId,
  initialEmail,
  onAction,
  showActions = true,
  className = "",
  showAllMessages = false,
  expandedMessageIds = new Set(),
  onToggleMessage
}: EmailThreadViewerProps) {
  const [thread, setThread] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [localExpandedIds, setLocalExpandedIds] = useState<Set<string>>(
    showAllMessages ? new Set() : new Set([initialEmail?.id].filter(Boolean) as string[])
  )

  // Use external expanded state if provided, otherwise use local state
  const expandedIds = onToggleMessage ? expandedMessageIds : localExpandedIds
  const toggleMessage = onToggleMessage || ((messageId: string) => {
    setLocalExpandedIds(prev => {
      const newSet = new Set(prev)
      if (newSet.has(messageId)) {
        newSet.delete(messageId)
      } else {
        newSet.add(messageId)
      }
      return newSet
    })
  })

  useEffect(() => {
    const fetchThread = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/gmail/threads/${threadId}`)
        if (!response.ok) {
          throw new Error('Failed to fetch thread')
        }
        const threadData = await response.json()
        setThread(threadData)
        
        // If showAllMessages is true, expand all messages
        if (showAllMessages) {
          setLocalExpandedIds(new Set(threadData.messages.map((msg: ThreadMessage) => msg.id)))
        } else if (threadData.messages.length > 0) {
          // By default, expand the latest message
          const latestMessage = threadData.messages[threadData.messages.length - 1]
          setLocalExpandedIds(new Set([latestMessage.id]))
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load thread')
      } finally {
        setLoading(false)
      }
    }

    fetchThread()
  }, [threadId, showAllMessages])

  if (loading) {
    return (
      <div className={`bg-white h-full flex items-center justify-center ${className}`}>
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading conversation...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-white h-full flex items-center justify-center ${className}`}>
        <div className="text-center">
          <AlertCircle className="h-8 w-8 mx-auto mb-4 text-red-400" />
          <p className="text-red-600 mb-2">Failed to load conversation</p>
          <p className="text-gray-500 text-sm">{error}</p>
        </div>
      </div>
    )
  }

  if (!thread || !thread.messages || thread.messages.length === 0) {
    return (
      <div className={`bg-white h-full flex items-center justify-center ${className}`}>
        <div className="text-center">
          <Mail className="h-8 w-8 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">No messages found in this conversation</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white h-full flex flex-col ${className}`}>
      {/* Thread Header */}
      <div className="border-b border-gray-100 p-4 flex-shrink-0">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-lg font-semibold text-gray-900 truncate">
            {thread.messages[0]?.subject || '(No Subject)'}
          </h1>
          <Badge variant="secondary" className="ml-2">
            {thread.messageCount} message{thread.messageCount > 1 ? 's' : ''}
          </Badge>
        </div>
        <p className="text-sm text-gray-500">
          Conversation with {thread.messageCount} message{thread.messageCount > 1 ? 's' : ''}
        </p>
      </div>

      {/* Thread Messages */}
      <div className="flex-1 overflow-auto">
        {thread.messages.map((message: ThreadMessage, index: number) => (
          <ThreadMessageItem
            key={message.id}
            message={message}
            isExpanded={expandedIds.has(message.id)}
            onToggle={() => toggleMessage(message.id)}
            onAction={onAction}
            showActions={showActions}
            isLatest={index === thread.messages.length - 1}
          />
        ))}
      </div>
    </div>
  )
}
