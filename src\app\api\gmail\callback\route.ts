import { NextRequest, NextResponse } from "next/server"
import { google } from 'googleapis'
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')

    if (error) {
      console.error('Gmail OAuth error:', error)
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard?gmail_error=${encodeURIComponent(error)}`)
    }

    if (!code || !state) {
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard?gmail_error=missing_parameters`)
    }

    // Create OAuth2 client
    const oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      `${process.env.NEXTAUTH_URL}/api/gmail/callback`
    )

    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code)
    
    if (!tokens.refresh_token) {
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard?gmail_error=no_refresh_token`)
    }

    // Find user by email (from state parameter)
    const user = await prisma.user.findUnique({
      where: { email: state }
    })

    if (!user) {
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard?gmail_error=user_not_found`)
    }

    // Update user with Gmail tokens
    await prisma.user.update({
      where: { id: user.id },
      data: {
        gmailRefreshToken: tokens.refresh_token,
        gmailConnected: true,
        dailySendLimit: 250, // Gmail's default daily limit
        dailySendCount: 0,
        lastSendReset: new Date()
      }
    })

    console.log('✅ Gmail connected successfully for user:', state)
    
    return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard?gmail_connected=true`)
  } catch (error) {
    console.error("Error in Gmail callback:", error)
    return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/dashboard?gmail_error=callback_error`)
  }
} 