import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetU<PERSON>, 
  handleMeetApiError, 
  formatMeetApiResponse,
  validatePaginationParams,
  listParticipants,
  getConferenceParticipants,
  getParticipantStatistics,
  getParticipantAttendance,
  searchParticipants
} from "@/lib/meet"

interface RouteParams {
  params: {
    conferenceId: string
  }
}

/**
 * GET /api/meet/conferences/[conferenceId]/participants - Get conference participants
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { conferenceId } = params
    
    // Decode the conference ID in case it's URL encoded
    const decodedConferenceId = decodeURIComponent(conferenceId)
    const searchParams = url.searchParams

    // Handle different query types
    const queryType = searchParams.get('type') || 'list'

    switch (queryType) {
      case 'statistics':
        return await handleParticipantStatistics(user.id, decodedConferenceId)
      
      case 'attendance':
        return await handleParticipantAttendance(user.id, decodedConferenceId)
      
      case 'search':
        return await handleParticipantSearch(user.id, decodedConferenceId, searchParams)
      
      case 'detailed':
        return await handleDetailedParticipants(user.id, decodedConferenceId, searchParams)
      
      case 'list':
      default:
        return await handleListParticipants(user.id, decodedConferenceId, searchParams)
    }
  } catch (error) {
    return handleMeetApiError(error)
  }
}

async function handleListParticipants(
  userId: string, 
  conferenceId: string, 
  searchParams: URLSearchParams
) {
  const { pageSize, pageToken } = validatePaginationParams(searchParams)
  const filter = searchParams.get('filter') || undefined

  const participants = await listParticipants(userId, conferenceId, {
    pageSize,
    pageToken,
    filter
  })

  return formatMeetApiResponse(
    participants,
    "Participants retrieved successfully"
  )
}

async function handleDetailedParticipants(
  userId: string, 
  conferenceId: string, 
  searchParams: URLSearchParams
) {
  const includeSessions = searchParams.get('includeSessions') === 'true'

  const participants = await getConferenceParticipants(userId, conferenceId, includeSessions)

  return formatMeetApiResponse(
    participants,
    "Detailed participants information retrieved successfully"
  )
}

async function handleParticipantStatistics(userId: string, conferenceId: string) {
  const statistics = await getParticipantStatistics(userId, conferenceId)

  return formatMeetApiResponse(
    statistics,
    "Participant statistics retrieved successfully"
  )
}

async function handleParticipantAttendance(userId: string, conferenceId: string) {
  const attendance = await getParticipantAttendance(userId, conferenceId)

  return formatMeetApiResponse(
    attendance,
    "Participant attendance retrieved successfully"
  )
}

async function handleParticipantSearch(
  userId: string, 
  conferenceId: string, 
  searchParams: URLSearchParams
) {
  // Build search criteria
  const criteria: any = {}
  
  const displayName = searchParams.get('displayName')
  if (displayName) criteria.displayName = displayName
  
  const isAnonymous = searchParams.get('isAnonymous')
  if (isAnonymous !== null) criteria.isAnonymous = isAnonymous === 'true'
  
  const isPhoneUser = searchParams.get('isPhoneUser')
  if (isPhoneUser !== null) criteria.isPhoneUser = isPhoneUser === 'true'
  
  const minDuration = searchParams.get('minDuration')
  if (minDuration) criteria.minDuration = parseInt(minDuration)

  if (isNaN(criteria.minDuration)) {
    return NextResponse.json({
      error: "minDuration must be a valid number"
    }, { status: 400 })
  }

  const participants = await searchParticipants(userId, conferenceId, criteria)

  return formatMeetApiResponse(
    participants,
    "Participant search completed successfully"
  )
}
