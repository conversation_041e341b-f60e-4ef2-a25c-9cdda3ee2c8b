import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentCalendarService } from '@/lib/ai-agent-calendar'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      context,
      attendees = [],
      duration = 60
    } = await request.json()

    if (!context) {
      return NextResponse.json({ 
        error: 'Meeting context is required' 
      }, { status: 400 })
    }

    const suggestions = await aiAgentCalendarService.suggestMeetingTimes(
      session.user.id,
      context,
      attendees,
      duration
    )

    return NextResponse.json({
      suggestions,
      success: true
    })

  } catch (error) {
    console.error('Meeting suggestion error:', error)
    return NextResponse.json(
      { error: 'Failed to suggest meeting times' },
      { status: 500 }
    )
  }
}
