import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { geminiService, ChatMessage } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { messages, context } = await request.json()

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({ error: 'Invalid messages data' }, { status: 400 })
    }

    // Validate message format
    const validMessages: ChatMessage[] = messages.map((msg, index) => ({
      id: msg.id || `msg-${index}`,
      role: msg.role || 'user',
      content: msg.content || '',
      timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(),
      context: msg.context
    }))

    // Get AI response
    const aiResponse = await geminiService.chatWithAI(validMessages, context)

    return NextResponse.json({
      response: aiResponse,
      timestamp: new Date()
    })

  } catch (error) {
    console.error('AI Chat Error:', error)
    return NextResponse.json(
      { error: 'Failed to process chat message' },
      { status: 500 }
    )
  }
}

export async function GET(_request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // This could be extended to retrieve chat history from a database
    // For now, return empty array as chat history is stored client-side
    return NextResponse.json({
      chatHistory: [],
      message: 'Chat history retrieved successfully'
    })

  } catch (error) {
    console.error('Chat History Error:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve chat history' },
      { status: 500 }
    )
  }
} 