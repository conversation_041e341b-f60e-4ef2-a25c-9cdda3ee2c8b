import { NextRequest } from "next/server"
import {
  createDraft,
  with<PERSON><PERSON><PERSON><PERSON>
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { draftCreateSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-draft-create', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, draftCreateSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-draft-create')
      throw new Error(`Invalid draft data: ${validation.error}`)
    }

    const {
      to,
      cc,
      bcc,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments
    } = validation.data

    // Create draft
    const result = await createDraft(user.id, {
      to,
      cc,
      bcc,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments
    })

    if (!result.success) {
      endPerformanceMetric('gmail-draft-create')
      throw new Error(result.error || 'Failed to create draft')
    }

    // End performance monitoring
    endPerformanceMetric('gmail-draft-create')

    return {
      success: true,
      draftId: result.draftId,
      messageId: result.messageId,
      message: 'Draft created successfully'
    }
  })
}
