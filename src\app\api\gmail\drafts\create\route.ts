import { NextRequest } from "next/server"
import { 
  createDraft, 
  withG<PERSON><PERSON><PERSON>, 
  validateRequiredFields 
} from "@/lib/gmail"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['to', 'subject', 'htmlBody'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { 
      to, 
      cc, 
      bcc, 
      subject, 
      htmlBody, 
      textBody, 
      threadId, 
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments 
    } = body

    // Create draft
    const result = await createDraft(user.id, {
      to,
      cc,
      bcc,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      messageId,
      references,
      inReplyTo,
      attachments
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create draft')
    }

    return {
      success: true,
      draftId: result.draftId,
      messageId: result.messageId,
      message: 'Draft created successfully'
    }
  })
}
