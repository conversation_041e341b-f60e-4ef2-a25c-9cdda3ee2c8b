import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { aiAgentService } from '@/lib/ai-agent'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      message,
      intent
    } = await request.json()

    if (!message) {
      return NextResponse.json({ 
        error: 'Message is required' 
      }, { status: 400 })
    }

    const suggestions = await aiAgentService.getContextualSuggestions(
      session.user.id, 
      message, 
      intent || 'information_query'
    )

    return NextResponse.json({
      suggestions,
      success: true
    })

  } catch (error) {
    console.error('Get contextual suggestions error:', error)
    return NextResponse.json(
      { error: 'Failed to get contextual suggestions' },
      { status: 500 }
    )
  }
}
