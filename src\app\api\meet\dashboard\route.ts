import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetU<PERSON>, 
  handleMeetApiError, 
  formatMeetApiResponse,
  getDashboardData
} from "@/lib/meet"

/**
 * GET /api/meet/dashboard - Get Meet dashboard data
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const searchParams = url.searchParams

    // Get the number of days for the dashboard data (default: 30)
    const days = parseInt(searchParams.get('days') || '30')

    if (days < 1 || days > 365) {
      return NextResponse.json({
        error: "Days parameter must be between 1 and 365"
      }, { status: 400 })
    }

    // Get dashboard data
    const dashboardData = await getDashboardData(user.id, days)

    const response = {
      ...dashboardData,
      period: {
        days,
        startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date().toISOString()
      },
      user: {
        id: user.id,
        email: user.email
      },
      generatedAt: new Date().toISOString()
    }

    return formatMeetApiResponse(
      response,
      `Dashboard data for the last ${days} days retrieved successfully`
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
