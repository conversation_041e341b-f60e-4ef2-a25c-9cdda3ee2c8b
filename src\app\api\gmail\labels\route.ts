import { NextRequest } from "next/server"
import { 
  getG<PERSON><PERSON><PERSON><PERSON>, 
  create<PERSON><PERSON><PERSON><PERSON><PERSON>, 
  withGmail<PERSON>uth, 
  validateRequired<PERSON>ields 
} from "@/lib/gmail"

export async function GET(request: NextRequest) {
  return with<PERSON><PERSON><PERSON>uth(request, async ({ user }) => {
    // Fetch Gmail labels
    const labels = await getGmail<PERSON>abels(user.id)

    return { labels }
  })
}

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['name'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { name, color } = body

    // Create Gmail label
    const label = await createGmailLabel(user.id, name, color)

    if (!label) {
      throw new Error("Failed to create label")
    }

    return { label }
  })
} 