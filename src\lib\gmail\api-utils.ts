import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { formatErrorMessage } from './utils'

export interface AuthenticatedUser {
  id: string
  email: string
  gmailRefreshToken: string | null
  gmailConnected: boolean
  dailySendLimit: number
  dailySendCount: number
  lastSendReset: Date | null
}

export interface GmailApiContext {
  user: AuthenticatedUser
  url: URL
}

/**
 * Standard authentication and Gmail connection check for all routes
 */
export async function authenticateGmailUser(request: NextRequest): Promise<{
  success: true
  data: GmailApiContext
} | {
  success: false
  response: NextResponse
}> {
  try {
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return {
        success: false,
        response: NextResponse.json({ error: "Unauthorized" }, { status: 401 })
      }
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        gmailRefreshToken: true,
        gmailConnected: true,
        dailySendLimit: true,
        dailySendCount: true,
        lastSendReset: true
      }
    })

    if (!user) {
      return {
        success: false,
        response: NextResponse.json({ error: "User not found" }, { status: 404 })
      }
    }

    // Check if user has Gmail connected
    if (!user.gmailRefreshToken || !user.gmailConnected) {
      return {
        success: false,
        response: NextResponse.json({ 
          error: "Gmail account not connected. Please connect your Gmail account first." 
        }, { status: 400 })
      }
    }

    return {
      success: true,
      data: {
        user: user as AuthenticatedUser,
        url: new URL(request.url)
      }
    }
  } catch (error) {
    console.error("Authentication error:", error)
    return {
      success: false,
      response: NextResponse.json(
        { error: "Authentication failed" },
        { status: 500 }
      )
    }
  }
}

/**
 * Standard query parameter parsing for email list endpoints
 */
export function parseEmailListParams(url: URL) {
  return {
    limit: parseInt(url.searchParams.get('limit') || '50'),
    page: parseInt(url.searchParams.get('page') || '1'),
    pageToken: url.searchParams.get('pageToken') || undefined,
    labelId: url.searchParams.get('labelId') || undefined,
    dateFrom: url.searchParams.get('dateFrom') ? new Date(url.searchParams.get('dateFrom')!) : undefined,
    dateTo: url.searchParams.get('dateTo') ? new Date(url.searchParams.get('dateTo')!) : undefined
  }
}

/**
 * Standard success response formatter
 */
export function createSuccessResponse(data: any) {
  return NextResponse.json({
    success: true,
    ...data
  })
}

/**
 * Standard error response handler with Gmail-specific error handling
 */
export function createErrorResponse(error: any, operation?: string): NextResponse {
  console.error(`Error ${operation ? `${operation}:` : ''}`, error)
  
  const errorMessage = formatErrorMessage(error)
  
  // Handle scope-related errors specifically
  if (errorMessage.includes('Gmail permissions need to be updated')) {
    return NextResponse.json(
      { 
        error: errorMessage,
        requiresReconnection: true 
      },
      { status: 403 }
    )
  }
  
  // Handle rate limiting errors
  if (errorMessage.includes('rate limit') || errorMessage.includes('quota exceeded')) {
    return NextResponse.json(
      { error: errorMessage },
      { status: 429 }
    )
  }
  
  // Handle not found errors
  if (errorMessage.includes('not found')) {
    return NextResponse.json(
      { error: errorMessage },
      { status: 404 }
    )
  }
  
  // Default error response
  return NextResponse.json(
    { error: errorMessage },
    { status: 500 }
  )
}

/**
 * Standard wrapper for Gmail API route handlers
 */
export async function withGmailAuth<T>(
  request: NextRequest,
  handler: (context: GmailApiContext) => Promise<T>
): Promise<NextResponse> {
  try {
    const authResult = await authenticateGmailUser(request)
    
    if (!authResult.success) {
      return authResult.response
    }
    
    const result = await handler(authResult.data)
    return createSuccessResponse(result)
  } catch (error) {
    return createErrorResponse(error)
  }
}

/**
 * Standard email operation result formatter
 */
export function formatEmailOperationResult(
  success: boolean,
  message: string,
  additionalData?: any
) {
  if (success) {
    return {
      success: true,
      message,
      ...additionalData
    }
  } else {
    throw new Error(`Failed to ${message.toLowerCase()}`)
  }
}

/**
 * Validate required JSON body fields
 */
export function validateRequiredFields(
  body: any,
  requiredFields: string[]
): { isValid: boolean; missingFields?: string[] } {
  const missingFields = requiredFields.filter(field => !body[field])
  
  return {
    isValid: missingFields.length === 0,
    missingFields: missingFields.length > 0 ? missingFields : undefined
  }
}

/**
 * Standard bulk operation response formatter
 */
export function formatBulkOperationResult(
  totalItems: number,
  successCount: number,
  failedCount: number,
  operation: string
) {
  return {
    success: true,
    message: `Successfully ${operation} ${successCount} out of ${totalItems} email(s)`,
    stats: {
      total: totalItems,
      success: successCount,
      failed: failedCount
    }
  }
} 