import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(_request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Fetch contact lists with contact counts
    const contactLists = await prisma.contactList.findMany({
      where: { userId: user.id },
      include: {
        _count: {
          select: { contacts: true }
        }
      },
      orderBy: { createdAt: "desc" }
    })

    // Transform the data to match expected format
    const transformedLists = contactLists.map(list => ({
      id: list.id,
      name: list.name,
      description: list.description,
      contactCount: list._count.contacts
    }))

    return NextResponse.json(transformedLists)
  } catch (error) {
    console.error("Error fetching contact lists:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const { name, description } = await request.json()

    // Validate required fields
    if (!name) {
      return NextResponse.json({ error: "Name is required" }, { status: 400 })
    }

    // Check if contact list with this name already exists for this user
    const existingList = await prisma.contactList.findFirst({
      where: {
        userId: user.id,
        name: name
      }
    })

    if (existingList) {
      return NextResponse.json(
        { error: "Contact list with this name already exists" },
        { status: 409 }
      )
    }

    // Create new contact list
    const newList = await prisma.contactList.create({
      data: {
        userId: user.id,
        name,
        description: description || null
      },
      include: {
        _count: {
          select: { contacts: true }
        }
      }
    })

    // Transform the response to match expected format
    const transformedList = {
      id: newList.id,
      name: newList.name,
      description: newList.description,
      contactCount: newList._count.contacts
    }

    return NextResponse.json(transformedList, { status: 201 })
  } catch (error) {
    console.error("Error creating contact list:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 