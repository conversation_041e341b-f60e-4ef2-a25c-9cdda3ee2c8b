"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import DOMPurify from 'dompurify'
import { EmailRendererProps } from './types'

// Plain text email component
function PlainTextEmail({ text }: { text: string }) {
  return (
    <div className="whitespace-pre-wrap text-foreground p-4 font-mono text-sm leading-relaxed">
      {text}
    </div>
  )
}

// HTML email component with iframe rendering for security
function HtmlEmail({ 
  html, 
  enableQuoteToggle = true 
}: { 
  html: string
  enableQuoteToggle?: boolean 
}) {
  const [showReplies, setShowReplies] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [isDarkMode, setIsDarkMode] = useState(false)

  // Check for dark mode
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(window.matchMedia('(prefers-color-scheme: dark)').matches)
    }
    checkDarkMode()
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', checkDarkMode)
    
    return () => mediaQuery.removeEventListener('change', checkDarkMode)
  }, [])

  const sanitizedHtml = useMemo(() => sanitizeHtml(html), [html])
  const { mainContent, hasReplies } = useMemo(
    () => extractEmailContent(sanitizedHtml),
    [sanitizedHtml],
  )

  const srcDoc = useMemo(
    () => generateIframeHtml(showReplies ? sanitizedHtml : mainContent, isDarkMode),
    [sanitizedHtml, mainContent, showReplies, isDarkMode],
  )

  const iframeHeight = useIframeHeight(iframeRef)

  return (
    <div className="relative w-full">
      <iframe
        ref={iframeRef}
        srcDoc={srcDoc}
        className="min-h-0 w-full border-0 bg-background"
        style={{ height: `${Math.max(iframeHeight, 200)}px` }}
        title="Email content"
        sandbox="allow-same-origin allow-popups allow-popups-to-escape-sandbox"
        referrerPolicy="no-referrer"
        loading="lazy"
      />
      {enableQuoteToggle && hasReplies && (
        <button
          type="button"
          className="absolute bottom-2 left-2 px-3 py-1 text-xs bg-background/80 backdrop-blur-sm border rounded-md text-muted-foreground hover:text-foreground hover:bg-background/90 transition-colors"
          onClick={() => setShowReplies(!showReplies)}
        >
          {showReplies ? 'Hide' : 'Show'} previous messages
        </button>
      )}
    </div>
  )
}

// Enhanced HTML sanitization
function sanitizeHtml(html: string): string {
  const config = {
    USE_PROFILES: { html: true },
    ADD_ATTR: ['target', 'rel'],
    ALLOW_DATA_ATTR: false,
    ALLOWED_TAGS: [
      'div', 'span', 'p', 'br', 'strong', 'b', 'em', 'i', 'u', 's', 'strike',
      'a', 'img', 'table', 'tr', 'td', 'th', 'thead', 'tbody', 'tfoot',
      'ul', 'ol', 'li', 'blockquote', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'pre', 'code', 'hr', 'font', 'center', 'small', 'big', 'sub', 'sup'
    ],
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'class', 'id', 'style', 'target', 'rel',
      'width', 'height', 'border', 'cellpadding', 'cellspacing', 'bgcolor',
      'color', 'face', 'size', 'align', 'valign'
    ],
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
    FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover']
  }
  
  return DOMPurify.sanitize(html, config)
}

// Extract main content and detect quoted replies
function extractEmailContent(html: string) {
  try {
    const doc = new DOMParser().parseFromString(html, "text/html")
    
    // Common patterns for quoted content
    const quoteSelectors = [
      ".gmail_quote",
      ".gmail_quote_container", 
      ".yahoo_quoted",
      "[class*='quote']",
      "[id*='quote']",
      "blockquote[type='cite']"
    ]
    
    let hasReplies = false
    const quoteContainer = quoteSelectors.find(selector => {
      const element = doc.querySelector(selector)
      if (element) {
        hasReplies = true
        return true
      }
      return false
    })

    if (!hasReplies) {
      return { mainContent: html, hasReplies: false }
    }

    // Clone document and remove quoted content for main view
    const mainDoc = doc.cloneNode(true) as Document
    quoteSelectors.forEach(selector => {
      const elements = mainDoc.querySelectorAll(selector)
      elements.forEach(el => el.remove())
    })

    return {
      mainContent: mainDoc.body.innerHTML,
      hasReplies: true,
    }
  } catch (error) {
    console.error("Error extracting email content:", error)
    return { mainContent: html, hasReplies: false }
  }
}

// Generate secure iframe HTML
function generateIframeHtml(html: string, isDarkMode: boolean): string {
  const hasHeavyStyling = detectHeavyStyling(html)
  
  const themeStyles = hasHeavyStyling ? getMinimalStyles() : getAdaptiveStyles(isDarkMode)
  
  const securityHeaders = `
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'none';
      style-src 'unsafe-inline';
      img-src data: https: http:;
      font-src 'none';
      script-src 'none';
      frame-src 'none';
      object-src 'none';
      base-uri 'none';
      form-action 'none';
    ">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta name="viewport" content="width=device-width, initial-scale=1">
  `

  const baseTarget = `<base target="_blank" rel="noopener noreferrer">`
  
  return `
    <!DOCTYPE html>
    <html class="${isDarkMode ? 'dark' : ''}">
      <head>
        ${securityHeaders}
        ${baseTarget}
        <style>${themeStyles}</style>
      </head>
      <body>
        ${html}
      </body>
    </html>
  `
}

// Detect if email has heavy styling that should not be overridden
function detectHeavyStyling(html: string): boolean {
  const styleIndicators = [
    /bgcolor\s*=/i,
    /background\s*[:=]/i,
    /<style/i,
    /font-family\s*:/i,
    /font-size\s*:/i,
    /<table[^>]*style/i
  ]
  
  const styleAttributeCount = (html.match(/style\s*=/gi) || []).length
  
  return (
    styleIndicators.some(pattern => pattern.test(html)) ||
    styleAttributeCount > 3
  )
}

// Minimal styles for heavily styled emails
function getMinimalStyles(): string {
  return `
    :root {
      color-scheme: light;
    }
    body {
      margin: 0;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.5;
      word-wrap: break-word;
      background-color: white !important;
    }
    img {
      max-width: 100% !important;
      height: auto !important;
    }
    table {
      max-width: 100% !important;
      border-collapse: collapse;
    }
    a {
      color: #1a73e8;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  `
}

// Adaptive styles that respect dark mode
function getAdaptiveStyles(isDarkMode: boolean): string {
  return `
    :root {
      color-scheme: ${isDarkMode ? 'dark' : 'light'};
      --text-primary: ${isDarkMode ? '#ffffff' : '#000000'};
      --text-secondary: ${isDarkMode ? '#a1a1aa' : '#6b7280'};
      --bg-primary: ${isDarkMode ? '#18181b' : '#ffffff'};
      --border-color: ${isDarkMode ? '#3f3f46' : '#e5e7eb'};
      --link-color: ${isDarkMode ? '#60a5fa' : '#1d4ed8'};
    }

    body {
      margin: 0;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: var(--text-primary);
      background-color: var(--bg-primary);
      word-wrap: break-word;
    }

    /* Typography */
    p {
      margin: 0 0 1em 0;
    }
    
    p:last-child {
      margin-bottom: 0;
    }

    h1, h2, h3, h4, h5, h6 {
      color: var(--text-primary);
      margin: 1.5em 0 0.5em 0;
    }

    /* Links */
    a {
      color: var(--link-color);
      text-decoration: none;
    }
    
    a:hover {
      text-decoration: underline;
    }

    /* Images */
    img {
      max-width: 100% !important;
      height: auto !important;
      border-radius: 4px;
    }

    /* Tables */
    table {
      max-width: 100% !important;
      border-collapse: collapse;
      margin: 1em 0;
    }
    
    td, th {
      padding: 8px 12px;
      border: 1px solid var(--border-color);
    }

    /* Blockquotes */
    blockquote {
      border-left: 4px solid var(--border-color);
      margin: 1em 0;
      padding: 0 0 0 1em;
      color: var(--text-secondary);
    }

    /* Code */
    pre, code {
      font-family: 'SF Mono', Monaco, Consolas, monospace;
      background-color: ${isDarkMode ? '#27272a' : '#f3f4f6'};
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 0.9em;
    }

    pre {
      padding: 1em;
      overflow-x: auto;
    }

    /* Lists */
    ul, ol {
      margin: 1em 0;
      padding-left: 2em;
    }

    li {
      margin: 0.3em 0;
    }

    /* Gmail-specific quote styling */
    .gmail_quote {
      color: var(--text-secondary);
      border-left: 3px solid var(--border-color);
      margin: 1em 0;
      padding-left: 1em;
    }
  `
}

// Hook to measure iframe content height
function useIframeHeight(iframeRef: React.RefObject<HTMLIFrameElement | null>) {
  const [height, setHeight] = useState(200)

  useEffect(() => {
    let attempts = 0
    const maxAttempts = 10
    const baseDelay = 100

    const updateHeight = () => {
      try {
        if (iframeRef.current?.contentWindow?.document) {
          const doc = iframeRef.current.contentWindow.document
          const scrollHeight = Math.max(
            doc.documentElement?.scrollHeight || 0,
            doc.body?.scrollHeight || 0
          )
          
          if (scrollHeight > 0) {
            setHeight(Math.min(scrollHeight + 20, window.innerHeight * 0.8))
            return true
          }
        }
      } catch (error) {
        console.warn("Failed to measure iframe height:", error)
      }
      return false
    }

    const attemptUpdate = () => {
      if (attempts >= maxAttempts) return

      const success = updateHeight()
      if (!success && attempts < maxAttempts) {
        attempts++
        setTimeout(attemptUpdate, baseDelay * Math.pow(1.5, attempts))
      }
    }

    // Initial measurement
    const timer = setTimeout(attemptUpdate, 100)
    
    // Also try when iframe loads
    const iframe = iframeRef.current
    if (iframe) {
      iframe.addEventListener('load', attemptUpdate)
    }

    return () => {
      clearTimeout(timer)
      if (iframe) {
        iframe.removeEventListener('load', attemptUpdate)
      }
    }
  }, [iframeRef.current])

  return height
}

// Utility function to determine if content is plain text
function isPlainTextContent(content: string): boolean {
  if (!content?.trim()) return true
  
  // Simple heuristic: if it contains HTML tags, treat as HTML
  const htmlTagPattern = /<[^>]+>/
  const hasHtmlTags = htmlTagPattern.test(content)
  
  if (!hasHtmlTags) return true
  
  // Check for actual structural HTML vs just angle brackets
  const structuralTags = /<(div|p|br|span|table|tr|td|ul|ol|li|h[1-6])[^>]*>/i
  return !structuralTags.test(content)
}

// Main component
export default function EmailRenderer({ 
  body, 
  className = "",
  enableQuoteToggle = true 
}: EmailRendererProps) {
  if (!body?.trim()) {
    return (
      <div className={`p-6 text-center text-muted-foreground ${className}`}>
        <p className="italic">No email content available</p>
      </div>
    )
  }

  const isPlainText = isPlainTextContent(body)

  return (
    <div className={`relative overflow-hidden rounded-lg border bg-background ${className}`}>
      {isPlainText ? (
        <PlainTextEmail text={body} />
      ) : (
        <HtmlEmail html={body} enableQuoteToggle={enableQuoteToggle} />
      )}
    </div>
  )
}

// Export utility functions for advanced use cases
export {
  sanitizeHtml,
  extractEmailContent,
  isPlainTextContent
} 