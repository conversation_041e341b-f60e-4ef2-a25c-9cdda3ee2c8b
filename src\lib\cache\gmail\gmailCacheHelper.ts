/**
 * Gmail Cache Helper
 * Centralized Gmail caching functionality with optimized performance
 */

import { CachedEmail, CachedEmailList } from '@/contexts/cache/types'
import { cacheService } from '@/contexts/cache/CacheService'
import { CachePresets } from '@/contexts/cache'

// ============================================================================
// CONSTANTS
// ============================================================================

export const GMAIL_CACHE_KEYS = {
  INBOX_7DAYS: 'inbox_7days',
  SENT_7DAYS: 'sent_7days',
  SPAM_7DAYS: 'spam_7days',
  TRASH_7DAYS: 'trash_7days',
  ARCHIVE_7DAYS: 'archive_7days',
  DRAFTS: 'drafts',
  CATEGORIES: 'categories',
  EMAIL_DETAILS: 'emailDetails',
  THREADS: 'threads'
} as const

export const GMAIL_CACHE_TTL = CachePresets.EMAIL.defaultTTL // 2 minutes

// ============================================================================
// EMAIL LIST CACHING
// ============================================================================

/**
 * Get cached email list by type and optional category
 */
export function getCachedEmails(type: string, category?: string): CachedEmailList | null {
  const key = category ? `categories.${category}` : type
  return cacheService.get<CachedEmailList>('email', key)
}

/**
 * Set cached email list with optimized TTL
 */
export function setCachedEmails(type: string, data: CachedEmailList, category?: string): void {
  const key = category ? `categories.${category}` : type
  cacheService.set('email', key, data, GMAIL_CACHE_TTL)
}

/**
 * Check if email cache is valid for given type and category
 */
export function isEmailCacheValid(type: string, category?: string, maxAge?: number): boolean {
  const key = category ? `categories.${category}` : type
  const cached = cacheService.get<CachedEmailList>('email', key)
  
  if (!cached) return false
  
  const age = Date.now() - cached.timestamp
  const maxAgeMs = maxAge || GMAIL_CACHE_TTL
  
  return age < maxAgeMs
}

// ============================================================================
// EMAIL DETAIL CACHING
// ============================================================================

/**
 * Get cached email detail by ID
 */
export function getCachedEmailDetail(emailId: string): CachedEmail | null {
  return cacheService.get<CachedEmail>('email', `emailDetails.${emailId}`)
}

/**
 * Set cached email detail
 */
export function setCachedEmailDetail(emailId: string, data: CachedEmail): void {
  cacheService.set('email', `emailDetails.${emailId}`, data, GMAIL_CACHE_TTL)
}

/**
 * Update cached email with partial data
 */
export function updateCachedEmail(emailId: string, updates: Partial<CachedEmail>): void {
  const existing = getCachedEmailDetail(emailId)
  if (existing) {
    const updated = { ...existing, ...updates }
    setCachedEmailDetail(emailId, updated)
  }
}

// ============================================================================
// THREAD CACHING
// ============================================================================

/**
 * Get cached email thread
 */
export function getCachedThread(threadId: string): CachedEmail[] | null {
  return cacheService.get<CachedEmail[]>('email', `threads.${threadId}`)
}

/**
 * Set cached email thread
 */
export function setCachedThread(threadId: string, emails: CachedEmail[]): void {
  cacheService.set('email', `threads.${threadId}`, emails, GMAIL_CACHE_TTL)
}

// ============================================================================
// CACHE INVALIDATION
// ============================================================================

/**
 * Invalidate specific email cache entry
 */
export function invalidateEmailCache(type?: string, category?: string): void {
  if (type && category) {
    cacheService.delete('email', `categories.${category}`)
  } else if (type) {
    cacheService.delete('email', type)
  } else {
    cacheService.clear('email')
  }
}

/**
 * Invalidate email detail cache
 */
export function invalidateEmailDetail(emailId: string): void {
  cacheService.delete('email', `emailDetails.${emailId}`)
}

/**
 * Invalidate thread cache
 */
export function invalidateThread(threadId: string): void {
  cacheService.delete('email', `threads.${threadId}`)
}

// ============================================================================
// BULK OPERATIONS
// ============================================================================

/**
 * Cache multiple emails at once
 */
export function cacheMultipleEmails(emails: Array<{ id: string; data: CachedEmail }>): void {
  emails.forEach(({ id, data }) => {
    setCachedEmailDetail(id, data)
  })
}

/**
 * Get multiple cached emails
 */
export function getMultipleCachedEmails(emailIds: string[]): Array<{ id: string; data: CachedEmail | null }> {
  return emailIds.map(id => ({
    id,
    data: getCachedEmailDetail(id)
  }))
}

// ============================================================================
// PRELOADING
// ============================================================================

/**
 * Preload Gmail data for better performance
 */
export async function preloadGmailData(): Promise<void> {
  const now = new Date()
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  const dateParams = `&after=${Math.floor(sevenDaysAgo.getTime() / 1000)}`

  const preloadPromises = [
    // Inbox
    fetch(`/api/gmail/inbox?limit=20${dateParams}`).then(async (response) => {
      if (response.ok) {
        const data = await response.json()
        if (data.emails) {
          setCachedEmails(GMAIL_CACHE_KEYS.INBOX_7DAYS, {
            emails: data.emails.map((email: any) => ({ ...email, date: new Date(email.date) })),
            totalCount: data.totalCount || 0,
            timestamp: Date.now(),
            currentPage: 1,
            totalPages: Math.ceil((data.totalCount || 0) / 20)
          })
        }
      }
    }).catch(err => console.error('Failed to preload inbox:', err)),

    // Sent emails
    fetch(`/api/gmail/sent?limit=15${dateParams}`).then(async (response) => {
      if (response.ok) {
        const data = await response.json()
        if (data.emails) {
          setCachedEmails(GMAIL_CACHE_KEYS.SENT_7DAYS, {
            emails: data.emails.map((email: any) => ({ ...email, date: new Date(email.date) })),
            totalCount: data.totalCount || 0,
            timestamp: Date.now(),
            currentPage: 1,
            totalPages: Math.ceil((data.totalCount || 0) / 15)
          })
        }
      }
    }).catch(err => console.error('Failed to preload sent:', err)),

    // Drafts
    fetch('/api/gmail/drafts?limit=10').then(async (response) => {
      if (response.ok) {
        const data = await response.json()
        if (data.drafts) {
          setCachedEmails(GMAIL_CACHE_KEYS.DRAFTS, {
            emails: data.drafts.map((draft: any) => ({ ...draft, date: new Date(draft.date) })),
            totalCount: data.totalCount || 0,
            timestamp: Date.now(),
            currentPage: 1,
            totalPages: Math.ceil((data.totalCount || 0) / 10)
          })
        }
      }
    }).catch(err => console.error('Failed to preload drafts:', err))
  ]

  await Promise.allSettled(preloadPromises)
  console.log('📧 [GMAIL CACHE] Preload completed')
}

// ============================================================================
// CACHE STATISTICS
// ============================================================================

/**
 * Get Gmail cache statistics
 */
export function getGmailCacheStats(): {
  totalEmails: number
  totalThreads: number
  cacheHitRate: number
  memoryUsage: number
} {
  const stats = cacheService.getStats()
  const emailNamespaceSize = cacheService.getNamespaceSize('email')
  
  return {
    totalEmails: emailNamespaceSize,
    totalThreads: 0, // TODO: Implement thread counting
    cacheHitRate: stats.hitRate,
    memoryUsage: stats.memoryUsage
  }
}

// ============================================================================
// CLEANUP
// ============================================================================

/**
 * Clean up expired Gmail cache entries
 */
export function cleanupGmailCache(): void {
  cacheService.cleanup()
  console.log('🧹 [GMAIL CACHE] Cleanup completed')
}
