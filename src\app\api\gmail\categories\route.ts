import { NextRequest } from "next/server"
import { getCategoryEmails, withGmailAuth, parseEmailListParams } from "@/lib/gmail"

export async function GET(request: NextRequest) {
  return withGmailAuth(request, async ({ user, url }) => {
    const params = parseEmailListParams(url)
    const category = url.searchParams.get('category') || 'primary'

    console.log(`Fetching ${category} category emails for user ${user.id}, page ${params.page}, limit ${params.limit}`)
    
    // Fetch category emails from Gmail
    const result = await getCategoryEmails(
      user.id, 
      category, 
      params.limit, 
      params.pageToken, 
      params.page, 
      params.dateFrom, 
      params.dateTo
    )

    console.log(`Found ${result.emails.length} emails for category ${category}`)

    return {
      category,
      ...result
    }
  })
}
