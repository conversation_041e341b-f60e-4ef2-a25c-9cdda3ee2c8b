import { NextRequest } from "next/server"
import { getCategoryEmails, withGmail<PERSON>uth } from "@/lib/gmail"
import { validateSearchParams } from "@/lib/validation/utils"
import { emailListParamsSchema, emailCategorySchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function GET(request: NextRequest) {
  return withGmailAuth(request, async ({ user, url }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-categories-fetch', { userId: user.id })

    // Validate search parameters using Zod
    const validation = validateSearchParams(url.searchParams, emailListParamsSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-categories-fetch')
      throw new Error(`Invalid parameters: ${validation.error}`)
    }

    const params = validation.data

    // Validate category parameter
    const categoryValidation = emailCategorySchema.safeParse(url.searchParams.get('category') || 'primary')
    if (!categoryValidation.success) {
      endPerformanceMetric('gmail-categories-fetch')
      throw new Error(`Invalid category: ${categoryValidation.error.message}`)
    }

    const category = categoryValidation.data

    // Fetch category emails from Gmail with validated parameters
    const result = await getCategoryEmails(
      user.id,
      category,
      params.limit,
      params.pageToken,
      params.page,
      params.dateFrom ? new Date(params.dateFrom) : undefined,
      params.dateTo ? new Date(params.dateTo) : undefined
    )

    // End performance monitoring
    const duration = endPerformanceMetric('gmail-categories-fetch')
    if (duration && duration > 3000) {
      console.warn(`Slow category fetch: ${duration.toFixed(2)}ms for ${category} category, user ${user.id}`)
    }

    return {
      category,
      ...result
    }
  })
}
