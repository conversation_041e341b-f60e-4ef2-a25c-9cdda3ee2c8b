"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"

import {
  Mail,
  Users,
  Send,
  BarChart3,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Zap,
  X,
  RefreshCw,
  Video
} from "lucide-react"
import Link from "next/link"

interface DashboardStats {
  totalCampaigns: number
  totalContacts: number
  emailsSentToday: number
  gmailConnected: boolean
  meetConnected: boolean
}

interface Campaign {
  id: string
  name: string
  status: string
  sentCount: number
  totalEmails: number
  createdAt: string
}

export default function Dashboard() {
  const { data: session, update } = useSession()
  const [stats, setStats] = useState<DashboardStats>({
    totalCampaigns: 0,
    totalContacts: 0,
    emailsSentToday: 0,
    gmailConnected: false,
    meetConnected: false
  })
  const [recentCampaigns, setRecentCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(true)
  const [gmailMessage, setGmailMessage] = useState<{type: 'success' | 'error', text: string} | null>(null)

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      // Fetch dashboard stats
      const statsResponse = await fetch('/api/dashboard/stats')
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }

      // Fetch recent campaigns
      const campaignsResponse = await fetch('/api/dashboard/recent-campaigns')
      if (campaignsResponse.ok) {
        const campaignsData = await campaignsResponse.json()
        setRecentCampaigns(campaignsData)
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()

    // Check URL parameters for Gmail connection status
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('gmail_connected') === 'true') {
      setGmailMessage({ type: 'success', text: 'Gmail connected successfully! You can now send emails.' })
      // Update session to reflect Gmail connection
      update()
      // Clear URL parameter
      window.history.replaceState({}, '', window.location.pathname)
    } else if (urlParams.get('gmail_error')) {
      const error = urlParams.get('gmail_error')
      setGmailMessage({ type: 'error', text: `Gmail connection failed: ${error}` })
      // Clear URL parameter
      window.history.replaceState({}, '', window.location.pathname)
    }
  }, [])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'sent':
        return 'text-green-600 bg-green-100'
      case 'sending':
        return 'text-blue-600 bg-blue-100'
      case 'draft':
        return 'text-gray-600 bg-gray-100'
      case 'scheduled':
        return 'text-orange-600 bg-orange-100'
      case 'failed':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'sent':
        return <CheckCircle className="h-4 w-4" />
      case 'sending':
        return <Send className="h-4 w-4" />
      case 'scheduled':
        return <Clock className="h-4 w-4" />
      case 'failed':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Mail className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 animate-pulse mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome back, {session?.user?.name?.split(' ')[0]}! 👋
          </h1>
          <p className="text-muted-foreground">
            Here&apos;s what&apos;s happening with your email campaigns today.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            onClick={fetchDashboardData}
            disabled={loading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link href="/dashboard/campaigns/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Campaign
            </Button>
          </Link>
        </div>
      </div>

      {/* Gmail Connection Messages */}
      {gmailMessage && (
        <Card className={`${gmailMessage.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              {gmailMessage.type === 'success' ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
              <p className={`${gmailMessage.type === 'success' ? 'text-green-800' : 'text-red-800'}`}>
                {gmailMessage.text}
              </p>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setGmailMessage(null)}
                className="ml-auto"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Gmail Connection Status */}
      {!stats.gmailConnected && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-orange-600" />
              <CardTitle className="text-orange-800">Gmail API Not Connected</CardTitle>
            </div>
            <CardDescription className="text-orange-700">
              Connect your Gmail account to start sending mass emails. This is required for email delivery.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              variant="outline" 
              className="border-orange-300 text-orange-700 hover:bg-orange-100"
              onClick={() => window.location.href = '/api/gmail/connect'}
            >
              <Zap className="mr-2 h-4 w-4" />
              Connect Gmail
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCampaigns}</div>
            <p className="text-xs text-muted-foreground">
              Email campaigns created
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Contacts</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalContacts}</div>
            <p className="text-xs text-muted-foreground">
              People in your lists
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Emails Sent Today</CardTitle>
            <Send className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.emailsSentToday}</div>
            <p className="text-xs text-muted-foreground">
              Out of 250 daily limit
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gmail Status</CardTitle>
            <Zap className={`h-4 w-4 ${stats.gmailConnected ? 'text-green-600' : 'text-gray-400'}`} />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant={stats.gmailConnected ? "default" : "secondary"}>
                {stats.gmailConnected ? "Connected" : "Not Connected"}
              </Badge>
              {!stats.gmailConnected && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/gmail/status', { method: 'POST' })
                      if (response.ok) {
                        window.location.reload()
                      } else {
                        window.location.href = '/api/gmail/connect'
                      }
                    } catch (error) {
                      console.error('Error checking Gmail status:', error)
                      window.location.href = '/api/gmail/connect'
                    }
                  }}
                  className="text-xs"
                >
                  Fix
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              {stats.gmailConnected ? "Ready to send emails" : "Setup required"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Meet Status</CardTitle>
            <Video className={`h-4 w-4 ${stats.meetConnected ? 'text-green-600' : 'text-gray-400'}`} />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant={stats.meetConnected ? "default" : "secondary"}>
                {stats.meetConnected ? "Connected" : "Not Connected"}
              </Badge>
              {!stats.meetConnected && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.location.href = '/api/meet/connect'}
                  className="text-xs"
                >
                  Connect
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              {stats.meetConnected ? "Ready for meetings" : "Setup required"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Campaigns */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Campaigns</CardTitle>
              <CardDescription>
                Your latest email campaigns and their performance
              </CardDescription>
            </div>
            <Link href="/dashboard/campaigns">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {recentCampaigns.length === 0 ? (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No campaigns yet</h3>
              <p className="text-gray-500 mb-4">
                Create your first email campaign to get started.
              </p>
              <Link href="/dashboard/campaigns/new">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Campaign
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {recentCampaigns.map((campaign) => (
                <div key={campaign.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-lg ${getStatusColor(campaign.status)}`}>
                      {getStatusIcon(campaign.status)}
                    </div>
                    <div>
                      <h3 className="font-medium">{campaign.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        Created {new Date(campaign.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {campaign.sentCount} / {campaign.totalEmails}
                      </p>
                      <p className="text-xs text-muted-foreground">emails sent</p>
                    </div>
                    <Badge variant="outline" className={getStatusColor(campaign.status)}>
                      {campaign.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <Link href="/dashboard/campaigns/new">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="h-5 w-5" />
                <span>Create Campaign</span>
              </CardTitle>
              <CardDescription>
                Start a new email campaign with our easy-to-use builder
              </CardDescription>
            </CardHeader>
          </Link>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <Link href="/dashboard/contacts">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Manage Contacts</span>
              </CardTitle>
              <CardDescription>
                Add, edit, and organize your email contact lists
              </CardDescription>
            </CardHeader>
          </Link>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <Link href="/dashboard/analytics">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>View Analytics</span>
              </CardTitle>
              <CardDescription>
                Track opens, clicks, and campaign performance
              </CardDescription>
            </CardHeader>
          </Link>
        </Card>
      </div>
    </div>
  )
} 