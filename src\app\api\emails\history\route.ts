import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status') as 'PENDING' | 'SENT' | 'FAILED' | 'DELIVERED' | 'BOUNCED' | null
    const campaignId = searchParams.get('campaignId')
    const offset = (page - 1) * limit

    // Build where clause
    const whereClause: any = {
      contact: {
        userId: user.id
      }
    }

    if (status) {
      whereClause.status = status
    }

    if (campaignId) {
      whereClause.campaignId = campaignId
    }

    // Get email history with pagination
    const [emails, totalCount] = await Promise.all([
      prisma.sentEmail.findMany({
        where: whereClause,
        include: {
          contact: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true
            }
          },
          campaign: {
            select: {
              id: true,
              name: true,
              subject: true
            }
          }
        },
        orderBy: { sentAt: 'desc' },
        skip: offset,
        take: limit
      }),
      prisma.sentEmail.count({ where: whereClause })
    ])

    // Get statistics
    const stats = await prisma.sentEmail.groupBy({
      by: ['status'],
      where: {
        contact: {
          userId: user.id
        }
      },
      _count: {
        status: true
      }
    })

    const statusCounts = stats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.status
      return acc
    }, {} as Record<string, number>)

    // Get today's stats
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayStats = await prisma.sentEmail.groupBy({
      by: ['status'],
      where: {
        contact: {
          userId: user.id
        },
        sentAt: {
          gte: today
        }
      },
      _count: {
        status: true
      }
    })

    const todayStatusCounts = todayStats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.status
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      emails,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      },
      stats: {
        overall: statusCounts,
        today: todayStatusCounts
      }
    })

  } catch (error) {
    console.error("Error fetching email history:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 