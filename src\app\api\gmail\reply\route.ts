import { NextRequest } from "next/server"
import { 
  replyToEmail, 
  with<PERSON><PERSON><PERSON><PERSON>, 
  validateRequiredFields 
} from "@/lib/gmail"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['to', 'subject', 'htmlBody', 'threadId', 'replyToMessageId'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { to, subject, htmlBody, textBody, threadId, replyToMessageId, attachments } = body

    // Send reply
    const result = await replyToEmail(user.id, {
      to,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      attachments
    })

    if (result.success) {
      return {
        success: true,
        messageId: result.messageId,
        message: "Reply sent successfully"
      }
    } else {
      throw new Error(result.error || "Failed to send reply")
    }
  })
} 