import { NextRequest } from "next/server"
import {
  replyToEmail,
  with<PERSON>mail<PERSON><PERSON>
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { emailReplySchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-reply-email', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, emailReplySchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-reply-email')
      throw new Error(`Invalid reply data: ${validation.error}`)
    }

    const body = validation.data

    const { to, subject, htmlBody, textBody, threadId, replyToMessageId, attachments } = body

    // Send reply
    const result = await replyToEmail(user.id, {
      to,
      subject,
      htmlBody,
      textBody,
      threadId,
      replyToMessageId,
      attachments
    })

    if (result.success) {
      // End performance monitoring
      const duration = endPerformanceMetric('gmail-reply-email')
      if (duration && duration > 5000) {
        console.warn(`Slow email reply: ${duration.toFixed(2)}ms for user ${user.id}`)
      }

      return {
        success: true,
        messageId: result.messageId,
        message: "Reply sent successfully"
      }
    } else {
      endPerformanceMetric('gmail-reply-email')
      throw new Error(result.error || "Failed to send reply")
    }
  })
}