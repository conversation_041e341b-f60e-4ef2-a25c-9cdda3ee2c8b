"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { 
  Wifi, 
  WifiOff, 
  Cloud, 
  CloudOff, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Database,
  Zap
} from "lucide-react"
// import { useOfflineDetection } from "@/hooks/useOfflineDetection" // Disabled
import { draftCache, DraftCacheStats } from "@/lib/cache/draftCache"
import { draftSyncManager } from "@/lib/cache/draftSyncManager"

interface CacheStatusIndicatorProps {
  userId?: string
  showDetails?: boolean
  compact?: boolean
}

export function CacheStatusIndicator({
  userId,
  showDetails = false,
  compact = false
}: CacheStatusIndicatorProps) {
  // Temporarily disabled to avoid API calls
  // const { networkStatus, offlineQueue, isProcessingQueue } = useOfflineDetection()
  const networkStatus = {
    isOnline: true,
    isSlowConnection: false,
    connectionType: undefined,
    effectiveType: undefined,
    downlink: undefined,
    rtt: undefined
  }
  const offlineQueue: any[] = []
  const isProcessingQueue = false

  const [cacheStats, setCacheStats] = useState<DraftCacheStats | null>(null)
  const [syncStatus, setSyncStatus] = useState({ syncInProgress: false, lastSyncTime: null, syncErrors: 0 })

  useEffect(() => {
    const updateStats = async () => {
      if (userId) {
        try {
          const stats = await draftCache.getCacheStats(userId)
          setCacheStats(stats)
        } catch (error) {
          console.log('Cache not available:', error)
        }
      }
    }

    updateStats()

    // Update stats periodically
    const interval = setInterval(updateStats, 10000)

    // Update sync status periodically - disabled
    const syncInterval = setInterval(() => {
      // Disabled to avoid API calls
      // try {
      //   setSyncStatus(draftSyncManager.getSyncStatus())
      // } catch (error) {
      //   console.log('Sync manager not available:', error)
      // }
    }, 1000)

    return () => {
      clearInterval(interval)
      clearInterval(syncInterval)
    }
  }, [userId])

  const getConnectionIcon = () => {
    if (!networkStatus.isOnline) {
      return <WifiOff className="h-3 w-3 text-red-500" />
    }
    if (networkStatus.isSlowConnection) {
      return <Wifi className="h-3 w-3 text-yellow-500" />
    }
    return <Wifi className="h-3 w-3 text-green-500" />
  }

  const getConnectionText = () => {
    if (!networkStatus.isOnline) return "Offline"
    if (networkStatus.isSlowConnection) return "Slow Connection"
    return "Online"
  }

  const getConnectionColor = () => {
    if (!networkStatus.isOnline) return "destructive"
    if (networkStatus.isSlowConnection) return "secondary"
    return "default"
  }

  const getSyncIcon = () => {
    if (syncStatus.syncInProgress || isProcessingQueue) {
      return <RefreshCw className="h-3 w-3 animate-spin text-blue-500" />
    }
    if (cacheStats?.syncErrors && cacheStats.syncErrors > 0) {
      return <AlertCircle className="h-3 w-3 text-red-500" />
    }
    if (cacheStats?.pendingSync && cacheStats.pendingSync > 0) {
      return <Clock className="h-3 w-3 text-yellow-500" />
    }
    return <CheckCircle className="h-3 w-3 text-green-500" />
  }

  const getSyncText = () => {
    if (syncStatus.syncInProgress || isProcessingQueue) return "Syncing..."
    if (cacheStats?.syncErrors && cacheStats.syncErrors > 0) return "Sync Errors"
    if (cacheStats?.pendingSync && cacheStats.pendingSync > 0) return "Pending Sync"
    return "Synced"
  }

  const getSyncColor = () => {
    if (syncStatus.syncInProgress || isProcessingQueue) return "default"
    if (cacheStats?.syncErrors && cacheStats.syncErrors > 0) return "destructive"
    if (cacheStats?.pendingSync && cacheStats.pendingSync > 0) return "secondary"
    return "default"
  }

  const handleRetrySync = async () => {
    // Disabled to avoid API calls
    // if (userId) {
    //   await draftSyncManager.retrySyncErrors(userId)
    // }
    console.log('Retry sync disabled')
  }

  if (compact) {
    return (
      <TooltipProvider>
        <div className="flex items-center space-x-2">
          <Tooltip>
            <TooltipTrigger>
              <Badge variant={getConnectionColor()} className="text-xs">
                {getConnectionIcon()}
                <span className="ml-1">{getConnectionText()}</span>
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                <div>Connection: {getConnectionText()}</div>
                {networkStatus.effectiveType && (
                  <div>Type: {networkStatus.effectiveType}</div>
                )}
                {networkStatus.downlink && (
                  <div>Speed: {networkStatus.downlink} Mbps</div>
                )}
              </div>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger>
              <Badge variant={getSyncColor()} className="text-xs">
                {getSyncIcon()}
                <span className="ml-1">{getSyncText()}</span>
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm">
                {cacheStats && (
                  <>
                    <div>Total Drafts: {cacheStats.totalDrafts}</div>
                    <div>Pending Sync: {cacheStats.pendingSync}</div>
                    <div>Sync Errors: {cacheStats.syncErrors}</div>
                    {cacheStats.lastSyncTime && (
                      <div>Last Sync: {cacheStats.lastSyncTime.toLocaleTimeString()}</div>
                    )}
                  </>
                )}
                <div>Queue Size: {offlineQueue.length}</div>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </TooltipProvider>
    )
  }

  if (!showDetails) {
    return (
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          {getConnectionIcon()}
          <span className="text-sm text-gray-600">{getConnectionText()}</span>
        </div>
        
        <div className="flex items-center space-x-1">
          {getSyncIcon()}
          <span className="text-sm text-gray-600">{getSyncText()}</span>
        </div>

        {!networkStatus.isOnline && (
          <Badge variant="outline" className="text-xs">
            <Database className="h-3 w-3 mr-1" />
            Cache Mode
          </Badge>
        )}
      </div>
    )
  }

  return (
    <Card className="w-full">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getConnectionIcon()}
              <span className="font-medium">Connection</span>
            </div>
            <Badge variant={getConnectionColor()}>
              {getConnectionText()}
            </Badge>
          </div>

          {/* Network Details */}
          {networkStatus.effectiveType && (
            <div className="text-sm text-gray-600">
              <div>Type: {networkStatus.effectiveType}</div>
              {networkStatus.downlink && (
                <div>Speed: {networkStatus.downlink} Mbps</div>
              )}
              {networkStatus.rtt && (
                <div>Latency: {networkStatus.rtt}ms</div>
              )}
            </div>
          )}

          {/* Sync Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getSyncIcon()}
              <span className="font-medium">Sync Status</span>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={getSyncColor()}>
                {getSyncText()}
              </Badge>
              {cacheStats?.syncErrors && cacheStats.syncErrors > 0 && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRetrySync}
                  className="text-xs"
                >
                  Retry
                </Button>
              )}
            </div>
          </div>

          {/* Cache Statistics */}
          {cacheStats && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Total Drafts:</span>
                <span className="font-medium">{cacheStats.totalDrafts}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Pending Sync:</span>
                <span className="font-medium">{cacheStats.pendingSync}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Sync Errors:</span>
                <span className="font-medium">{cacheStats.syncErrors}</span>
              </div>
              {cacheStats.lastSyncTime && (
                <div className="flex items-center justify-between text-sm">
                  <span>Last Sync:</span>
                  <span className="font-medium">
                    {cacheStats.lastSyncTime.toLocaleTimeString()}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Offline Queue */}
          {offlineQueue.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <span className="font-medium">Offline Queue</span>
              </div>
              <div className="text-sm text-gray-600">
                {offlineQueue.length} operations queued for sync
              </div>
            </div>
          )}

          {/* Cache Mode Indicator */}
          {!networkStatus.isOnline && (
            <div className="flex items-center space-x-2 p-2 bg-blue-50 rounded-lg">
              <Zap className="h-4 w-4 text-blue-600" />
              <div className="text-sm">
                <div className="font-medium text-blue-800">Cache Mode Active</div>
                <div className="text-blue-600">
                  Drafts are saved locally and will sync when online
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
