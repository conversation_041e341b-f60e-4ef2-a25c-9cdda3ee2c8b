import { getGmailClient } from './client'

/**
 * Common error handler for Gmail operations
 */
export function handleGmailError(error: any, operation: string): never {
  console.error(`Error ${operation}:`, error)
  
  if (error instanceof Error) {
    throw error
  }
  
  throw new Error(`Failed to ${operation}`)
}

/**
 * Safe error handler that returns a boolean result
 */
export function handleGmailErrorSafe(error: any, operation: string): boolean {
  console.error(`Error ${operation}:`, error)
  return false
}

/**
 * Standard error message formatter
 */
export function formatErrorMessage(error: any): string {
  return error instanceof Error ? error.message : 'Unknown error'
}

/**
 * Common Gmail API response handler with null safety
 */
export function safeGetProperty<T>(obj: any, property: string, defaultValue: T): T {
  return obj?.[property] || defaultValue
}

/**
 * Helper to build Gmail search queries with date filters
 */
export function buildGmailQuery(
  baseQuery: string,
  dateFrom?: Date,
  dateTo?: Date,
  additionalFilters?: string[]
): string {
  let query = baseQuery
  
  if (dateFrom) {
    query += ` after:${Math.floor(dateFrom.getTime() / 1000)}`
  }
  
  if (dateTo) {
    query += ` before:${Math.floor(dateTo.getTime() / 1000)}`
  }
  
  if (additionalFilters?.length) {
    query += ` ${additionalFilters.join(' ')}`
  }
  
  return query
}

/**
 * Standard Gmail message modify operation
 */
export async function modifyGmailMessage(
  userId: string,
  messageId: string,
  operation: {
    addLabelIds?: string[]
    removeLabelIds?: string[]
  },
  operationName: string
): Promise<boolean> {
  try {
    const { gmail } = await getGmailClient(userId)
    
    await gmail.users.messages.modify({
      userId: 'me',
      id: messageId,
      requestBody: operation
    })
    
    return true
  } catch (error) {
    return handleGmailErrorSafe(error, operationName)
  }
}

/**
 * Standard Gmail API call wrapper with error handling
 */
export async function executeGmailOperation<T>(
  userId: string,
  operation: (gmail: any) => Promise<T>,
  operationName: string
): Promise<T> {
  try {
    const { gmail } = await getGmailClient(userId)
    return await operation(gmail)
  } catch (error) {
    handleGmailError(error, operationName)
  }
}

/**
 * Safe Gmail API call wrapper that returns null on error
 */
export async function executeGmailOperationSafe<T>(
  userId: string,
  operation: (gmail: any) => Promise<T>,
  operationName: string
): Promise<T | null> {
  try {
    const { gmail } = await getGmailClient(userId)
    return await operation(gmail)
  } catch (error) {
    console.error(`Error ${operationName}:`, error)
    return null
  }
}

/**
 * Generate unique tracking ID
 */
export function generateTrackingId(): string {
  return `track_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Rate limiting utility
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Standard pagination parameters interface
 */
export interface PaginationParams {
  limit?: number
  pageToken?: string
  page?: number
  dateFrom?: Date
  dateTo?: Date
}

/**
 * Standard Gmail list response formatter
 */
export function formatGmailListResponse<T>(
  items: T[],
  nextPageToken?: string | null,
  totalCount?: number
): {
  emails: T[]
  nextPageToken?: string
  totalCount: number
} {
  return {
    emails: items,
    nextPageToken: nextPageToken || undefined,
    totalCount: totalCount || 0
  }
}

/**
 * Header extraction utility with null safety
 */
export function extractHeader(headers: any[], headerName: string): string {
  return headers.find((h: any) => 
    h.name?.toLowerCase() === headerName.toLowerCase()
  )?.value || ''
}

/**
 * Format headers array with null safety
 */
export function formatHeaders(headers: any[]): Array<{name: string, value: string}> {
  return headers.map((h: any) => ({
    name: h.name || '',
    value: h.value || ''
  }))
}

/**
 * Check if daily reset is needed
 */
export function shouldResetDailyCount(lastReset: Date | null): boolean {
  if (!lastReset) return true
  
  const today = new Date().toDateString()
  const lastResetString = new Date(lastReset).toDateString()
  
  return lastResetString !== today
}

/**
 * Calculate next reset time (midnight of next day)
 */
export function getNextResetTime(): Date {
  const resetTime = new Date()
  resetTime.setDate(resetTime.getDate() + 1)
  resetTime.setHours(0, 0, 0, 0)
  return resetTime
}

/**
 * Standard database operation wrapper with error handling
 */
export async function executeDatabaseOperation<T>(
  operation: () => Promise<T>,
  operationName: string,
  throwOnError: boolean = false
): Promise<T | null> {
  try {
    return await operation()
  } catch (error) {
    console.error(`Failed to ${operationName}:`, error)
    
    if (throwOnError) {
      throw error
    }
    
    return null
  }
} 