"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tags, Search, Filter, MoreHorizontal, Plus, Edit3, Trash2, Tag } from "lucide-react"
import { useState } from "react"

export default function LabelsPage() {
  const [searchTerm, setSearchTerm] = useState("")

  // Mock data for email labels
  const labels = [
    {
      id: 1,
      name: "High Priority",
      color: "bg-red-500",
      textColor: "text-white",
      emailCount: 23,
      description: "Urgent emails requiring immediate attention",
      lastUsed: "2024-01-15"
    },
    {
      id: 2,
      name: "Follow Up",
      color: "bg-yellow-500",
      textColor: "text-white",
      emailCount: 18,
      description: "Emails that need follow-up actions",
      lastUsed: "2024-01-14"
    },
    {
      id: 3,
      name: "VIP Customers",
      color: "bg-purple-500",
      textColor: "text-white",
      emailCount: 15,
      description: "Communications with VIP customers",
      lastUsed: "2024-01-13"
    },
    {
      id: 4,
      name: "Marketing",
      color: "bg-blue-500",
      textColor: "text-white",
      emailCount: 42,
      description: "Marketing campaigns and promotional emails",
      lastUsed: "2024-01-12"
    },
    {
      id: 5,
      name: "Internal",
      color: "bg-green-500",
      textColor: "text-white",
      emailCount: 8,
      description: "Internal team communications",
      lastUsed: "2024-01-11"
    },
    {
      id: 6,
      name: "Completed",
      color: "bg-gray-500",
      textColor: "text-white",
      emailCount: 67,
      description: "Completed tasks and resolved issues",
      lastUsed: "2024-01-10"
    }
  ]

  const filteredLabels = labels.filter(label =>
    label.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    label.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Email Labels</h1>
          <p className="text-muted-foreground">
            Create and manage labels to organize your emails
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Label
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Tags className="h-5 w-5 mr-2" />
                Manage Labels
              </CardTitle>
              <CardDescription>
                Create custom labels to tag and organize your email campaigns
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search labels..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredLabels.length === 0 ? (
              <div className="text-center py-8">
                <Tags className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No labels found</h3>
                <p className="text-gray-500">
                  {searchTerm ? "Try adjusting your search terms" : "Create your first label to get started"}
                </p>
              </div>
            ) : (
              filteredLabels.map((label) => (
                <div
                  key={label.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1">
                      <div 
                        className={`w-4 h-4 rounded-full ${label.color} flex items-center justify-center`}
                      >
                        <Tag className="h-2 w-2 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-1">
                          <h3 className="font-medium text-gray-900">{label.name}</h3>
                          <Badge 
                            variant="secondary" 
                            className={`${label.color} ${label.textColor} border-0 text-xs`}
                          >
                            {label.emailCount} emails
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-500 mb-2">{label.description}</p>
                        <p className="text-xs text-gray-400">Last used: {label.lastUsed}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" title="Edit label">
                        <Edit3 className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" title="Delete label">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 