"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Layout,
  Plus,
  FileText,
  ArrowRight
} from "lucide-react"
import Link from "next/link"

export default function TemplatesPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Templates</h2>
          <p className="text-muted-foreground">
            Create and manage reusable email templates for your campaigns.
          </p>
        </div>
      </div>

      {/* Coming Soon Content */}
      <div className="flex flex-col items-center justify-center space-y-6 py-16">
        <div className="relative">
          <div className="w-24 h-24 rounded-full bg-primary/10 flex items-center justify-center">
            <Layout className="h-12 w-12 text-primary" />
          </div>
          <div className="absolute -top-1 -right-1 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">Soon</span>
          </div>
        </div>
        
                  <div className="text-center space-y-4 max-w-md">
            <h3 className="text-2xl font-semibold">Templates Coming Soon</h3>
            <p className="text-muted-foreground">
              We&apos;re working on bringing you powerful email template management. 
              For now, you can compose emails directly using the full-featured email editor.
            </p>
          </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <Link href="/dashboard/compose">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Compose Email
            </Button>
          </Link>
          <Link href="/dashboard/emails">
            <Button variant="outline">
              <FileText className="mr-2 h-4 w-4" />
              View Email History
            </Button>
          </Link>
        </div>
      </div>

      {/* Feature Preview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 opacity-60">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Layout className="h-5 w-5" />
              Template Library
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Browse and use pre-built templates for common email types like newsletters, 
              welcome emails, and product announcements.
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Plus className="h-5 w-5" />
              Custom Templates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Create your own reusable templates with custom variables and 
              personalization fields for consistent branding.
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <ArrowRight className="h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Duplicate, edit, and share templates across your team. 
              Track template usage and performance metrics.
            </CardDescription>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 