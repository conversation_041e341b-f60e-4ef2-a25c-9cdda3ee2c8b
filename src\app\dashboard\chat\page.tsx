'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { ChatInterface } from '@/components/chat/chat-interface'
import { AIMessage } from '@/lib/ai-agent'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Bot, 
  Plus, 
  MessageSquare, 
  Sparkles,
  TrendingUp,
  Clock,
  Zap,
  Brain,
  Shield
} from "lucide-react"

interface ConversationSummary {
  id: string
  title: string
  lastMessage?: {
    role: string
    content: string
    timestamp: Date
  }
  updatedAt: Date
  createdAt: Date
}

interface AIStats {
  totalConversations: number
  messagesThisWeek: number
  tasksCreated: number
  emailsAnalyzed: number
}

export default function ChatPage() {
  const searchParams = useSearchParams()
  const sessionId = searchParams.get('session')
  
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(sessionId)
  const [messages, setMessages] = useState<AIMessage[]>([])
  const [conversations, setConversations] = useState<ConversationSummary[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [aiStats, setAiStats] = useState<AIStats>({
    totalConversations: 0,
    messagesThisWeek: 0,
    tasksCreated: 0,
    emailsAnalyzed: 0
  })

  // Load conversations on mount
  useEffect(() => {
    loadConversations()
    loadStats()
  }, [])

  // Load messages when conversation changes
  useEffect(() => {
    if (currentConversationId) {
      loadMessages(currentConversationId)
    } else {
      setMessages([])
    }
  }, [currentConversationId])

  const loadConversations = async () => {
    try {
      const response = await fetch('/api/chat/conversations')
      const data = await response.json()
      
      if (data.conversations) {
        setConversations(data.conversations)
      }
    } catch (error) {
      console.error('Error loading conversations:', error)
    }
  }

  const loadMessages = async (conversationId: string) => {
    try {
      const response = await fetch(`/api/chat/conversations/${conversationId}/messages`)
      const data = await response.json()
      
      if (data.messages) {
        setMessages(data.messages)
      }
    } catch (error) {
      console.error('Error loading messages:', error)
    }
  }

  const loadStats = async () => {
    try {
      const response = await fetch('/api/chat/stats')
      const data = await response.json()
      
      if (data.stats) {
        setAiStats(data.stats)
      }
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const handleSendMessage = async (content: string, conversationId?: string): Promise<AIMessage> => {
    setIsLoading(true)
    
    try {
      let targetConversationId = conversationId || currentConversationId

      // Create new conversation if none exists
      if (!targetConversationId) {
        const createResponse = await fetch('/api/chat/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ title: 'New Chat' })
        })
        
        const createData = await createResponse.json()
        if (createData.conversation) {
          targetConversationId = createData.conversation.id
          setCurrentConversationId(targetConversationId)
          await loadConversations() // Refresh conversations list
        }
      }

      // Send message
      const response = await fetch('/api/chat/message', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          conversationId: targetConversationId,
          content,
          context: {
            // Add any relevant context here
          }
        })
      })

      const data = await response.json()
      
      if (data.userMessage && data.aiMessage) {
        // Update messages state
        setMessages(prev => [...prev, data.userMessage, data.aiMessage])
        
        // Update conversations list
        await loadConversations()
        
        return data.aiMessage
      }
      
      throw new Error(data.error || 'Failed to send message')
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const createNewConversation = async () => {
    try {
      const response = await fetch('/api/chat/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: 'New Chat' })
      })
      
      const data = await response.json()
      
      if (data.conversation) {
        setCurrentConversationId(data.conversation.id)
        setMessages([])
        await loadConversations()
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-[#e8eaed]">
        <div>
          <h1 className="text-3xl font-bold text-[#202124] flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-[#4285f4] to-[#34a853] rounded-lg">
              <Brain className="h-8 w-8 text-white" />
            </div>
            AI Agent
          </h1>
          <p className="text-[#5f6368] mt-2">
            Your intelligent assistant for email, calendar, and task management
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <Badge variant="outline" className="text-xs">
            <Shield className="h-3 w-3 mr-1" />
            End-to-end encrypted
          </Badge>
          <Button onClick={createNewConversation} className="bg-[#1a73e8] hover:bg-[#1557b0]">
            <Plus className="h-4 w-4 mr-2" />
            New Chat
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="p-6 border-b border-[#e8eaed]">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="border-[#e8eaed]">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MessageSquare className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-[#202124]">
                    {aiStats.totalConversations}
                  </div>
                  <div className="text-sm text-[#5f6368]">
                    Total Conversations
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-[#e8eaed]">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-[#202124]">
                    {aiStats.messagesThisWeek}
                  </div>
                  <div className="text-sm text-[#5f6368]">
                    Messages This Week
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-[#e8eaed]">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Zap className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-[#202124]">
                    {aiStats.tasksCreated}
                  </div>
                  <div className="text-sm text-[#5f6368]">
                    Tasks Created
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-[#e8eaed]">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Sparkles className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-[#202124]">
                    {aiStats.emailsAnalyzed}
                  </div>
                  <div className="text-sm text-[#5f6368]">
                    Emails Analyzed
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 p-6">
        <ChatInterface
          conversationId={currentConversationId || undefined}
          onSendMessage={handleSendMessage}
          messages={messages}
          isLoading={isLoading}
          className="h-full"
        />
      </div>
    </div>
  )
}
