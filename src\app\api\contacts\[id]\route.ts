import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const { id: contactId } = await params

    // Check if contact exists and belongs to user
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId: user.id
      }
    })

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 })
    }

    // Delete the contact (this will also delete related ContactListMember records due to cascade)
    await prisma.contact.delete({
      where: { id: contactId }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting contact:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
   
        const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const { id: contactId } = await params
    const { firstName, lastName, email, customFields } = await request.json()

    // Check if contact exists and belongs to user
    const existingContact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId: user.id
      }
    })

    if (!existingContact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 })
    }

    // If email is being changed, check if new email already exists for this user
    if (email && email !== existingContact.email) {
      const emailExists = await prisma.contact.findFirst({
        where: {
          userId: user.id,
          email: email,
          id: { not: contactId }
        }
      })

      if (emailExists) {
        return NextResponse.json(
          { error: "Contact with this email already exists" },
          { status: 409 }
        )
      }
    }

    // Update the contact
    const updatedContact = await prisma.contact.update({
      where: { id: contactId },
      data: {
        firstName: firstName !== undefined ? firstName : existingContact.firstName,
        lastName: lastName !== undefined ? lastName : existingContact.lastName,
        email: email !== undefined ? email : existingContact.email,
        customFields: customFields !== undefined ? customFields : existingContact.customFields
      },
      include: {
        lists: {
          include: {
            contactList: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    })

    // Transform the response to match expected format
    const transformedContact = {
      id: updatedContact.id,
      firstName: updatedContact.firstName,
      lastName: updatedContact.lastName,
      email: updatedContact.email,
      customFields: updatedContact.customFields,
      contactLists: updatedContact.lists.map((cl: any) => cl.contactList),
      createdAt: updatedContact.createdAt.toISOString(),
      updatedAt: updatedContact.updatedAt.toISOString()
    }

    return NextResponse.json(transformedContact)
  } catch (error) {
    console.error("Error updating contact:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 