import { getGmailClient } from './client'
import { extractEnhancedEmailBody, checkForAttachments, extractAttachments } from './content-parser'
import { InboxEmail, EmailListResult } from './types'
import {
  buildGmailQuery,
  extractHeader,
  formatHeaders,
  formatGmailListResponse,
  executeGmailOperation,
  PaginationParams
} from './utils'

async function processEmailList(
  gmail: any,
  messages: any[],
  limit: number
): Promise<InboxEmail[]> {
  const emails: InboxEmail[] = []
  
  // Process emails in batches to avoid rate limits
  for (const message of messages.slice(0, limit)) {
    try {
      const emailDetails = await gmail.users.messages.get({
        userId: 'me',
        id: message.id,
        format: 'full'
      })

      const payload = emailDetails.data.payload
      if (!payload) continue
      
      const headers = payload.headers || []

      // Extract headers using utility function
      const fromHeader = extractHeader(headers, 'From')
      const toHeader = extractHeader(headers, 'To')
      const subjectHeader = extractHeader(headers, 'Subject')
      const dateHeader = extractHeader(headers, 'Date')
      const ccHeader = extractHeader(headers, 'Cc')
      const bccHeader = extractHeader(headers, 'Bcc')
      const replyToHeader = extractHeader(headers, 'Reply-To')
      const messageIdHeader = extractHeader(headers, 'Message-ID')
      const referencesHeader = extractHeader(headers, 'References')
      const inReplyToHeader = extractHeader(headers, 'In-Reply-To')

      // Parse labels
      const labels = emailDetails.data.labelIds?.map((labelId: string) => ({
        id: labelId,
        name: labelId,
        type: 'system'
      })) || []

      // Extract email body
      const body = extractEnhancedEmailBody(payload)

      // Check for attachments
      const hasAttachments = checkForAttachments(payload)
      const attachments = hasAttachments ? extractAttachments(payload) : []

      // Format headers using utility
      const formattedHeaders = formatHeaders(headers)

      emails.push({
        id: message.id,
        threadId: emailDetails.data.threadId || '',
        from: fromHeader,
        to: toHeader,
        subject: subjectHeader,
        date: dateHeader ? new Date(dateHeader) : new Date(),
        snippet: emailDetails.data.snippet || '',
        isRead: !emailDetails.data.labelIds?.includes('UNREAD'),
        isStarred: emailDetails.data.labelIds?.includes('STARRED') || false,
        hasAttachments,
        labels,
        body,
        attachments,
        rawPayload: payload,
        headers: formattedHeaders,
        cc: ccHeader || undefined,
        bcc: bccHeader || undefined,
        replyTo: replyToHeader || undefined,
        messageId: messageIdHeader || undefined,
        references: referencesHeader || undefined,
        inReplyTo: inReplyToHeader || undefined
      })
    } catch (error) {
      console.error('Error processing email:', error)
      // Continue processing other emails
    }
  }

  return emails
}

export async function getInboxEmails(
  userId: string,
  limit = 20,
  pageToken?: string,
  page?: number,
  labelId?: string,
  dateFrom?: Date,
  dateTo?: Date
): Promise<EmailListResult> {
  return executeGmailOperation(
    userId,
    async (gmail) => {
      // Build query using utility function
      const additionalFilters = labelId && labelId !== 'INBOX' ? [`label:${labelId}`] : []
      const query = buildGmailQuery('in:inbox', dateFrom, dateTo, additionalFilters)

      // PAGINATION FIX: Handle page-based pagination by fetching multiple pages if needed
      let currentPageToken = pageToken
      let allEmails: any[] = []
      let finalResponse: any = null
      
      // If page number is provided and > 1, we need to fetch previous pages to get to the right offset
      if (page && page > 1 && !pageToken) {
        console.log(`📧 [PAGINATION FIX] Fetching page ${page} by skipping ${(page - 1) * limit} emails`)
        
        // Fetch all pages up to the requested page
        let currentPage = 1
        let nextPageToken: string | undefined = undefined
        
        while (currentPage < page) {
          console.log(`📧 [PAGINATION FIX] Fetching intermediate page ${currentPage}`)
          
          const intermediateResponse: any = await gmail.users.messages.list({
            userId: 'me',
            q: query,
            maxResults: limit,
            pageToken: nextPageToken
          })
          
          nextPageToken = intermediateResponse.data.nextPageToken
          currentPage++
          
          // If no more pages available, break
          if (!nextPageToken && currentPage < page) {
            console.log(`📧 [PAGINATION FIX] Reached end of emails at page ${currentPage - 1}`)
            break
          }
        }
        
        // Now fetch the actual page we want
        currentPageToken = nextPageToken
      }

      console.log(`📧 [PAGINATION FIX] Final API call with pageToken: ${currentPageToken || 'undefined'}`)
      
      const response = await gmail.users.messages.list({
        userId: 'me',
        q: query,
        maxResults: limit,
        pageToken: currentPageToken
      })

      const messages = response.data.messages || []
      const emails = await processEmailList(gmail, messages, limit)

      console.log(`📧 [PAGINATION FIX] Retrieved ${emails.length} emails for page ${page || 1}`)
      if (emails.length > 0) {
        console.log(`📧 [PAGINATION FIX] First email ID: ${emails[0].id}`)
      }

      // Format response using utility
      return formatGmailListResponse(emails, response.data.nextPageToken, response.data.resultSizeEstimate)
    },
    'fetching inbox emails'
  )
}

export async function getEmailDetails(userId: string, messageId: string): Promise<InboxEmail | null> {
  return executeGmailOperation(
    userId,
    async (gmail) => {
      const response = await gmail.users.messages.get({
        userId: 'me',
        id: messageId,
        format: 'full'
      })

      const emailData = response.data
      const payload = emailData.payload
      if (!payload) return null
      
      const headers = payload.headers || []

      // Extract headers using utility function
      const fromHeader = extractHeader(headers, 'From')
      const toHeader = extractHeader(headers, 'To')
      const subjectHeader = extractHeader(headers, 'Subject')
      const dateHeader = extractHeader(headers, 'Date')
      const ccHeader = extractHeader(headers, 'Cc')
      const bccHeader = extractHeader(headers, 'Bcc')
      const replyToHeader = extractHeader(headers, 'Reply-To')
      const messageIdHeaderValue = extractHeader(headers, 'Message-ID')
      const referencesHeader = extractHeader(headers, 'References')
      const inReplyToHeader = extractHeader(headers, 'In-Reply-To')

      // Parse labels
      const labels = emailData.labelIds?.map((labelId: string) => ({
        id: labelId,
        name: labelId,
        type: 'system'
      })) || []

      // Extract email body
      const body = extractEnhancedEmailBody(payload)

      // Check for attachments
      const hasAttachments = checkForAttachments(payload)
      const attachments = hasAttachments ? extractAttachments(payload) : []

      // Format headers using utility
      const formattedHeaders = formatHeaders(headers)

      return {
        id: messageId,
        threadId: emailData.threadId || '',
        from: fromHeader,
        to: toHeader,
        subject: subjectHeader,
        date: dateHeader ? new Date(dateHeader) : new Date(),
        snippet: emailData.snippet || '',
        isRead: !emailData.labelIds?.includes('UNREAD'),
        isStarred: emailData.labelIds?.includes('STARRED') || false,
        hasAttachments,
        labels,
        body,
        attachments,
        rawPayload: payload,
        headers: formattedHeaders,
        cc: ccHeader || undefined,
        bcc: bccHeader || undefined,
        replyTo: replyToHeader || undefined,
        messageId: messageIdHeaderValue || undefined,
        references: referencesHeader || undefined,
        inReplyTo: inReplyToHeader || undefined
      }
    },
    'fetching email details'
  )
}

async function getEmailsByQuery(
  userId: string,
  baseQuery: string,
  params: PaginationParams = {}
): Promise<EmailListResult> {
  const { limit = 20, pageToken, page, dateFrom, dateTo } = params
  
  return executeGmailOperation(
    userId,
    async (gmail) => {
      // Build query using utility function
      const query = buildGmailQuery(baseQuery, dateFrom, dateTo)

      // PAGINATION FIX: Handle page-based pagination by fetching multiple pages if needed
      let currentPageToken = pageToken
      
      // If page number is provided and > 1, we need to fetch previous pages to get to the right offset
      if (page && page > 1 && !pageToken) {
        console.log(`📧 [PAGINATION FIX] ${baseQuery} - Fetching page ${page} by skipping ${(page - 1) * limit} emails`)
        
        // Fetch all pages up to the requested page
        let currentPage = 1
        let nextPageToken: string | undefined = undefined
        
        while (currentPage < page) {
          console.log(`📧 [PAGINATION FIX] ${baseQuery} - Fetching intermediate page ${currentPage}`)
          
          const intermediateResponse: any = await gmail.users.messages.list({
            userId: 'me',
            q: query,
            maxResults: limit,
            pageToken: nextPageToken
          })
          
          nextPageToken = intermediateResponse.data.nextPageToken
          currentPage++
          
          // If no more pages available, break
          if (!nextPageToken && currentPage < page) {
            console.log(`📧 [PAGINATION FIX] ${baseQuery} - Reached end of emails at page ${currentPage - 1}`)
            break
          }
        }
        
        // Now fetch the actual page we want
        currentPageToken = nextPageToken
      }

      console.log(`📧 [PAGINATION FIX] ${baseQuery} - Final API call with pageToken: ${currentPageToken || 'undefined'}`)

      const response = await gmail.users.messages.list({
        userId: 'me',
        q: query,
        maxResults: limit,
        pageToken: currentPageToken
      })

      const messages = response.data.messages || []
      const emails = await processEmailList(gmail, messages, limit)

      console.log(`📧 [PAGINATION FIX] ${baseQuery} - Retrieved ${emails.length} emails for page ${page || 1}`)
      if (emails.length > 0) {
        console.log(`📧 [PAGINATION FIX] ${baseQuery} - First email ID: ${emails[0].id}`)
      }

      // Format response using utility
      return formatGmailListResponse(emails, response.data.nextPageToken, response.data.resultSizeEstimate)
    },
    `fetching emails with query "${baseQuery}"`
  )
}

export async function getSentEmails(
  userId: string, 
  limit = 20, 
  pageToken?: string, 
  page?: number, 
  dateFrom?: Date, 
  dateTo?: Date
): Promise<EmailListResult> {
  return getEmailsByQuery(userId, 'in:sent', { limit, pageToken, page, dateFrom, dateTo })
}

export async function getDraftEmails(
  userId: string, 
  limit = 20, 
  pageToken?: string, 
  page?: number, 
  dateFrom?: Date, 
  dateTo?: Date
): Promise<EmailListResult> {
  return getEmailsByQuery(userId, 'in:drafts', { limit, pageToken, page, dateFrom, dateTo })
}

export async function getArchivedEmails(
  userId: string, 
  limit = 20, 
  pageToken?: string, 
  page?: number, 
  dateFrom?: Date, 
  dateTo?: Date
): Promise<EmailListResult> {
  return getEmailsByQuery(userId, '-in:inbox -in:spam -in:trash', { limit, pageToken, page, dateFrom, dateTo })
}

export async function getTrashEmails(
  userId: string, 
  limit = 20, 
  pageToken?: string, 
  page?: number, 
  dateFrom?: Date, 
  dateTo?: Date
): Promise<EmailListResult> {
  return getEmailsByQuery(userId, 'in:trash', { limit, pageToken, page, dateFrom, dateTo })
}

export async function getSpamEmails(
  userId: string, 
  limit = 20, 
  pageToken?: string, 
  page?: number, 
  dateFrom?: Date, 
  dateTo?: Date
): Promise<EmailListResult> {
  return getEmailsByQuery(userId, 'in:spam', { limit, pageToken, page, dateFrom, dateTo })
}

export async function getStarredEmails(
  userId: string, 
  limit = 20, 
  pageToken?: string, 
  page?: number, 
  dateFrom?: Date, 
  dateTo?: Date
): Promise<EmailListResult> {
  return getEmailsByQuery(userId, 'is:starred', { limit, pageToken, page, dateFrom, dateTo })
}

export async function getCategoryEmails(
  userId: string, 
  category: string, 
  limit = 20, 
  pageToken?: string, 
  page?: number, 
  dateFrom?: Date, 
  dateTo?: Date
): Promise<EmailListResult> {
  // Map category names to Gmail labels
  const categoryMap: Record<string, string> = {
    'primary': 'category:primary',
    'social': 'category:social',
    'promotions': 'category:promotions',
    'updates': 'category:updates',
    'forums': 'category:forums'
  }

  const categoryQuery = categoryMap[category.toLowerCase()] || `label:${category}`
  const query = `in:inbox ${categoryQuery}`
  
  return getEmailsByQuery(userId, query, { limit, pageToken, page, dateFrom, dateTo })
} 