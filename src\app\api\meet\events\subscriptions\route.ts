import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetUser, 
  handleMeetApiError, 
  formatMeetApiResponse,
  validatePaginationParams
} from "@/lib/meet"
import {
  createEventSubscription,
  listEventSubscriptions,
  setupCommonMeetSubscriptions,
  getAvailableMeetEventTypes,
  validateEventSubscriptionConfig
} from "@/lib/meet/events"

/**
 * GET /api/meet/events/subscriptions - List event subscriptions
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const searchParams = url.searchParams

    // Handle different query types
    const queryType = searchParams.get('type') || 'list'

    if (queryType === 'eventTypes') {
      // Return available event types
      const eventTypes = getAvailableMeetEventTypes()
      return formatMeetApiResponse(
        eventTypes,
        "Available Meet event types retrieved successfully"
      )
    }

    // List subscriptions
    const { pageSize, pageToken } = validatePaginationParams(searchParams)
    const filter = searchParams.get('filter') || undefined

    const subscriptions = await listEventSubscriptions(user.id, {
      pageSize,
      pageToken,
      filter
    })

    return formatMeetApiResponse(
      subscriptions,
      "Event subscriptions retrieved successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}

/**
 * POST /api/meet/events/subscriptions - Create event subscription
 */
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user } = authResult.data
    const body = await request.json()

    // Handle setup of common subscriptions
    if (body.setupCommon) {
      if (!body.pubsubTopic) {
        return NextResponse.json({
          error: "pubsubTopic is required for setting up common subscriptions"
        }, { status: 400 })
      }

      const subscriptions = await setupCommonMeetSubscriptions(user.id, body.pubsubTopic)
      
      return formatMeetApiResponse(
        subscriptions,
        "Common Meet event subscriptions created successfully"
      )
    }

    // Validate required fields
    const requiredFields = ['targetResource', 'eventTypes', 'pubsubTopic']
    const missingFields = requiredFields.filter(field => !body[field])
    
    if (missingFields.length > 0) {
      return NextResponse.json({
        error: `Missing required fields: ${missingFields.join(', ')}`
      }, { status: 400 })
    }

    // Validate configuration
    const validation = validateEventSubscriptionConfig({
      targetResource: body.targetResource,
      eventTypes: body.eventTypes,
      pubsubTopic: body.pubsubTopic
    })

    if (!validation.valid) {
      return NextResponse.json({
        error: "Invalid subscription configuration",
        details: validation.errors
      }, { status: 400 })
    }

    // Create the subscription
    const subscription = await createEventSubscription(user.id, {
      targetResource: body.targetResource,
      eventTypes: body.eventTypes,
      pubsubTopic: body.pubsubTopic,
      includeResource: body.includeResource,
      fieldMask: body.fieldMask
    })

    return formatMeetApiResponse(
      subscription,
      "Event subscription created successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
