import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database with Gmail info
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        email: true,
        gmailConnected: true,
        gmailRefreshToken: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if user has Gmail account linked
    const googleAccount = await prisma.account.findFirst({
      where: {
        userId: user.id,
        provider: "google",
        scope: {
          contains: "https://www.googleapis.com/auth/gmail.send"
        }
      },
      select: {
        refresh_token: true,
        access_token: true,
        scope: true,
        expires_at: true
      }
    })

    console.log("Gmail Status Check:", {
      userId: user.id,
      email: user.email,
      gmailConnected: user.gmailConnected,
      hasRefreshToken: !!user.gmailRefreshToken,
      hasGoogleAccount: !!googleAccount,
      googleAccountScope: googleAccount?.scope
    })

    return NextResponse.json({
      gmailConnected: user.gmailConnected,
      hasRefreshToken: !!user.gmailRefreshToken,
      hasGoogleAccount: !!googleAccount,
      accountScope: googleAccount?.scope,
      needsUpdate: !!googleAccount && !user.gmailConnected
    })

  } catch (error) {
    console.error("Error checking Gmail status:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
 
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if user has Gmail account linked
    const googleAccount = await prisma.account.findFirst({
      where: {
        userId: user.id,
        provider: "google",
        scope: {
          contains: "https://www.googleapis.com/auth/gmail.send"
        }
      }
    })

    if (googleAccount) {
      // Update user to mark Gmail as connected
      const updateData: any = {
        gmailConnected: true,
        dailySendLimit: 250,
        dailySendCount: 0,
        lastSendReset: new Date()
      }
      
      // Only store refresh token if available
      if (googleAccount.refresh_token) {
        updateData.gmailRefreshToken = googleAccount.refresh_token
      }
      
      await prisma.user.update({
        where: { id: user.id },
        data: updateData
      })

      return NextResponse.json({ 
        success: true, 
        message: "Gmail connection updated successfully" 
      })
    } else {
      return NextResponse.json({ 
        error: "No valid Gmail account found. Please reconnect with Google." 
      }, { status: 400 })
    }

  } catch (error) {
    console.error("Error updating Gmail status:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
} 