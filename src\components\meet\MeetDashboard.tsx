'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Video,
  Users,
  Clock,
  FileText,
  Download,
  TrendingUp,
  Activity,
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  RefreshCw,
  ExternalLink,
  Play,
  Mic,
  Settings,
  Filter,
  Plus
} from 'lucide-react'
import MeetingJoinInterface from './MeetingJoinInterface'

interface MeetDashboardProps {
  className?: string
}

interface DashboardData {
  recentMeetings: any[]
  statistics: {
    totalConferences: number
    totalDuration: number
    averageDuration: number
    totalParticipants: number
    averageParticipants: number
    conferencesWithRecordings: number
    conferencesWithTranscripts: number
    peakParticipants: number
    totalMeetingHours: number
    averageMeetingLength: number
    meetingsThisWeek: number
    meetingsLastWeek: number
    growthRate: number
  }
  upcomingMeetings: any[]
  totalArtifacts: {
    recordings: number
    transcripts: number
    totalSize: number
    downloadableRecordings: number
  }
  analytics: {
    dailyMeetings: Array<{ date: string; count: number }>
    participantTrends: Array<{ date: string; participants: number }>
    durationTrends: Array<{ date: string; duration: number }>
    topParticipants: Array<{ email: string; meetingCount: number }>
    meetingTypes: Array<{ type: string; count: number }>
  }
  insights: {
    mostActiveDay: string
    averageJoinTime: number
    commonMeetingDuration: number
    recordingUsage: number
    transcriptUsage: number
  }
}

export function MeetDashboard({ className }: MeetDashboardProps) {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const [refreshing, setRefreshing] = useState(false)
  const [selectedTab, setSelectedTab] = useState('overview')
  const [filterType, setFilterType] = useState('all')
  const [activeMeetingSpace, setActiveMeetingSpace] = useState<{
    name: string
    meetingUri: string
    meetingCode: string
    displayName?: string
    description?: string
    config?: any
  } | null>(null)
  const [showMeetingUI, setShowMeetingUI] = useState(false)

  useEffect(() => {
    fetchDashboardData()
  }, [selectedPeriod])

  const fetchDashboardData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }
      const response = await fetch(`/api/meet/dashboard?days=${selectedPeriod}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch dashboard data')
      }

      const result = await response.json()
      setDashboardData(result.data)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      console.error('Dashboard fetch error:', err)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const handleRefresh = () => {
    fetchDashboardData(true)
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
  }

  const formatGrowthRate = (rate: number) => {
    const sign = rate >= 0 ? '+' : ''
    return `${sign}${rate.toFixed(1)}%`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const createMeetingSpace = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/meet/spaces', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          displayName: `Meeting - ${new Date().toLocaleString()}`,
          description: 'Open meeting space for collaboration',
          config: {
            accessType: 'OPEN',
            entryPointAccess: 'ALL'
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create meeting space')
      }

      const result = await response.json()
      setActiveMeetingSpace(result.space)
      setShowMeetingUI(true)
    } catch (error) {
      console.error('Error creating meeting space:', error)
      setError('Failed to create meeting space')
    } finally {
      setLoading(false)
    }
  }

  const handleEndMeeting = () => {
    setShowMeetingUI(false)
    setActiveMeetingSpace(null)
    fetchDashboardData(true) // Refresh dashboard data
  }

  const handleLeaveMeeting = () => {
    setShowMeetingUI(false)
    // Keep the meeting space active but hide the UI
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => fetchDashboardData()} variant="outline">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!dashboardData) {
    return null
  }

  const { statistics, recentMeetings, totalArtifacts } = dashboardData

  // Show custom meeting UI if a meeting space is active
  if (showMeetingUI && activeMeetingSpace) {
    return (
      <div className="h-full">
        <ScrollArea className="h-full">
          <div className="p-4">
            <MeetingJoinInterface
              meetingSpace={activeMeetingSpace}
              onClose={handleLeaveMeeting}
            />
          </div>
        </ScrollArea>
      </div>
    )
  }

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Dashboard Header */}
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">Meet Dashboard</h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={createMeetingSpace}
              disabled={loading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Quick Meet
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="all">All Meetings</option>
            <option value="recorded">Recorded Only</option>
            <option value="transcribed">With Transcripts</option>
          </select>
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="space-y-6 pr-4">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Total Meetings
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">
                      {dashboardData?.statistics?.totalConferences || 0}
                    </div>
                    <Video className="h-4 w-4 text-blue-600" />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {dashboardData?.statistics?.meetingsThisWeek || 0} this week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Total Duration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">
                      {formatDuration(dashboardData?.statistics?.totalMeetingHours || 0)}
                    </div>
                    <Clock className="h-4 w-4 text-green-600" />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Avg: {formatDuration(dashboardData?.statistics?.averageMeetingLength || 0)}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Total Participants
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">
                      {dashboardData?.statistics?.totalParticipants || 0}
                    </div>
                    <Users className="h-4 w-4 text-purple-600" />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Avg: {Math.round(dashboardData?.statistics?.averageParticipants || 0)} per meeting
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    Growth Rate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">
                      {formatGrowthRate(dashboardData?.statistics?.growthRate || 0)}
                    </div>
                    <TrendingUp className="h-4 w-4 text-orange-600" />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    vs last period
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Tabs for detailed views */}
            <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="recent">Recent</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="artifacts">Artifacts</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* Recent Meetings */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5" />
                        Recent Meetings
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        {dashboardData?.recentMeetings?.length > 0 ? (
                          <div className="space-y-3 pr-4">
                            {dashboardData.recentMeetings.slice(0, 10).map((meeting: any, index: number) => (
                              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">
                                    {meeting.spaceName || `Meeting ${index + 1}`}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    {formatDate(meeting.startTime)} • {formatDuration(meeting.duration)}
                                  </p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge variant="secondary" className="text-xs">
                                    {meeting.participantCount} participants
                                  </Badge>
                                  {meeting.hasRecording && (
                                    <Badge variant="outline" className="text-xs">
                                      <Play className="h-3 w-3 mr-1" />
                                      Recorded
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            <Video className="h-8 w-8 mx-auto mb-2 opacity-50" />
                            <p>No recent meetings found</p>
                          </div>
                        )}
                      </ScrollArea>
                    </CardContent>
                  </Card>

                  {/* Upcoming Meetings */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        Upcoming Meetings
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        {dashboardData?.upcomingMeetings?.length > 0 ? (
                          <div className="space-y-3 pr-4">
                            {dashboardData.upcomingMeetings.map((meeting: any, index: number) => (
                              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">
                                    {meeting.summary || `Meeting ${index + 1}`}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    {formatDate(meeting.startTime)}
                                  </p>
                                </div>
                                <Button size="sm" variant="outline">
                                  <ExternalLink className="h-3 w-3 mr-1" />
                                  Join
                                </Button>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                            <p>No upcoming meetings</p>
                          </div>
                        )}
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Other tab contents with proper scrolling */}
              <TabsContent value="recent" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Meeting History</CardTitle>
                    <CardDescription>
                      Detailed view of your recent meetings with full information
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      {dashboardData?.recentMeetings?.length > 0 ? (
                        <div className="space-y-4 pr-4">
                          {dashboardData.recentMeetings.map((meeting: any, index: number) => (
                            <div key={index} className="border rounded-lg p-4">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium">
                                    {meeting.spaceName || `Meeting ${index + 1}`}
                                  </h4>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    Started: {formatDate(meeting.startTime)}
                                  </p>
                                  <div className="flex items-center gap-4 mt-2">
                                    <span className="text-sm">
                                      Duration: {formatDuration(meeting.duration)}
                                    </span>
                                    <span className="text-sm">
                                      Participants: {meeting.participantCount}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  {meeting.hasRecording && (
                                    <Badge variant="secondary">
                                      <Play className="h-3 w-3 mr-1" />
                                      Recording
                                    </Badge>
                                  )}
                                  {meeting.hasTranscript && (
                                    <Badge variant="outline">
                                      <FileText className="h-3 w-3 mr-1" />
                                      Transcript
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p>No meeting history available</p>
                        </div>
                      )}
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Analytics tab */}
              <TabsContent value="analytics" className="space-y-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Meeting Trends
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        <div className="space-y-3 pr-4">
                          {dashboardData?.analytics?.dailyMeetings?.map((day: any, index: number) => (
                            <div key={index} className="flex items-center justify-between">
                              <span className="text-sm">{day.date}</span>
                              <div className="flex items-center gap-2">
                                <div className="w-20 bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-blue-600 h-2 rounded-full" 
                                    style={{ width: `${Math.min((day.count / 10) * 100, 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium">{day.count}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <PieChart className="h-5 w-5" />
                        Top Participants
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        <div className="space-y-3 pr-4">
                          {dashboardData?.analytics?.topParticipants?.map((participant: any, index: number) => (
                            <div key={index} className="flex items-center justify-between">
                              <span className="text-sm truncate">{participant.email}</span>
                              <Badge variant="secondary">{participant.meetingCount} meetings</Badge>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Artifacts tab */}
              <TabsContent value="artifacts" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Meeting Artifacts
                    </CardTitle>
                    <CardDescription>
                      Recordings, transcripts, and other meeting artifacts
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {dashboardData?.totalArtifacts?.recordings || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Recordings</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {dashboardData?.totalArtifacts?.transcripts || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Transcripts</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">
                          {dashboardData?.totalArtifacts?.downloadableRecordings || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Downloadable</div>
                      </div>
                    </div>
                    <ScrollArea className="h-64">
                      <div className="space-y-3 pr-4">
                        {/* Artifact list would go here */}
                        <div className="text-center py-8 text-muted-foreground">
                          <Download className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p>Artifact management coming soon</p>
                        </div>
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </ScrollArea>
      </div>
    </div>
  )
}
