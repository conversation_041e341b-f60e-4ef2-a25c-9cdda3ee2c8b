import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetU<PERSON>, 
  handleMeetApiError, 
  formatMeetApiResponse,
  validatePaginationParams,
  getTranscript,
  getTranscriptDownloadInfo,
  getFullTranscriptText,
  listTranscriptEntries,
  searchTranscriptEntries
} from "@/lib/meet"

interface RouteParams {
  params: {
    conferenceId: string
    transcriptId: string
  }
}

/**
 * GET /api/meet/conferences/[conferenceId]/transcripts/[transcriptId] - Get transcript details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { transcriptId } = params
    
    // Decode the transcript ID in case it's URL encoded
    const decodedTranscriptId = decodeURIComponent(transcriptId)
    const searchParams = url.searchParams

    // Handle different query types
    const queryType = searchParams.get('type') || 'details'

    switch (queryType) {
      case 'fullText':
        return await handleFullTranscriptText(user.id, decodedTranscriptId)
      
      case 'entries':
        return await handleTranscriptEntries(user.id, decodedTranscriptId, searchParams)
      
      case 'search':
        return await handleTranscriptSearch(user.id, decodedTranscriptId, searchParams)
      
      case 'details':
      default:
        return await handleTranscriptDetails(user.id, decodedTranscriptId, searchParams)
    }
  } catch (error) {
    return handleMeetApiError(error)
  }
}

async function handleTranscriptDetails(
  userId: string, 
  transcriptId: string, 
  searchParams: URLSearchParams
) {
  const includeDownloadInfo = searchParams.get('includeDownloadInfo') === 'true'

  // Get transcript details
  const transcript = await getTranscript(userId, transcriptId)

  let response: any = transcript

  if (includeDownloadInfo) {
    try {
      const downloadInfo = await getTranscriptDownloadInfo(userId, transcriptId)
      response = {
        ...transcript,
        downloadInfo
      }
    } catch (error) {
      console.error(`Error getting download info for transcript ${transcriptId}:`, error)
      // Continue without download info if there's an error
    }
  }

  return formatMeetApiResponse(
    response,
    "Transcript details retrieved successfully"
  )
}

async function handleFullTranscriptText(userId: string, transcriptId: string) {
  const fullTranscript = await getFullTranscriptText(userId, transcriptId)

  return formatMeetApiResponse(
    fullTranscript,
    "Full transcript text retrieved successfully"
  )
}

async function handleTranscriptEntries(
  userId: string, 
  transcriptId: string, 
  searchParams: URLSearchParams
) {
  const { pageSize, pageToken } = validatePaginationParams(searchParams)

  const entries = await listTranscriptEntries(userId, transcriptId, {
    pageSize,
    pageToken
  })

  return formatMeetApiResponse(
    entries,
    "Transcript entries retrieved successfully"
  )
}

async function handleTranscriptSearch(
  userId: string, 
  transcriptId: string, 
  searchParams: URLSearchParams
) {
  const searchQuery = searchParams.get('q')
  if (!searchQuery) {
    return NextResponse.json({
      error: "Search query parameter 'q' is required"
    }, { status: 400 })
  }

  const caseSensitive = searchParams.get('caseSensitive') === 'true'
  const wholeWords = searchParams.get('wholeWords') === 'true'

  const searchResults = await searchTranscriptEntries(userId, transcriptId, searchQuery, {
    caseSensitive,
    wholeWords
  })

  return formatMeetApiResponse(
    searchResults,
    `Transcript search completed successfully. Found ${searchResults.totalMatches} matches.`
  )
}
