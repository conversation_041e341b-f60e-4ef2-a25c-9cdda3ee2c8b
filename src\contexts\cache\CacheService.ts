// Centralized Cache Service
// Core cache management system for all application data

import {
  UnifiedCacheData,
  BaseCacheEntry,
  CacheConfig,
  CacheStats,
  CacheOperation,
  CacheQuery,
  CacheInvalidation,
  CacheNamespace,
  CacheKey
} from './types'

export class CacheService {
  private cache: UnifiedCacheData
  private config: CacheConfig
  private stats: CacheStats
  private cleanupInterval: NodeJS.Timeout | null = null

  // Default configuration
  private static readonly DEFAULT_CONFIG: CacheConfig = {
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    maxSize: 1000,
    enablePersistence: false,
    enableCompression: false
  }

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...CacheService.DEFAULT_CONFIG, ...config }
    this.cache = this.initializeCache()
    this.stats = this.initializeStats()
    this.startCleanupInterval()
  }

  private initializeCache(): UnifiedCacheData {
    return {
      email: {
        emailDetails: {},
        categories: {},
        threads: {}
      },
      calendar: {
        eventDetails: {},
        calendars: {},
        freeBusy: {}
      },
      meet: {
        spaces: {},
        conferences: {},
        participants: {},
        analytics: {}
      },
      ai: {
        conversations: {},
        messages: {},
        userBehavior: {},
        knowledge: {},
        insights: {}
      },
      generic: {}
    }
  }

  private initializeStats(): CacheStats {
    return {
      totalEntries: 0,
      memoryUsage: 0,
      hitRate: 0,
      missRate: 0,
      lastCleanup: Date.now()
    }
  }

  private startCleanupInterval(): void {
    // Run cleanup every 10 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 10 * 60 * 1000)
  }

  // ============================================================================
  // CORE CACHE OPERATIONS
  // ============================================================================

  /**
   * Set data in cache with optional TTL
   */
  set<T>(namespace: CacheNamespace, key: CacheKey, data: T, ttl?: number): void {
    const now = Date.now()
    const entry: BaseCacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: ttl ? now + ttl : now + this.config.defaultTTL,
      version: 1
    }

    // Navigate to the correct namespace and set the data
    this.setNestedValue(this.cache[namespace], key, entry)
    this.updateStats('set')
    
    console.log(`💾 [CACHE] Set ${namespace}:${key}`)
  }

  /**
   * Get data from cache
   */
  get<T>(namespace: CacheNamespace, key: CacheKey): T | null {
    const entry = this.getNestedValue(this.cache[namespace], key) as BaseCacheEntry<T>
    
    if (!entry) {
      this.updateStats('miss')
      console.log(`❌ [CACHE] Miss ${namespace}:${key}`)
      return null
    }

    // Check if entry has expired
    if (entry.expiresAt && Date.now() > entry.expiresAt) {
      this.delete(namespace, key)
      this.updateStats('miss')
      console.log(`⏰ [CACHE] Expired ${namespace}:${key}`)
      return null
    }

    this.updateStats('hit')
    console.log(`✅ [CACHE] Hit ${namespace}:${key}`)
    return entry.data
  }

  /**
   * Check if cache entry exists and is valid
   */
  has(namespace: CacheNamespace, key: CacheKey): boolean {
    const entry = this.getNestedValue(this.cache[namespace], key) as BaseCacheEntry
    
    if (!entry) return false
    
    // Check expiration
    if (entry.expiresAt && Date.now() > entry.expiresAt) {
      this.delete(namespace, key)
      return false
    }
    
    return true
  }

  /**
   * Delete specific cache entry
   */
  delete(namespace: CacheNamespace, key: CacheKey): boolean {
    const deleted = this.deleteNestedValue(this.cache[namespace], key)
    if (deleted) {
      this.updateStats('delete')
      console.log(`🗑️ [CACHE] Deleted ${namespace}:${key}`)
    }
    return deleted
  }

  /**
   * Clear entire namespace or specific keys
   */
  clear(namespace?: CacheNamespace, keys?: CacheKey[]): void {
    if (!namespace) {
      // Clear entire cache
      this.cache = this.initializeCache()
      this.stats.totalEntries = 0
      console.log('🧹 [CACHE] Cleared entire cache')
      return
    }

    if (keys && keys.length > 0) {
      // Clear specific keys in namespace
      keys.forEach(key => this.delete(namespace, key))
      console.log(`🧹 [CACHE] Cleared ${keys.length} keys from ${namespace}`)
    } else {
      // Clear entire namespace
      this.cache[namespace] = this.initializeCache()[namespace]
      console.log(`🧹 [CACHE] Cleared namespace ${namespace}`)
    }
  }

  // ============================================================================
  // ADVANCED OPERATIONS
  // ============================================================================

  /**
   * Batch set multiple entries
   */
  setBatch(operations: CacheOperation[]): void {
    operations.forEach(op => {
      if (op.data !== undefined) {
        this.set(op.namespace, op.key, op.data, op.ttl)
      }
    })
    console.log(`📦 [CACHE] Batch set ${operations.length} entries`)
  }

  /**
   * Batch get multiple entries
   */
  getBatch<T>(queries: Array<{ namespace: CacheNamespace; key: CacheKey }>): Array<T | null> {
    return queries.map(query => this.get<T>(query.namespace, query.key))
  }

  /**
   * Query cache with filters and sorting
   */
  query<T>(query: CacheQuery): Array<{ key: CacheKey; data: T }> {
    const namespace = this.cache[query.namespace]
    const results: Array<{ key: CacheKey; data: T }> = []

    // This is a simplified implementation - in a real scenario,
    // you'd want more sophisticated querying capabilities
    this.traverseNamespace(namespace, (key, entry) => {
      if (entry && (!entry.expiresAt || Date.now() <= entry.expiresAt)) {
        if (!query.pattern || this.matchesPattern(key, query.pattern)) {
          results.push({ key, data: entry.data as T })
        }
      }
    })

    // Apply sorting if specified
    if (query.sortBy) {
      results.sort((a, b) => {
        const aVal = (a.data as any)[query.sortBy!]
        const bVal = (b.data as any)[query.sortBy!]
        const order = query.sortOrder === 'desc' ? -1 : 1
        return aVal > bVal ? order : aVal < bVal ? -order : 0
      })
    }

    // Apply pagination
    const start = query.offset || 0
    const end = query.limit ? start + query.limit : undefined
    return results.slice(start, end)
  }

  /**
   * Invalidate cache entries based on criteria
   */
  invalidate(criteria: CacheInvalidation): number {
    let invalidatedCount = 0

    if (criteria.namespace) {
      const namespace = this.cache[criteria.namespace]
      
      if (criteria.keys) {
        // Invalidate specific keys
        criteria.keys.forEach(key => {
          if (this.delete(criteria.namespace!, key)) {
            invalidatedCount++
          }
        })
      } else if (criteria.pattern) {
        // Invalidate by pattern
        this.traverseNamespace(namespace, (key, entry) => {
          if (this.matchesPattern(key, criteria.pattern!)) {
            if (this.delete(criteria.namespace!, key)) {
              invalidatedCount++
            }
          }
        })
      } else if (criteria.olderThan) {
        // Invalidate entries older than specified time
        const cutoff = Date.now() - criteria.olderThan
        this.traverseNamespace(namespace, (key, entry) => {
          if (entry && entry.timestamp < cutoff) {
            if (this.delete(criteria.namespace!, key)) {
              invalidatedCount++
            }
          }
        })
      }
    }

    console.log(`🔄 [CACHE] Invalidated ${invalidatedCount} entries`)
    return invalidatedCount
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * Get cache size for a namespace
   */
  getNamespaceSize(namespace: CacheNamespace): number {
    let count = 0
    this.traverseNamespace(this.cache[namespace], () => count++)
    return count
  }

  /**
   * Cleanup expired entries
   */
  cleanup(): number {
    const now = Date.now()
    let cleanedCount = 0

    Object.keys(this.cache).forEach(namespace => {
      this.traverseNamespace(this.cache[namespace as CacheNamespace], (key, entry) => {
        if (entry && entry.expiresAt && now > entry.expiresAt) {
          if (this.delete(namespace as CacheNamespace, key)) {
            cleanedCount++
          }
        }
      })
    })

    this.stats.lastCleanup = now
    console.log(`🧹 [CACHE] Cleanup removed ${cleanedCount} expired entries`)
    return cleanedCount
  }

  /**
   * Get cache memory usage estimation
   */
  getMemoryUsage(): number {
    let totalSize = 0

    Object.keys(this.cache).forEach(namespace => {
      this.traverseNamespace(this.cache[namespace as CacheNamespace], (key, entry) => {
        if (entry) {
          // Rough estimation of memory usage
          totalSize += JSON.stringify(entry.data).length * 2 // UTF-16 encoding
          totalSize += key.length * 2
          totalSize += 100 // Overhead for timestamps, metadata, etc.
        }
      })
    })

    this.stats.memoryUsage = totalSize
    return totalSize
  }

  /**
   * Optimize cache by removing least recently used entries when size limit is reached
   */
  optimizeCache(): number {
    if (!this.config.maxSize) return 0

    const currentSize = this.stats.totalEntries
    if (currentSize <= this.config.maxSize) return 0

    const entriesToRemove = currentSize - this.config.maxSize
    const allEntries: Array<{ namespace: CacheNamespace; key: string; timestamp: number }> = []

    // Collect all entries with their timestamps
    Object.keys(this.cache).forEach(namespace => {
      this.traverseNamespace(this.cache[namespace as CacheNamespace], (key, entry) => {
        if (entry) {
          allEntries.push({
            namespace: namespace as CacheNamespace,
            key,
            timestamp: entry.timestamp
          })
        }
      })
    })

    // Sort by timestamp (oldest first)
    allEntries.sort((a, b) => a.timestamp - b.timestamp)

    // Remove oldest entries
    let removedCount = 0
    for (let i = 0; i < Math.min(entriesToRemove, allEntries.length); i++) {
      const entry = allEntries[i]
      if (this.delete(entry.namespace, entry.key)) {
        removedCount++
      }
    }

    console.log(`🔧 [CACHE] Optimized cache: removed ${removedCount} LRU entries`)
    return removedCount
  }

  /**
   * Prefetch data based on usage patterns
   */
  async prefetchData(patterns: Array<{ namespace: CacheNamespace; key: string; fetcher: () => Promise<any> }>): Promise<void> {
    console.log(`🚀 [CACHE] Starting prefetch for ${patterns.length} patterns`)

    const prefetchPromises = patterns.map(async (pattern) => {
      try {
        // Only prefetch if not already cached
        if (!this.has(pattern.namespace, pattern.key)) {
          const data = await pattern.fetcher()
          this.set(pattern.namespace, pattern.key, data)
          console.log(`✅ [CACHE] Prefetched ${pattern.namespace}:${pattern.key}`)
        }
      } catch (error) {
        console.error(`❌ [CACHE] Failed to prefetch ${pattern.namespace}:${pattern.key}:`, error)
      }
    })

    await Promise.allSettled(prefetchPromises)
    console.log(`🏁 [CACHE] Prefetch completed`)
  }

  /**
   * Get cache health score (0-100)
   */
  getHealthScore(): number {
    const hitRate = this.stats.hitRate + this.stats.missRate > 0
      ? (this.stats.hitRate / (this.stats.hitRate + this.stats.missRate)) * 100
      : 0

    const memoryUsage = this.getMemoryUsage()
    const memoryScore = memoryUsage < 10 * 1024 * 1024 ? 100 : Math.max(0, 100 - (memoryUsage / (50 * 1024 * 1024)) * 100)

    const ageScore = this.getAverageEntryAge() < 5 * 60 * 1000 ? 100 : 50 // Prefer fresh data

    return Math.round((hitRate * 0.5 + memoryScore * 0.3 + ageScore * 0.2))
  }

  /**
   * Get average age of cache entries
   */
  private getAverageEntryAge(): number {
    const now = Date.now()
    let totalAge = 0
    let entryCount = 0

    Object.keys(this.cache).forEach(namespace => {
      this.traverseNamespace(this.cache[namespace as CacheNamespace], (key, entry) => {
        if (entry) {
          totalAge += now - entry.timestamp
          entryCount++
        }
      })
    })

    return entryCount > 0 ? totalAge / entryCount : 0
  }

  /**
   * Auto-optimize cache based on usage patterns
   */
  autoOptimize(): void {
    const healthScore = this.getHealthScore()

    if (healthScore < 70) {
      console.log(`🔧 [CACHE] Auto-optimization triggered (health: ${healthScore})`)

      // Clean up expired entries
      this.cleanup()

      // Optimize if over size limit
      this.optimizeCache()

      // Update memory usage
      this.getMemoryUsage()

      console.log(`✅ [CACHE] Auto-optimization completed (new health: ${this.getHealthScore()})`)
    }
  }

  /**
   * Destroy cache service and cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.cache = this.initializeCache()
    this.stats = this.initializeStats()
    console.log('💥 [CACHE] Cache service destroyed')
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private setNestedValue(obj: any, key: string, value: any): void {
    const keys = key.split('.')
    let current = obj
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) {
        current[keys[i]] = {}
      }
      current = current[keys[i]]
    }
    
    current[keys[keys.length - 1]] = value
  }

  private getNestedValue(obj: any, key: string): any {
    const keys = key.split('.')
    let current = obj
    
    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k]
      } else {
        return undefined
      }
    }
    
    return current
  }

  private deleteNestedValue(obj: any, key: string): boolean {
    const keys = key.split('.')
    let current = obj
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!(keys[i] in current)) {
        return false
      }
      current = current[keys[i]]
    }
    
    if (keys[keys.length - 1] in current) {
      delete current[keys[keys.length - 1]]
      return true
    }
    
    return false
  }

  private traverseNamespace(namespace: any, callback: (key: string, entry: BaseCacheEntry) => void): void {
    const traverse = (obj: any, prefix: string = '') => {
      Object.keys(obj).forEach(key => {
        const fullKey = prefix ? `${prefix}.${key}` : key
        const value = obj[key]
        
        if (value && typeof value === 'object' && 'data' in value && 'timestamp' in value) {
          callback(fullKey, value as BaseCacheEntry)
        } else if (value && typeof value === 'object') {
          traverse(value, fullKey)
        }
      })
    }
    
    traverse(namespace)
  }

  private matchesPattern(key: string, pattern: string | RegExp): boolean {
    if (typeof pattern === 'string') {
      return key.includes(pattern)
    }
    return pattern.test(key)
  }

  private updateStats(operation: 'set' | 'hit' | 'miss' | 'delete'): void {
    switch (operation) {
      case 'set':
        this.stats.totalEntries++
        break
      case 'hit':
        this.stats.hitRate++
        break
      case 'miss':
        this.stats.missRate++
        break
      case 'delete':
        this.stats.totalEntries = Math.max(0, this.stats.totalEntries - 1)
        break
    }
  }
}

// Singleton instance for global use
export const cacheService = new CacheService()
