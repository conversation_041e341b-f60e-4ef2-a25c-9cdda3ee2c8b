import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { startPerformanceMetric, endPerformanceMetric } from '@/lib/performance/monitor'

export async function GET(request: NextRequest) {
  try {
    // Start performance monitoring
    startPerformanceMetric('chat-stats-fetch', { endpoint: '/api/chat/stats' })

    const session = await getServerSession(authOptions) as any

    if (!session?.user?.email) {
      endPerformanceMetric('chat-stats-fetch')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = session.user.id

    // Get total conversations
    const totalConversations = await prisma.conversation.count({
      where: {
        userId,
        status: 'active'
      }
    })

    // Get messages this week
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)

    const messagesThisWeek = await prisma.message.count({
      where: {
        conversation: {
          userId
        },
        createdAt: {
          gte: oneWeekAgo
        }
      }
    })

    // Get tasks created
    const tasksCreated = await prisma.aITask.count({
      where: { userId }
    })

    // For now, set emails analyzed to 0 since we haven't implemented email integration yet
    const emailsAnalyzed = 0

    const stats = {
      totalConversations,
      messagesThisWeek,
      tasksCreated,
      emailsAnalyzed
    }

    // End performance monitoring
    const duration = endPerformanceMetric('chat-stats-fetch')
    if (duration && duration > 1000) {
      console.warn(`Slow chat stats fetch: ${duration.toFixed(2)}ms for user ${session.user.id}`)
    }

    return NextResponse.json({
      stats,
      success: true
    })

  } catch (error) {
    endPerformanceMetric('chat-stats-fetch')
    console.error('Get stats error:', error)
    return NextResponse.json(
      { error: 'Failed to get stats' },
      { status: 500 }
    )
  }
}
