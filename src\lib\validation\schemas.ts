import { z } from 'zod'

// ============================================================================
// EMAIL VALIDATION SCHEMAS
// ============================================================================

export const emailAddressSchema = z.string().email('Invalid email address')

export const emailRecipientSchema = z.object({
  email: emailAddressSchema,
  name: z.string().optional(),
  type: z.enum(['to', 'cc', 'bcc']).default('to')
})

export const emailComposeSchema = z.object({
  to: z.array(emailRecipientSchema).min(1, 'At least one recipient is required'),
  cc: z.array(emailRecipientSchema).optional().default([]),
  bcc: z.array(emailRecipientSchema).optional().default([]),
  subject: z.string().min(1, 'Subject is required').max(998, 'Subject too long'),
  content: z.string().min(1, 'Email content is required'),
  isHtml: z.boolean().default(false),
  threadId: z.string().optional(),
  replyToMessageId: z.string().optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    content: z.string(),
    mimeType: z.string()
  })).optional().default([]),
  htmlBody: z.string().optional(),
  textBody: z.string().optional(),
  campaignId: z.string().optional(),
  contactId: z.string().optional()
})

export const emailReplySchema = z.object({
  to: z.array(emailRecipientSchema).min(1, 'At least one recipient is required'),
  subject: z.string().min(1, 'Subject is required').max(998, 'Subject too long'),
  htmlBody: z.string().min(1, 'Email content is required'),
  textBody: z.string().optional(),
  threadId: z.string().min(1, 'Thread ID is required'),
  replyToMessageId: z.string().min(1, 'Reply to message ID is required'),
  attachments: z.array(z.object({
    filename: z.string(),
    content: z.string(),
    mimeType: z.string()
  })).optional().default([])
})

export const emailListParamsSchema = z.object({
  limit: z.number().int().min(1).max(100).default(20),
  page: z.number().int().min(1).default(1),
  pageToken: z.string().optional(),
  labelId: z.string().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  category: z.enum(['primary', 'social', 'promotions', 'updates', 'forums']).optional()
})

// Email category validation
export const emailCategorySchema = z.enum(['primary', 'social', 'promotions', 'updates', 'forums'])

// ============================================================================
// CALENDAR VALIDATION SCHEMAS
// ============================================================================

export const calendarEventSchema = z.object({
  summary: z.string().min(1, 'Event title is required').max(1024, 'Title too long'),
  description: z.string().max(8192, 'Description too long').optional(),
  location: z.string().max(1024, 'Location too long').optional(),
  start: z.object({
    dateTime: z.string().datetime().optional(),
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    timeZone: z.string().optional()
  }).refine(data => data.dateTime || data.date, {
    message: 'Either dateTime or date must be provided'
  }),
  end: z.object({
    dateTime: z.string().datetime().optional(),
    date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    timeZone: z.string().optional()
  }).refine(data => data.dateTime || data.date, {
    message: 'Either dateTime or date must be provided'
  }),
  attendees: z.array(z.object({
    email: emailAddressSchema,
    displayName: z.string().optional(),
    responseStatus: z.enum(['needsAction', 'declined', 'tentative', 'accepted']).optional()
  })).optional().default([]),
  recurrence: z.array(z.string()).optional(),
  reminders: z.object({
    useDefault: z.boolean().default(true),
    overrides: z.array(z.object({
      method: z.enum(['email', 'popup']),
      minutes: z.number().int().min(0).max(40320) // Max 4 weeks
    })).optional()
  }).optional()
})

export const calendarListParamsSchema = z.object({
  timeMin: z.string().datetime().optional(),
  timeMax: z.string().datetime().optional(),
  maxResults: z.number().int().min(1).max(2500).default(250),
  singleEvents: z.boolean().default(true),
  orderBy: z.enum(['startTime', 'updated']).default('startTime')
})

// ============================================================================
// MEET VALIDATION SCHEMAS
// ============================================================================

export const meetingSpaceConfigSchema = z.object({
  accessType: z.enum(['OPEN', 'TRUSTED', 'RESTRICTED']).default('TRUSTED'),
  entryPointAccess: z.enum(['ALL', 'CREATOR_APP_ONLY']).default('ALL')
})

export const meetConferenceParamsSchema = z.object({
  pageSize: z.number().int().min(1).max(100).default(20),
  pageToken: z.string().optional(),
  filter: z.string().optional(),
  type: z.enum(['list', 'recent', 'statistics', 'search']).default('list'),
  days: z.number().int().min(1).max(365).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  spaceName: z.string().optional(),
  minDuration: z.number().int().min(0).optional(),
  hasRecording: z.boolean().optional(),
  hasTranscript: z.boolean().optional()
})

// ============================================================================
// AI AGENT VALIDATION SCHEMAS
// ============================================================================

export const aiEmailAnalysisSchema = z.object({
  query: z.string().min(1, 'Query is required').max(500, 'Query too long'),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  category: z.enum(['all', 'inbox', 'sent', 'drafts', 'spam', 'trash']).default('all'),
  limit: z.number().int().min(1).max(100).default(50)
})

export const aiCalendarScheduleSchema = z.object({
  context: z.string().min(1, 'Meeting context is required').max(1000, 'Context too long'),
  attendees: z.array(emailAddressSchema).optional().default([]),
  preferredDate: z.string().datetime().optional(),
  preferredTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
  duration: z.number().int().min(15).max(480).default(60), // 15 min to 8 hours
  timezone: z.string().default('UTC'),
  meetingType: z.enum(['virtual', 'in-person', 'hybrid']).default('virtual'),
  location: z.string().max(500, 'Location too long').optional(),
  addMeetLink: z.boolean().default(true)
})

export const aiAvailabilitySchema = z.object({
  attendees: z.array(emailAddressSchema).default([]),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  duration: z.number().int().min(15).max(480).default(60),
  workingHours: z.object({
    start: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    end: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
  }).default({ start: '09:00', end: '17:00' }),
  timezone: z.string().default('UTC')
})

// ============================================================================
// CACHE VALIDATION SCHEMAS
// ============================================================================

export const cacheOperationSchema = z.object({
  namespace: z.enum(['email', 'calendar', 'meet', 'ai']),
  key: z.string().min(1, 'Cache key is required'),
  data: z.any().optional(),
  ttl: z.number().int().min(1000).optional() // Minimum 1 second
})

// ============================================================================
// PAGINATION VALIDATION SCHEMAS
// ============================================================================

export const paginationParamsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  pageToken: z.string().optional()
})

// ============================================================================
// DATE RANGE VALIDATION SCHEMAS
// ============================================================================

export const dateRangeSchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime()
}).refine(data => new Date(data.startDate) <= new Date(data.endDate), {
  message: 'Start date must be before or equal to end date'
})

// ============================================================================
// USER VALIDATION SCHEMAS
// ============================================================================

export const userSessionSchema = z.object({
  user: z.object({
    id: z.string().min(1, 'User ID is required'),
    email: emailAddressSchema,
    name: z.string().optional(),
    image: z.string().url().optional()
  })
})

// ============================================================================
// API RESPONSE VALIDATION SCHEMAS
// ============================================================================

export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  message: z.string().optional()
})

// ============================================================================
// VALIDATION HELPER FUNCTIONS
// ============================================================================

export function validateEmailAddress(email: string): boolean {
  return emailAddressSchema.safeParse(email).success
}

export function validateDateRange(startDate: string, endDate: string): boolean {
  return dateRangeSchema.safeParse({ startDate, endDate }).success
}

// Import consolidated sanitization utilities
import { sanitizeHtmlContent } from './utils'

export function sanitizeEmailContent(content: string): string {
  return sanitizeHtmlContent(content, { strictMode: true })
}

export function validateAndSanitizeHtml(html: string): string {
  return sanitizeHtmlContent(html, {
    maxSize: 1048576, // 1MB limit
    strictMode: true
  })
}
