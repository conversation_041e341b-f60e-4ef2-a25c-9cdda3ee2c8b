// Google Calendar API types
export interface GoogleCalendarEvent {
  id: string
  summary: string
  description?: string
  start: {
    dateTime?: string
    date?: string
    timeZone?: string
  }
  end: {
    dateTime?: string
    date?: string
    timeZone?: string
  }
  location?: string
  colorId?: string
  status?: string
  htmlLink?: string
  attendees?: Array<{ email: string; displayName?: string; responseStatus?: string }>
  conferenceData?: {
    entryPoints?: Array<{
      entryPointType: string
      uri: string
      label?: string
    }>
  }
  created?: string
  updated?: string
  creator?: {
    email: string
    displayName?: string
  }
  organizer?: {
    email: string
    displayName?: string
  }
  transparency?: string
  visibility?: string
  recurrence?: string[]
  reminders?: {
    useDefault: boolean
    overrides?: Array<{
      method: string
      minutes: number
    }>
  }
}

// Extended calendar event for local statistics and AI features
export interface ExtendedCalendarEvent extends GoogleCalendarEvent {
  // AI-generated properties (stored in description or extended properties)
  sourceType?: 'manual' | 'ai-extracted' | 'ai-generated'
  priority?: 'low' | 'medium' | 'high'
  category?: string
  urgency?: string
}

// Calendar statistics interface
export interface CalendarStats {
  totalEvents: number
  upcomingEvents: number
  thisWeekEvents: number
  calendarsCount: number
  todayEvents?: number
  aiGeneratedEvents?: number
}

// Event creation request
export interface CreateEventRequest {
  summary: string
  description?: string
  startDate: string
  endDate?: string
  isAllDay?: boolean
  location?: string
  attendees?: string[]
  addMeet?: boolean
  timeZone?: string
  reminders?: Array<{
    method: string
    minutes: number
  }>
}

// Event update request
export interface UpdateEventRequest extends Partial<CreateEventRequest> {
  eventId: string
} 