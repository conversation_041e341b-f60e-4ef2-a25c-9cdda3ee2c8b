"use client"

import * as React from "react"
import { CalendarIcon, X } from "lucide-react"
import { format } from "date-fns"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DateRangePickerProps {
  dateRange: DateRange | undefined
  onDateRangeChange: (range: DateRange | undefined) => void
  className?: string
  placeholder?: string
  showClearButton?: boolean
  allowSingleDate?: boolean
}

export function DateRangePicker({
  dateRange,
  onDateRangeChange,
  className,
  placeholder = "Select date range",
  showClearButton = true,
  allowSingleDate = false,
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onDateRangeChange(undefined)
  }

  const formatDateRange = () => {
    if (!dateRange?.from) return placeholder
    
    if (dateRange.to) {
      return `${format(dateRange.from, "MMM dd, yyyy")} - ${format(dateRange.to, "MMM dd, yyyy")}`
    } else {
      return allowSingleDate 
        ? format(dateRange.from, "MMM dd, yyyy")
        : `${format(dateRange.from, "MMM dd, yyyy")} - ...`
    }
  }

  const getCurrentDateInfo = () => {
    const today = new Date()
    return {
      formatted: format(today, "MMMM dd, yyyy"),
      dayName: format(today, "EEEE"),
      isToday: true
    }
  }

  const getQuickRanges = () => {
    const today = new Date()
    today.setHours(23, 59, 59, 999) // End of today
    
    const todayStart = new Date()
    todayStart.setHours(0, 0, 0, 0) // Start of today
    
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayStart = new Date(yesterday)
    yesterdayStart.setHours(0, 0, 0, 0)
    
    const lastWeek = new Date()
    lastWeek.setDate(today.getDate() - 7)
    lastWeek.setHours(0, 0, 0, 0)
    
    const lastMonth = new Date()
    lastMonth.setMonth(today.getMonth() - 1)
    lastMonth.setHours(0, 0, 0, 0)
    
    const lastThreeMonths = new Date()
    lastThreeMonths.setMonth(today.getMonth() - 3)
    lastThreeMonths.setHours(0, 0, 0, 0)

    return [
      { label: "Today", range: { from: todayStart, to: today }, isCurrent: true },
      { label: "Yesterday", range: { from: yesterdayStart, to: yesterday } },
      { label: "Last 7 days", range: { from: lastWeek, to: today } },
      { label: "Last 30 days", range: { from: lastMonth, to: today } },
      { label: "Last 3 months", range: { from: lastThreeMonths, to: today } },
    ]
  }

  const currentDate = getCurrentDateInfo()

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-[320px] justify-start text-left font-normal",
              "border-slate-200 dark:border-slate-700",
              "hover:border-slate-300 dark:hover:border-slate-600",
              "hover:bg-slate-50 dark:hover:bg-slate-950/50",
              "transition-all duration-200",
              "bg-white dark:bg-slate-950",
              !dateRange && "text-slate-500 dark:text-slate-400"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 shrink-0 opacity-70" />
            <span className="flex-1 truncate text-sm">
              {formatDateRange()}
            </span>
            {dateRange && showClearButton && (
              <X 
                className="ml-2 h-4 w-4 opacity-50 hover:opacity-100 transition-opacity cursor-pointer" 
                onClick={handleClear}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          className="w-auto p-0 shadow-xl border-slate-200 dark:border-slate-700" 
          align="start"
          sideOffset={4}
        >
          <div className="flex">
            {/* Quick Range Selector */}
            <div className="w-52 border-r border-slate-200 dark:border-slate-700 bg-slate-50/50 dark:bg-slate-900/20">
              <div className="p-4">
                {/* Current Date Display */}
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="text-xs font-medium text-blue-600 dark:text-blue-400 mb-1">Today</div>
                  <div className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                    {currentDate.formatted}
                  </div>
                  <div className="text-xs text-blue-700 dark:text-blue-300">
                    {currentDate.dayName}
                  </div>
                </div>

                <div className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                  Quick Select
                </div>
                <div className="space-y-1">
                  {getQuickRanges().map((item) => (
                    <Button
                      key={item.label}
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "w-full justify-start h-9 px-3 text-sm font-normal",
                        "hover:bg-slate-100 dark:hover:bg-slate-800/50",
                        "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100",
                        "transition-colors duration-150",
                        item.isCurrent && "bg-blue-50 dark:bg-blue-950/20 text-blue-700 dark:text-blue-300 font-medium"
                      )}
                      onClick={() => {
                        onDateRangeChange(item.range)
                        setIsOpen(false)
                      }}
                    >
                      {item.label}
                    </Button>
                  ))}
                  {dateRange && (
                    <>
                      <div className="my-3 border-t border-slate-200 dark:border-slate-700" />
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "w-full justify-start h-9 px-3 text-sm font-normal",
                          "text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20",
                          "hover:text-red-700 dark:hover:text-red-300"
                        )}
                        onClick={() => {
                          onDateRangeChange(undefined)
                          setIsOpen(false)
                        }}
                      >
                        Clear selection
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            {/* Calendar */}
            <div className="p-4 bg-white dark:bg-slate-950">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={(range) => {
                  onDateRangeChange(range)
                  // Close popover if both dates are selected or single date mode
                  if (range?.from && (range?.to || allowSingleDate)) {
                    setIsOpen(false)
                  }
                }}
                numberOfMonths={2}
                className="rounded-md"
                modifiers={{
                  today: new Date(),
                }}
                modifiersStyles={{
                  today: {
                    backgroundColor: '#2563eb',
                    color: 'white',
                    fontWeight: 'bold',
                    boxShadow: '0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.3)'
                  }
                }}
              />
              
              {/* Footer with Range Display */}
              <div className="flex items-center justify-between pt-4 mt-4 border-t border-slate-200 dark:border-slate-700">
                <div className="text-xs text-slate-500 dark:text-slate-400">
                  {dateRange?.from && dateRange?.to ? (
                    <div className="space-y-1">
                      <div className="font-medium">Selected Range:</div>
                      <div className="text-blue-600 dark:text-blue-400">
                        {format(dateRange.from, "MMM dd")} - {format(dateRange.to, "MMM dd, yyyy")}
                      </div>
                      <div>{Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1} days</div>
                    </div>
                  ) : dateRange?.from ? (
                    <div className="space-y-1">
                      <div className="font-medium">Start Date:</div>
                      <div className="text-blue-600 dark:text-blue-400">
                        {format(dateRange.from, "MMM dd, yyyy")}
                      </div>
                      <div className="text-amber-600 dark:text-amber-400">Select end date</div>
                    </div>
                  ) : (
                    "Select date range"
                  )}
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                  className="h-8 px-4 text-xs"
                >
                  Done
                </Button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
} 