'use client'

import React, { useEffect, useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  Video, 
  <PERSON>Off, 
  Mic, 
  MicOff, 
  Users, 
  Copy,
  ExternalLink,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { 
  initializeMeetAddonSDK,
  createEmbeddedMeetingSession,
  getCurrentMeetingInfo,
  getCurrentParticipants,
  setupParticipantListeners
} from '@/lib/meet/addon-sdk'

interface EmbeddedMeetingUIProps {
  meetingSpace: {
    name: string
    meetingUri: string
    meetingCode: string
    displayName?: string
    description?: string
    config?: {
      accessType: string
      entryPointAccess: string
    }
  }
  onClose: () => void
}

interface Participant {
  id: string
  name: string
  email?: string
  isOrganizer: boolean
  isPresenting: boolean
}

interface MeetingInfo {
  meetingId: string
  meetingCode: string
  organizer: Participant
}

export default function EmbeddedMeetingUI({ meetingSpace, onClose }: EmbeddedMeetingUIProps) {
  const [isInitializing, setIsInitializing] = useState(true)
  const [isConnected, setIsConnected] = useState(false)
  const [participants, setParticipants] = useState<Participant[]>([])
  const [meetingInfo, setMeetingInfo] = useState<MeetingInfo | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isEmbedded, setIsEmbedded] = useState(false)
  
  const meetingContainerRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()

  useEffect(() => {
    initializeEmbeddedMeeting()
  }, [])

  const initializeEmbeddedMeeting = async () => {
    try {
      setIsInitializing(true)
      setError(null)

      // Check if we're running inside Google Meet (as an add-on)
      const isInMeet = window.location.hostname.includes('meet.google.com') || 
                       window.parent !== window

      if (isInMeet) {
        // We're running as a Google Meet Add-on
        console.log('Initializing as Google Meet Add-on...')
        await initializeAsAddon()
      } else {
        // We're running in our own application - show join interface
        console.log('Running in standalone mode - showing join interface')
        setIsInitializing(false)
        setIsEmbedded(false)
      }
    } catch (error) {
      console.error('Failed to initialize embedded meeting:', error)
      setError(error instanceof Error ? error.message : 'Failed to initialize meeting')
      setIsInitializing(false)
    }
  }

  const initializeAsAddon = async () => {
    try {
      // Initialize the Google Meet Add-ons SDK
      await initializeMeetAddonSDK()
      
      // Create an embedded session
      const session = await createEmbeddedMeetingSession(meetingSpace.meetingUri)
      
      // Get meeting info and participants
      const info = await getCurrentMeetingInfo()
      const currentParticipants = await getCurrentParticipants()
      
      setMeetingInfo(info)
      setParticipants(currentParticipants)
      setIsConnected(true)
      setIsEmbedded(true)
      
      // Set up real-time participant listeners
      setupParticipantListeners(
        (participant) => {
          setParticipants(prev => [...prev, participant])
          toast({
            title: "Participant joined",
            description: `${participant.name} joined the meeting`,
          })
        },
        (participant) => {
          setParticipants(prev => prev.filter(p => p.id !== participant.id))
          toast({
            title: "Participant left",
            description: `${participant.name} left the meeting`,
          })
        }
      )
      
      toast({
        title: "Meeting connected",
        description: "Successfully connected to Google Meet",
      })
    } catch (error) {
      console.error('Failed to initialize as add-on:', error)
      // Fallback to join interface
      setIsEmbedded(false)
    } finally {
      setIsInitializing(false)
    }
  }

  const joinMeetingInNewTab = () => {
    window.open(meetingSpace.meetingUri, '_blank')
    toast({
      title: "Opening Google Meet",
      description: "Meeting opened in a new tab",
    })
  }

  const copyMeetingCode = async () => {
    try {
      await navigator.clipboard.writeText(meetingSpace.meetingCode)
      toast({
        title: "Copied!",
        description: "Meeting code copied to clipboard",
      })
    } catch (error) {
      console.error('Failed to copy meeting code:', error)
    }
  }

  const copyMeetingLink = async () => {
    try {
      await navigator.clipboard.writeText(meetingSpace.meetingUri)
      toast({
        title: "Copied!",
        description: "Meeting link copied to clipboard",
      })
    } catch (error) {
      console.error('Failed to copy meeting link:', error)
    }
  }

  if (isInitializing) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-muted-foreground">Initializing meeting...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Meeting Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">{error}</p>
            <div className="flex gap-2">
              <Button onClick={joinMeetingInNewTab} className="flex-1">
                <ExternalLink className="h-4 w-4 mr-2" />
                Open in Google Meet
              </Button>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isEmbedded && isConnected) {
    // Embedded Google Meet Add-on interface
    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
              Connected
            </Badge>
            <span className="font-medium">Meeting: {meetingSpace.meetingCode}</span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              <Users className="h-3 w-3 mr-1" />
              {participants.length}
            </Badge>
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        {/* Embedded Meeting Container */}
        <div 
          ref={meetingContainerRef}
          className="flex-1 bg-gray-900 relative"
          id="meet-addon-container"
        >
          {/* This is where the Google Meet Add-on content will be rendered */}
          <div className="absolute inset-0 flex items-center justify-center text-white">
            <div className="text-center space-y-4">
              <Video className="h-12 w-12 mx-auto opacity-50" />
              <p className="text-lg font-medium">Google Meet Session Active</p>
              <p className="text-sm opacity-75">
                Meeting controls and video are handled by Google Meet Add-on SDK
              </p>
            </div>
          </div>
        </div>

        {/* Participants Panel */}
        <div className="p-4 border-t bg-gray-50 dark:bg-gray-800">
          <h3 className="font-medium mb-3">Participants ({participants.length})</h3>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {participants.map((participant) => (
              <div key={participant.id} className="flex items-center gap-3 text-sm">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                  {participant.name.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1">
                  <div className="font-medium">{participant.name}</div>
                  {participant.email && (
                    <div className="text-xs text-muted-foreground">{participant.email}</div>
                  )}
                </div>
                {participant.isOrganizer && (
                  <Badge variant="secondary" className="text-xs">Host</Badge>
                )}
                {participant.isPresenting && (
                  <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">
                    Presenting
                  </Badge>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Standalone join interface (when not embedded in Google Meet)
  return (
    <div className="flex items-center justify-center min-h-[500px] p-6">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <Video className="h-6 w-6 text-blue-600" />
            Google Meet Session
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Meeting Info */}
          <div className="text-center space-y-2">
            <div className="text-2xl font-mono font-bold text-blue-600">
              {meetingSpace.meetingCode}
            </div>
            <div className="text-sm text-muted-foreground">
              Access Type: <Badge variant="outline">{meetingSpace.config?.accessType || 'OPEN'}</Badge>
            </div>
          </div>

          {/* Join Button */}
          <Button 
            onClick={joinMeetingInNewTab}
            className="w-full h-12 text-lg"
            size="lg"
          >
            <Video className="h-5 w-5 mr-2" />
            Join Google Meet
          </Button>

          {/* Copy Actions */}
          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" onClick={copyMeetingCode} className="flex-1">
              <Copy className="h-4 w-4 mr-2" />
              Copy Code
            </Button>
            <Button variant="outline" onClick={copyMeetingLink} className="flex-1">
              <Copy className="h-4 w-4 mr-2" />
              Copy Link
            </Button>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
              📋 How to Join:
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Click "Join Google Meet" to open the meeting</li>
              <li>• The meeting creator will admit external participants</li>
              <li>• Make sure you're signed in with your Google account</li>
              <li>• All participants will be visible once admitted by the host</li>
            </ul>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Close
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
