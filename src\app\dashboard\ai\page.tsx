'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"

import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { 
  Bot, 
  Mail, 
  Calendar, 
  TrendingUp, 
  Clock, 
  MessageSquare,
  Sparkles,
  Brain,
  Filter,
  Search,
  Zap,
  Target,
  Shield
} from "lucide-react"
import { EmailTile } from "@/components/ai/email-tile"
import { ChatInterface } from "@/components/ai/chat-interface"
import { EmailAnalysis, ActionItem, ChatMessage } from "@/lib/gemini"
import { ChatSession } from "@/lib/chat-encryption"

interface AIStats {
  totalEmails: number
  importantEmails: number
  actionItems: number
  highPriorityTasks: number
}

export default function AIPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [isChatLoading, setIsChatLoading] = useState(false)
  const [emails, setEmails] = useState<any[]>([])
  const [analyses, setAnalyses] = useState<EmailAnalysis[]>([])
  const [aiStats, setAiStats] = useState<AIStats>({
    totalEmails: 0,
    importantEmails: 0,
    actionItems: 0,
    highPriorityTasks: 0
  })
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedImportance, setSelectedImportance] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  
  // Chat state
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [hasAutoExtracted, setHasAutoExtracted] = useState(false)
  const [autoDetectedItems, setAutoDetectedItems] = useState<ActionItem[]>([])

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  useEffect(() => {
    if (session?.user?.email) {
      loadEmails()
    }
  }, [session])

  // Handle session from URL params
  useEffect(() => {
    const sessionParam = searchParams.get('session')
    if (sessionParam && sessionParam !== currentSessionId) {
      loadChatSession(sessionParam)
    } else if (!sessionParam && !currentSession) {
      // Create a new session if none exists
      createNewChatSession()
    }
  }, [searchParams, currentSessionId])

  // Auto-detect bills and due dates from emails on page load
  useEffect(() => {
    if (emails.length > 0 && !hasAutoExtracted) {
      const extractBillsFromEmails = async () => {
        try {
          // Use the enhanced gemini service to extract due dates from emails
          const response = await fetch('/api/ai/extract-emails-due-dates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              emails: emails.slice(0, 10) // Process last 10 emails
            })
          })

          if (response.ok) {
            const data = await response.json()
            
            if (data.items && data.items.length > 0) {
              setAutoDetectedItems(data.items)
              
              // Show notification
                             const billCount = data.items.filter((item: ActionItem) => item.type === 'bill' || item.type === 'payment').length
              const otherCount = data.items.length - billCount
              
              let message = `🤖 Auto-detected ${data.items.length} calendar item(s):`
              if (billCount > 0) message += ` ${billCount} bill(s)/payment(s)`
              if (otherCount > 0) message += ` ${otherCount} other item(s)`
              
              console.log(message, data.items)
            }
          }
        } catch (error) {
          console.error('Error auto-extracting due dates from emails:', error)
        } finally {
          setHasAutoExtracted(true)
        }
      }

      extractBillsFromEmails()
    }
  }, [emails, hasAutoExtracted])

  const createNewChatSession = async () => {
    try {
      const response = await fetch('/api/chat/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: 'New Chat' })
      })
      
      const data = await response.json()
      
      if (data.session) {
        setCurrentSession(data.session)
        setChatMessages([])
        setCurrentSessionId(data.session.id)
        
        // Update URL without page reload
        const newUrl = `/dashboard/ai?session=${data.session.id}`
        window.history.replaceState({}, '', newUrl)
      }
    } catch (error) {
      console.error('Error creating session:', error)
    }
  }

  const loadChatSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/chat/sessions/${sessionId}`)
      const data = await response.json()
      
      if (data.session) {
        setCurrentSession(data.session)
        setChatMessages(data.session.messages || [])
        setCurrentSessionId(sessionId)
      }
    } catch (error) {
      console.error('Error loading session:', error)
    }
  }

  const updateChatSession = async (messages: ChatMessage[], title?: string) => {
    if (!currentSession) return

    try {
      // Only update title from first user message if it's still "New Chat"
      const updatedTitle = title || 
        (currentSession.title === 'New Chat' && messages.length > 0 && messages[0].role === 'user' ? 
          messages[0].content.substring(0, 30) + (messages[0].content.length > 30 ? '...' : '') : 
          currentSession.title)

      const response = await fetch('/api/chat/sessions', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId: currentSession.id,
          title: updatedTitle,
          messages: messages
        })
      })
      
      if (response.ok) {
        setCurrentSession(prev => prev ? { ...prev, title: updatedTitle, messages, updatedAt: new Date() } : null)
      }
    } catch (error) {
      console.error('Error updating session:', error)
    }
  }

  const loadEmails = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/gmail/inbox')
      const data = await response.json()
      
      if (data.emails) {
        setEmails(data.emails)
      }
    } catch (error) {
      console.error('Error loading emails:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const analyzeEmails = async (userPrompt: string = '') => {
    if (emails.length === 0) {
      await loadEmails()
      return
    }

    try {
      setIsLoading(true)
      const response = await fetch('/api/ai/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          emails,
          userPrompt: userPrompt || 'Analyze these emails for importance and extract actionable items',
          days: 2
        })
      })

      const data = await response.json()
      
      if (data.analyses) {
        setAnalyses(data.analyses)
        
        // Calculate stats
        const totalActionItems = data.analyses.reduce((sum: number, analysis: EmailAnalysis) => 
          sum + analysis.actionItems.length, 0)
        const highPriorityTasks = data.analyses.reduce((sum: number, analysis: EmailAnalysis) => 
          sum + analysis.actionItems.filter(item => item.urgency === 'high').length, 0)

        setAiStats({
          totalEmails: data.totalAnalyzed,
          importantEmails: data.importantCount,
          actionItems: totalActionItems,
          highPriorityTasks
        })
      }
    } catch (error) {
      console.error('Error analyzing emails:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleChatMessage = async (message: string, context?: any): Promise<string> => {
    if (!currentSession) {
      await createNewChatSession()
      return 'Please try again.'
    }

    try {
      setIsChatLoading(true)
      
      // Add user message
      const userMessage: ChatMessage = {
        id: `msg-${Date.now()}`,
        role: 'user',
        content: message,
        timestamp: new Date(),
        context: { emailIds: context?.emailContext?.map((e: any) => e.id) }
      }

      const updatedMessages = [...chatMessages, userMessage]
      setChatMessages(updatedMessages)

      // Check if this is a calendar request
      const calendarKeywords = /\b(add|schedule|calendar|remind|meeting|appointment|bill|due|deadline|event)\b/gi
      const isCalendarRequest = calendarKeywords.test(message)

      // Get AI response
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [
            ...chatMessages.slice(-5), // Include last 5 messages for context
            userMessage
          ],
          context: {
            emails: context?.emailContext || emails.slice(0, 10), // Limit context size
            analyses: analyses,
            isCalendarRequest,
            ...context
          }
        })
      })

      const data = await response.json()
      let aiResponse = data.response || 'Sorry, I could not process your request.'

      // If it's a calendar request, try to extract and add calendar items
      if (isCalendarRequest) {
        try {
          // Extract calendar items from the message
          const calendarResponse = await fetch('/api/ai/extract-calendar', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              message: message,
              emailContext: emails.slice(0, 5)
            })
          })
          
          if (calendarResponse.ok) {
            const calendarData = await calendarResponse.json()
            
            if (calendarData.items && calendarData.items.length > 0) {
              // Add calendar items
              for (const item of calendarData.items) {
                await handleAddToCalendar(item)
              }
              
              aiResponse += `<br><br><strong>✅ Added ${calendarData.items.length} item(s) to your calendar:</strong><br>`
              aiResponse += calendarData.items.map((item: any) => 
                `• <strong>${item.title}</strong> - ${item.dueDate ? new Date(item.dueDate).toLocaleDateString() : 'No date specified'}`
              ).join('<br>')
            }
          }
        } catch (calendarError) {
          console.error('Calendar extraction error:', calendarError)
          aiResponse += '<br><br><em>Note: Could not automatically add to calendar. Please use the manual "Add to Calendar" feature.</em>'
        }
      }

      // Add AI response
      const aiMessage: ChatMessage = {
        id: `msg-${Date.now()}-ai`,
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date()
      }

      const finalMessages = [...updatedMessages, aiMessage]
      setChatMessages(finalMessages)

      // Update session in background
      await updateChatSession(finalMessages)

      // If the message is requesting email analysis, trigger analysis
      if (message.toLowerCase().includes('pull') && message.toLowerCase().includes('emails')) {
        await analyzeEmails(message)
      }

      return aiResponse
    } catch (error) {
      console.error('Chat error:', error)
      throw error
    } finally {
      setIsChatLoading(false)
    }
  }

  const handleOpenEmail = (emailId: string) => {
    router.push(`/dashboard/mail/inbox/${emailId}`)
  }

  const handleAddToCalendar = async (actionItem: ActionItem) => {
    try {
      const eventData = {
        summary: `[AI] ${actionItem.title}`,
        description: `${actionItem.description}\n\nsourceType:ai-generated\nurgency:${actionItem.urgency}\ntype:${actionItem.type}`,
        startDate: actionItem.dueDate ? actionItem.dueDate.toISOString() : new Date().toISOString(),
        endDate: actionItem.dueDate ? 
          new Date(actionItem.dueDate.getTime() + 60 * 60 * 1000).toISOString() : 
          new Date(Date.now() + 60 * 60 * 1000).toISOString(),
        isAllDay: !actionItem.dueDate,
        location: actionItem.location,
        attendees: actionItem.attendees || []
      }

      const response = await fetch('/api/calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      })

      if (response.ok) {
        alert(`Successfully added "${actionItem.title}" to calendar!`)
      } else {
        throw new Error('Failed to create calendar event')
      }
    } catch (error) {
      console.error('Error adding to calendar:', error)
      alert('Failed to add item to calendar')
    }
  }

  const handleAddAllToCalendar = async (actionItems: ActionItem[]) => {
    try {
      let successCount = 0
      
      for (const item of actionItems) {
        const eventData = {
          summary: `[AI] ${item.title}`,
          description: `${item.description}\n\nsourceType:ai-generated\nurgency:${item.urgency}\ntype:${item.type}`,
          startDate: item.dueDate ? item.dueDate.toISOString() : new Date().toISOString(),
          endDate: item.dueDate ? 
            new Date(item.dueDate.getTime() + 60 * 60 * 1000).toISOString() : 
            new Date(Date.now() + 60 * 60 * 1000).toISOString(),
          isAllDay: !item.dueDate,
          location: item.location,
          attendees: item.attendees || []
        }

        const response = await fetch('/api/calendar', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(eventData)
        })

        if (response.ok) {
          successCount++
        }
      }
      
      if (successCount === actionItems.length) {
        alert(`Successfully added all ${actionItems.length} events to calendar!`)
        router.push('/dashboard/calendar')
      } else {
        alert(`Added ${successCount} out of ${actionItems.length} events to calendar`)
      }
    } catch (error) {
      console.error('Error adding multiple items to calendar:', error)
      alert('Failed to add some events to calendar')
    }
  }

  // Filter analyses based on selections
  const filteredAnalyses = analyses.filter(analysis => {
    const matchesCategory = selectedCategory === 'all' || analysis.category.toLowerCase() === selectedCategory.toLowerCase()
    const matchesImportance = selectedImportance === 'all' || analysis.importance === selectedImportance
    const matchesSearch = searchQuery === '' || 
      analysis.summary.toLowerCase().includes(searchQuery.toLowerCase()) ||
      emails.find(e => e.id === analysis.id)?.subject.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesCategory && matchesImportance && matchesSearch
  })

  const categories = [...new Set(analyses.map(a => a.category))]
  const importanceLevels = ['high', 'medium', 'low']

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#1a73e8]"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-6 bg-white min-h-screen">
      {/* Auto-detected Items Alert */}
      {autoDetectedItems.length > 0 && (
        <Card className="border-l-4 border-l-[#34a853] bg-[#e6f4ea]">
          <CardContent className="pt-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-[#34a853] rounded-full">
                  <Calendar className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-medium text-[#137333]">
                    🤖 Auto-detected {autoDetectedItems.length} important item(s) from your emails
                  </h3>
                  <p className="text-sm text-[#137333] mt-1">
                    Bills, due dates, and appointments found. Click to add to your calendar.
                  </p>
                  <div className="mt-3 space-y-2">
                                         {autoDetectedItems.slice(0, 3).map((item: ActionItem, index: number) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <Badge variant={item.type === 'bill' ? 'destructive' : 'default'} className="text-xs">
                          {item.type}
                        </Badge>
                        <span className="font-medium">{item.title}</span>
                        <span className="text-[#5f6368]">
                          {item.dueDate ? new Date(item.dueDate).toLocaleDateString() : 'No date'}
                        </span>
                        {item.amount && <span className="text-[#ea4335] font-medium">${item.amount}</span>}
                      </div>
                    ))}
                    {autoDetectedItems.length > 3 && (
                      <p className="text-xs text-[#5f6368]">
                        ...and {autoDetectedItems.length - 3} more items
                      </p>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button 
                  size="sm" 
                  onClick={() => handleAddAllToCalendar(autoDetectedItems)}
                  className="bg-[#34a853] hover:bg-[#137333]"
                >
                  Add All to Calendar
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => setAutoDetectedItems([])}
                >
                  Dismiss
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-[#202124] flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-[#1a73e8] to-[#4285f4] rounded-lg">
              <Brain className="h-8 w-8 text-white" />
            </div>
            AI Email Intelligence
          </h1>
          <p className="text-[#5f6368] mt-2">
            Powered by Gemini AI • End-to-end encrypted • Auto-detects bills & due dates
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Badge variant="outline" className="bg-[#e8f0fe] text-[#1a73e8] border-[#1a73e8]">
            <Shield className="h-3 w-3 mr-1" />
            Encrypted
          </Badge>
          <Button 
            onClick={() => analyzeEmails()}
            disabled={isLoading}
            className="bg-[#1a73e8] hover:bg-[#1557b0]"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            {isLoading ? 'Analyzing...' : 'Analyze Emails'}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Total Emails
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#202124]">{aiStats.totalEmails}</div>
            <p className="text-xs text-[#5f6368] mt-1">Last 2 days</p>
          </CardContent>
        </Card>

        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <Target className="h-4 w-4" />
              Important Emails
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#1a73e8]">{aiStats.importantEmails}</div>
            <p className="text-xs text-[#5f6368] mt-1">High & medium priority</p>
          </CardContent>
        </Card>

        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Action Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#34a853]">{aiStats.actionItems}</div>
            <p className="text-xs text-[#5f6368] mt-1">Tasks, meetings, reminders</p>
          </CardContent>
        </Card>

        <Card className="border-[#e8eaed]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#5f6368] flex items-center gap-2">
              <Zap className="h-4 w-4" />
              High Priority
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#ea4335]">{aiStats.highPriorityTasks}</div>
            <p className="text-xs text-[#5f6368] mt-1">Urgent tasks</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="analysis" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="analysis" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Email Analysis
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            AI Assistant
          </TabsTrigger>
        </TabsList>

        <TabsContent value="analysis" className="space-y-4">
          {/* Filters */}
          {analyses.length > 0 && (
            <Card className="border-[#e8eaed]">
              <CardHeader>
                <CardTitle className="text-lg text-[#202124] flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filter & Search
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium text-[#202124]">Category:</label>
                    <select 
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className="border border-[#e8eaed] rounded px-3 py-1 text-sm"
                    >
                      <option value="all">All Categories</option>
                      {categories.map(cat => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                    </select>
                  </div>

                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium text-[#202124]">Importance:</label>
                    <select 
                      value={selectedImportance}
                      onChange={(e) => setSelectedImportance(e.target.value)}
                      className="border border-[#e8eaed] rounded px-3 py-1 text-sm"
                    >
                      <option value="all">All Levels</option>
                      {importanceLevels.map(level => (
                        <option key={level} value={level}>{level}</option>
                      ))}
                    </select>
                  </div>

                  <div className="flex items-center gap-2 flex-1 max-w-md">
                    <Search className="h-4 w-4 text-[#5f6368]" />
                    <Input
                      placeholder="Search emails..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="border-[#e8eaed]"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Email Analysis Results */}
          <div className="space-y-4">
            {isLoading && (
              <Card className="border-[#e8eaed]">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Bot className="h-12 w-12 mx-auto text-[#1a73e8] animate-pulse mb-4" />
                    <h3 className="text-lg font-medium text-[#202124] mb-2">
                      Analyzing Your Emails...
                    </h3>
                    <p className="text-[#5f6368]">
                      AI is processing your emails to identify important messages and extract action items.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {!isLoading && analyses.length === 0 && (
              <Card className="border-[#e8eaed]">
                <CardContent className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Mail className="h-12 w-12 mx-auto text-[#5f6368] mb-4" />
                    <h3 className="text-lg font-medium text-[#202124] mb-2">
                      No Analysis Available
                    </h3>
                    <p className="text-[#5f6368] mb-4">
                      Click &quot;Analyze Emails&quot; to start AI analysis of your recent emails.
                    </p>
                    <Button onClick={() => analyzeEmails()} className="bg-[#1a73e8] hover:bg-[#1557b0]">
                      <Sparkles className="h-4 w-4 mr-2" />
                      Start Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {filteredAnalyses.map((analysis) => {
              const originalEmail = emails.find(e => e.id === analysis.id)
              if (!originalEmail) return null

              return (
                <EmailTile
                  key={analysis.id}
                  analysis={analysis}
                  originalEmail={originalEmail}
                  onOpenEmail={handleOpenEmail}
                  onAddToCalendar={handleAddToCalendar}
                  onAddAllToCalendar={handleAddAllToCalendar}
                />
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="chat" className="h-[800px]">
          <Card className="h-full border-[#e8eaed]">
            <ChatInterface 
              onSendMessage={handleChatMessage}
              emailContext={emails}
              messages={chatMessages}
              isLoading={isChatLoading}
              className="h-full"
            />
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 