import { NextRequest, NextResponse } from "next/server"
import { 
  authenticateMeetUser, 
  handleMeetApiError, 
  formatMeetApiResponse,
  validatePaginationParams,
  getParticipant,
  listParticipantSessions
} from "@/lib/meet"

interface RouteParams {
  params: {
    conferenceId: string
    participantId: string
  }
}

/**
 * GET /api/meet/conferences/[conferenceId]/participants/[participantId] - Get participant details
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user, url } = authResult.data
    const { participantId } = params
    
    // Decode the participant ID in case it's URL encoded
    const decodedParticipantId = decodeURIComponent(participantId)
    const searchParams = url.searchParams

    // Check if sessions should be included
    const includeSessions = searchParams.get('includeSessions') === 'true'

    // Get participant details
    const participant = await getParticipant(user.id, decodedParticipantId)

    let response: any = participant

    if (includeSessions) {
      const { pageSize, pageToken } = validatePaginationParams(searchParams)
      const filter = searchParams.get('filter') || undefined

      const sessions = await listParticipantSessions(user.id, decodedParticipantId, {
        pageSize,
        pageToken,
        filter
      })

      response = {
        ...participant,
        sessions: sessions.items,
        totalSessions: sessions.totalSize,
        nextPageToken: sessions.nextPageToken
      }
    }

    return formatMeetApiResponse(
      response,
      "Participant details retrieved successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
