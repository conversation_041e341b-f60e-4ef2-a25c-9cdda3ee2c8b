import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API || "AIzaSyAwHr4qa8SLtb7RMqaSRFzgAo6YwpP2Ga0")
const model = genAI.getGenerativeModel({ model: process.env.GEMINI_MODEL || "gemini-2.5-flash-lite-preview-06-17" })

export interface EmailAnalysis {
  id: string
  importance: 'high' | 'medium' | 'low'
  summary: string
  category: string
  actionItems: ActionItem[]
  sentiment: 'positive' | 'neutral' | 'negative'
  priority: number // 1-10 scale
}

export interface ActionItem {
  type: 'meeting' | 'task' | 'delivery' | 'reminder' | 'follow-up' | 'bill' | 'payment' | 'deadline' | 'appointment' | 'renewal'
  title: string
  description: string
  dueDate?: Date
  location?: string
  attendees?: string[]
  urgency: 'high' | 'medium' | 'low'
  amount?: string | number // For bills and payments
}

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  context?: {
    emailIds?: string[]
    analysisResults?: EmailAnalysis[]
  }
}

class GeminiEmailService {
  private async encryptData(data: string, userKey: string): Promise<string> {
    // Client-side encryption using user's session key
    const encoder = new TextEncoder()
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(userKey),
      'PBKDF2',
      false,
      ['deriveBits', 'deriveKey']
    )

    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: encoder.encode('email-encryption-salt'),
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    )

    const iv = crypto.getRandomValues(new Uint8Array(12))
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      encoder.encode(data)
    )

    return btoa(String.fromCharCode(...new Uint8Array(iv))) + '|' + btoa(String.fromCharCode(...new Uint8Array(encrypted)))
  }

  private async decryptData(encryptedData: string, userKey: string): Promise<string> {
    const [ivBase64, dataBase64] = encryptedData.split('|')
    const iv = new Uint8Array(atob(ivBase64).split('').map(c => c.charCodeAt(0)))
    const data = new Uint8Array(atob(dataBase64).split('').map(c => c.charCodeAt(0)))

    const encoder = new TextEncoder()
    const keyMaterial = await crypto.subtle.importKey(
      'raw',
      encoder.encode(userKey),
      'PBKDF2',
      false,
      ['deriveBits', 'deriveKey']
    )

    const key = await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: encoder.encode('email-encryption-salt'),
        iterations: 100000,
        hash: 'SHA-256'
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    )

    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      key,
      data
    )

    return new TextDecoder().decode(decrypted)
  }

  async analyzeEmails(emails: any[], userPrompt?: string): Promise<EmailAnalysis[]> {
    const analysisPrompt = `
    Analyze the following emails and provide a structured response for each email with importance, summary, category, and action items.
    
    ${userPrompt ? `User Request: ${userPrompt}` : 'Focus on identifying important emails and extracting actionable items.'}
    
    For each email, provide:
    1. Importance level (high/medium/low)
    2. Brief but meaningful summary (2-3 sentences)
    3. Category (work, personal, promotional, bills, social, etc.)
    4. Action items (meetings, tasks, deliveries, reminders, follow-ups)
    5. Sentiment analysis
    6. Priority score (1-10)
    
    Extract specific dates, times, locations, and attendees for any meetings or events.
    
    Emails to analyze:
    ${emails.map((email, index) => `
    Email ${index + 1}:
    From: ${email.from}
    Subject: ${email.subject}
    Date: ${email.date}
    Content: ${email.body?.substring(0, 1000)}...
    `).join('\n')}
    
    Respond in JSON format as an array of EmailAnalysis objects.
    `

    try {
      const result = await model.generateContent(analysisPrompt)
      const response = await result.response
      const text = response.text()
      
      // Parse JSON response
      const jsonMatch = text.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const analysisData = JSON.parse(jsonMatch[0])
        return analysisData.map((analysis: any, index: number) => ({
          id: emails[index]?.id || `email-${index}`,
          importance: analysis.importance || 'medium',
          summary: analysis.summary || 'No summary available',
          category: analysis.category || 'general',
          actionItems: analysis.actionItems?.map((item: any) => ({
            type: item.type || 'task',
            title: item.title || '',
            description: item.description || '',
            dueDate: item.dueDate ? new Date(item.dueDate) : undefined,
            location: item.location,
            attendees: item.attendees || [],
            urgency: item.urgency || 'medium'
          })) || [],
          sentiment: analysis.sentiment || 'neutral',
          priority: analysis.priority || 5
        }))
      }
      
      throw new Error('Invalid response format')
    } catch (error) {
      console.error('Error analyzing emails:', error)
      throw error
    }
  }

  async chatWithAI(messages: ChatMessage[], context?: any): Promise<string> {
    const conversationHistory = messages.map(msg => 
      `${msg.role}: ${msg.content}`
    ).join('\n')

    const latestUserMessage = messages.filter(m => m.role === 'user').pop()?.content || ''

    // Check if user wants to add something to calendar
    const calendarKeywords = /\b(add|schedule|calendar|remind|meeting|appointment|bill|due|deadline|event)\b/gi
    const isCalendarRequest = calendarKeywords.test(latestUserMessage)

    const chatPrompt = `
    You are an intelligent email assistant with calendar integration capabilities. Help the user manage their emails, extract insights, and organize tasks.
    
    IMPORTANT: When users ask to add something to their calendar, respond with clear instructions and mention that they can use the calendar integration feature.
    
    Conversation history:
    ${conversationHistory}
    
    ${context ? `Email context available: ${context.emails?.length || 0} emails` : ''}
    
    ${isCalendarRequest ? `
    CALENDAR REQUEST DETECTED: The user wants to add something to their calendar. 
    - Extract specific details like dates, times, titles, and descriptions
    - Provide a clear summary of what should be added
    - Mention that they can use the "Add to Calendar" feature
    ` : ''}
    
    Provide helpful, concise responses. Use **bold** for important information and *italics* for emphasis.
    Include relevant information extracted from emails when available.
    Support regex patterns in responses for better formatting.
    `

    try {
      const result = await model.generateContent(chatPrompt)
      const response = await result.response
      let responseText = response.text()

      // Apply regex formatting for better responses
      responseText = this.formatChatResponse(responseText)
      
      return responseText
    } catch (error) {
      console.error('Error in AI chat:', error)
      throw error
    }
  }

  private formatChatResponse(text: string): string {
    // Apply regex patterns for better formatting
    return text
      // Convert **text** to HTML bold
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Convert *text* to HTML italic
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Format email addresses
      .replace(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g, '<code>$1</code>')
      // Format dates (MM/DD/YYYY, DD-MM-YYYY, etc.)
      .replace(/(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/g, '<strong>$1</strong>')
      // Format times (HH:MM AM/PM)
      .replace(/(\d{1,2}:\d{2}\s*(AM|PM|am|pm))/g, '<strong>$1</strong>')
      // Format currency
      .replace(/(\$\d+(?:\.\d{2})?)/g, '<strong>$1</strong>')
      // Format phone numbers
      .replace(/(\(\d{3}\)\s*\d{3}-\d{4}|\d{3}-\d{3}-\d{4})/g, '<code>$1</code>')
      // Convert line breaks to proper HTML
      .replace(/\n/g, '<br>')
  }

  async extractCalendarItemsFromMessage(message: string, emailContext?: any[]): Promise<ActionItem[]> {
    const extractPrompt = `
    Extract calendar items (meetings, bills, deadlines, appointments, tasks) from this user message:
    "${message}"
    
    ${emailContext ? `
    Email context:
    ${emailContext.slice(0, 3).map(email => `
    From: ${email.from}
    Subject: ${email.subject}
    Date: ${email.date}
    Content: ${email.body?.substring(0, 300)}...
    `).join('\n')}
    ` : ''}
    
    IMPORTANT: Focus on extracting specific due dates and important calendar information:
    - Bills with due dates (electricity, water, phone, internet, rent, etc.)
    - Appointment dates and times
    - Meeting schedules
    - Deadline dates for tasks or projects
    - Payment due dates
    - Renewal dates for subscriptions/services
    - Important events with specific dates
    
    Look for date patterns like:
    - "due on January 15th"
    - "expires on 12/25/2024"
    - "meeting on Monday at 3 PM"
    - "bill due next Friday"
    - "appointment tomorrow at 2:30"
    - "deadline is March 1st"
    
    Extract and return JSON array of calendar items with:
    - type: "bill", "meeting", "task", "deadline", "appointment", "payment", "renewal"
    - title: clear, descriptive title (e.g., "Electricity Bill Payment", "Doctor Appointment")
    - description: detailed description including amount if it's a bill
    - dueDate: ISO date string, be smart about relative dates (tomorrow, next week, etc.)
    - amount: if it's a bill or payment, extract the amount
    - location: if mentioned
    - urgency: "high" for overdue or due soon, "medium" for this week, "low" for later
    
    Focus on bills, payments, deadlines, meetings, and appointments.
    `

    try {
      const result = await model.generateContent(extractPrompt)
      const response = await result.response
      const text = response.text()
      
      // Parse JSON response
      const jsonMatch = text.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const items = JSON.parse(jsonMatch[0])
        return items.map((item: any) => {
          // Parse due date intelligently
          let dueDate = new Date()
          if (item.dueDate) {
            try {
              dueDate = new Date(item.dueDate)
              if (isNaN(dueDate.getTime())) {
                // Fallback to smart date parsing
                dueDate = this.parseSmartDate(item.dueDate)
              }
            } catch {
              dueDate = this.parseSmartDate(item.dueDate)
            }
          }

          return {
            type: item.type || 'task',
            title: item.title || 'Untitled',
            description: item.description || '',
            dueDate: dueDate,
            location: item.location,
            attendees: item.attendees || [],
            urgency: item.urgency || 'medium',
            amount: item.amount // Additional field for bill amounts
          }
        })
      }
      
      return []
    } catch (error) {
      console.error('Error extracting calendar items:', error)
      return []
    }
  }

  private parseSmartDate(dateString: string): Date {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    // Handle relative dates
    if (dateString.toLowerCase().includes('tomorrow')) {
      return new Date(today.getTime() + 24 * 60 * 60 * 1000)
    }
    
    if (dateString.toLowerCase().includes('next week')) {
      return new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
    }
    
    if (dateString.toLowerCase().includes('next month')) {
      const nextMonth = new Date(today)
      nextMonth.setMonth(nextMonth.getMonth() + 1)
      return nextMonth
    }
    
    // Handle "this Friday", "next Monday", etc.
    const dayPattern = /(this|next)\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)/i
    const dayMatch = dateString.match(dayPattern)
    if (dayMatch) {
      const daysOfWeek = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      const targetDay = daysOfWeek.indexOf(dayMatch[2].toLowerCase())
      const isNext = dayMatch[1].toLowerCase() === 'next'
      
      const date = new Date(today)
      const currentDay = date.getDay()
      let daysUntilTarget = targetDay - currentDay
      
      if (daysUntilTarget <= 0 || isNext) {
        daysUntilTarget += 7
      }
      
      date.setDate(date.getDate() + daysUntilTarget)
      return date
    }
    
    // Try parsing as regular date
    try {
      return new Date(dateString)
    } catch {
      // Default to tomorrow if all else fails
      return new Date(today.getTime() + 24 * 60 * 60 * 1000)
    }
  }

  async extractDueDatesFromEmails(emails: any[]): Promise<ActionItem[]> {
    const allItems: ActionItem[] = []
    
    for (const email of emails.slice(0, 5)) { // Limit to 5 emails for performance
      const emailText = `
        Subject: ${email.subject}
        From: ${email.from}
        Content: ${email.body || email.snippet}
      `
      
      // Look for bill-related keywords
      const billKeywords = /\b(bill|invoice|payment|due|amount|charges|balance|statement)\b/i
      const dueDateKeywords = /\b(due|expires|deadline|payment.*date|pay.*by)\b/i
      
      if (billKeywords.test(emailText) || dueDateKeywords.test(emailText)) {
        const items = await this.extractCalendarItemsFromMessage(
          `Extract any bills, payments, or due dates from this email`,
          [email]
        )
        allItems.push(...items)
      }
    }
    
    return allItems
  }

  async extractTasksFromText(text: string): Promise<ActionItem[]> {
    const taskPrompt = `
    Extract actionable tasks, meetings, and important dates from this text:
    
    "${text}"
    
    Return a JSON array of ActionItem objects with:
    - type (meeting, task, delivery, reminder, follow-up)
    - title
    - description
    - dueDate (if mentioned)
    - location (if mentioned)
    - attendees (if mentioned)
    - urgency (high, medium, low)
    `

    try {
      const result = await model.generateContent(taskPrompt)
      const response = await result.response
      const text = response.text()
      
      const jsonMatch = text.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const tasks = JSON.parse(jsonMatch[0])
        return tasks.map((task: any) => ({
          type: task.type || 'task',
          title: task.title || '',
          description: task.description || '',
          dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
          location: task.location,
          attendees: task.attendees || [],
          urgency: task.urgency || 'medium'
        }))
      }
      
      return []
    } catch (error) {
      console.error('Error extracting tasks:', error)
      return []
    }
  }

  // Calendar integration methods
  async convertActionItemToCalendarEvent(actionItem: ActionItem): Promise<any> {
    return {
      title: actionItem.title,
      description: actionItem.description,
      startDate: actionItem.dueDate || new Date(),
      endDate: actionItem.type === 'meeting' && actionItem.dueDate 
        ? new Date(actionItem.dueDate.getTime() + 60 * 60 * 1000) // 1 hour default
        : undefined,
      isAllDay: !actionItem.dueDate,
      location: actionItem.location,
      attendees: actionItem.attendees || [],
      importance: actionItem.urgency === 'high' ? 'high' : actionItem.urgency === 'low' ? 'low' : 'medium',
      category: actionItem.type === 'meeting' ? 'meeting' : actionItem.type === 'task' ? 'task' : actionItem.type,
      urgency: actionItem.urgency,
      status: 'pending',
      sourceType: 'ai_generated',
      reminders: actionItem.urgency === 'high' ? [15, 60] : [15] // minutes before
    }
  }

  async generateCalendarEventsFromEmails(emails: any[]): Promise<any[]> {
    const allActionItems: (ActionItem & { sourceEmailId: string })[] = []
    
    // Extract action items from all emails
    for (const email of emails) {
      const emailText = `
        Subject: ${email.subject}
        From: ${email.from}
        Date: ${email.date}
        Content: ${email.body || email.snippet}
      `
      
      const actionItems = await this.extractTasksFromText(emailText)
      allActionItems.push(...actionItems.map(item => ({ ...item, sourceEmailId: email.id })))
    }

    // Convert to calendar events
    const calendarEvents = await Promise.all(
      allActionItems.map(async (item) => {
        const event = await this.convertActionItemToCalendarEvent(item)
        return { ...event, sourceEmailId: item.sourceEmailId }
      })
    )

    return calendarEvents
  }
}

export const geminiService = new GeminiEmailService() 