import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from "next-auth/next"
import { authOptions } from '@/lib/auth'
import { geminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
  
    const session = await getServerSession(authOptions) as any
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { message, emailContext } = await request.json()

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 })
    }

    // Extract calendar items from the message
    const calendarItems = await geminiService.extractCalendarItemsFromMessage(message, emailContext)

    return NextResponse.json({
      items: calendarItems,
      success: true
    })

  } catch (error) {
    console.error('Calendar extraction error:', error)
    return NextResponse.json(
      { error: 'Failed to extract calendar items' },
      { status: 500 }
    )
  }
} 