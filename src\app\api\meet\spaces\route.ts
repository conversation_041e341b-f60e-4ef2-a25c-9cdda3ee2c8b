import { NextRequest, NextResponse } from "next/server"
import {
  authenticateMeetU<PERSON>,
  handleMeetApiError,
  formatMeetApiResponse,
  createMeetingSpace
} from "@/lib/meet"
import { Meet<PERSON>acheHelper } from '@/lib/cache/meetCacheHelper'

/**
 * GET /api/meet/spaces - List meeting spaces (placeholder)
 * Note: Google Meet API doesn't support listing spaces directly
 */
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    // Since the Meet API doesn't support listing spaces,
    // we return a message explaining this limitation
    return formatMeetApiResponse(
      [],
      "Google Meet API doesn't support listing meeting spaces. Create spaces individually or store references in your database."
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}

/**
 * POST /api/meet/spaces - Create a new meeting space
 */
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateMeetUser(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { user } = authResult.data
    const body = await request.json()

    // Validate request body
    const config = body.config || {}
    
    // Validate access type
    if (config.accessType && !['OPEN', 'TRUSTED', 'RESTRICTED'].includes(config.accessType)) {
      return NextResponse.json({
        error: "Invalid accessType. Must be one of: OPEN, TRUSTED, RESTRICTED"
      }, { status: 400 })
    }

    // Validate entry point access
    if (config.entryPointAccess && !['ALL', 'CREATOR_APP_ONLY'].includes(config.entryPointAccess)) {
      return NextResponse.json({
        error: "Invalid entryPointAccess. Must be one of: ALL, CREATOR_APP_ONLY"
      }, { status: 400 })
    }

    // Create the meeting space
    const meetingSpace = await createMeetingSpace(user.id, config)

    // Cache the created meeting space
    if (meetingSpace && meetingSpace.name) {
      MeetCacheHelper.cacheMeetingSpace(meetingSpace.name, meetingSpace)
    }

    return formatMeetApiResponse(
      meetingSpace,
      "Meeting space created successfully"
    )
  } catch (error) {
    return handleMeetApiError(error)
  }
}
