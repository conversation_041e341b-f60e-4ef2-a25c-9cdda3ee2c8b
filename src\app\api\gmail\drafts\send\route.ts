import { NextRequest } from "next/server"
import {
  sendDraft,
  withGmail<PERSON><PERSON>
} from "@/lib/gmail"
import { validateRequestBody } from "@/lib/validation/utils"
import { draftActionSchema } from "@/lib/validation/schemas"
import { startPerformanceMetric, endPerformanceMetric } from "@/lib/performance/monitor"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    // Start performance monitoring
    startPerformanceMetric('gmail-draft-send', { userId: user.id })

    // Validate request body using Zod
    const validation = await validateRequestBody(request, draftActionSchema)
    if (!validation.success) {
      endPerformanceMetric('gmail-draft-send')
      throw new Error(`Invalid draft send data: ${validation.error}`)
    }

    const { draftId } = validation.data

    // Send draft
    const result = await sendDraft(user.id, draftId)

    if (!result.success) {
      endPerformanceMetric('gmail-draft-send')
      throw new Error(result.error || 'Failed to send draft')
    }

    // End performance monitoring
    endPerformanceMetric('gmail-draft-send')

    return {
      success: true,
      draftId: result.draftId,
      messageId: result.messageId,
      message: 'Draft sent successfully'
    }
  })
}
