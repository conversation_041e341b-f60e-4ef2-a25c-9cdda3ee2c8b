import { NextRequest } from "next/server"
import { 
  sendDraft, 
  with<PERSON><PERSON><PERSON><PERSON>, 
  validateRequiredFields 
} from "@/lib/gmail"

export async function POST(request: NextRequest) {
  return withGmailAuth(request, async ({ user }) => {
    const body = await request.json()
    
    // Validate required fields
    const validation = validateRequiredFields(body, ['draftId'])
    if (!validation.isValid) {
      throw new Error(`Missing required fields: ${validation.missingFields?.join(', ')}`)
    }

    const { draftId } = body

    // Send draft
    const result = await sendDraft(user.id, draftId)

    if (!result.success) {
      throw new Error(result.error || 'Failed to send draft')
    }

    return {
      success: true,
      draftId: result.draftId,
      messageId: result.messageId,
      message: 'Draft sent successfully'
    }
  })
}
